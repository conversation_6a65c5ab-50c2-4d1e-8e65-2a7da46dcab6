# must be unique in a given SonarQube instance
sonar.projectKey=mstrat:m72ui

# this is the name and version displayed in the SonarQube UI. Was mandatory prior to SonarQube 6.1.
sonar.projectName=M72 UI

# Path is relative to the sonar-project.properties file. Replace "\" by "/" on Windows.
# This property is optional if sonar.modules is set.
sonar.sources=src
sonar.tests=src

# Encoding of the source code. Default is default system encoding
sonar.sourceEncoding=UTF-8

# Exclude some files from coverage report
sonar.coverage.exclusions=**/*.test.tsx,**/*.test.ts,__mocks__/**,src/main.tsx
sonar.exclusions=**/*.test.tsx,**/*.test.ts,src/core/utils/debug.ts,src/main.tsx,__mocks__/core-clib/shared-types/**/*,__mocks__/**,src/main.tsx

# Include only these files for tests
sonar.test.inclusions=**/*.test.tsx,**/*.test.ts

# Specifies the .ts, .tsx files to be analyzed
sonar.inclusions=**/*.ts,**/*.tsx

# Define the TypeScript plugin
sonar.typescript.lcov.reportPaths=./coverage/lcov.info

# Path to test execution report
sonar.testExecutionReportPaths=sonar-report.xml
