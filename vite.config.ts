import react from '@vitejs/plugin-react-swc'
import path from 'node:path'
import { defineConfig } from 'vite'
import tsconfigPaths from 'vite-tsconfig-paths'
import EnvironmentPlugin from 'vite-plugin-environment'

export default defineConfig(() => {
  return {
    // vite config
    plugins: [
      react(),
      tsconfigPaths(),
      EnvironmentPlugin('all', { prefix: 'VITE_' })
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '/@core-clib': path.resolve(__dirname, 'node_modules/@core-clib')
      }
    },
    server: {
      port: 8082
    },
    esbuild: {
      charset: 'ascii'
    },
    build: {
      sourcemap: true,
      rollupOptions: {
        output: {
          manualChunks: {
            react: ['react', 'react-dom'],
            lodash: ['lodash'],
            redux: ['redux', 'react-redux', 'redux-thunk', '@reduxjs/toolkit'],
            dateFns: ['date-fns'],
            agGrid: [
              '@ag-grid-community/react',
              '@ag-grid-community/core',
              '@ag-grid-community/styles',
              '@ag-grid-community/client-side-row-model'
            ],
            agGridEnterprise: ['@ag-grid-enterprise/core']
          }
        }
      }
    }
  }
})
