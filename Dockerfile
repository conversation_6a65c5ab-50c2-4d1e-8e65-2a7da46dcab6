FROM prddocker:80/p72-centos7-nginx-base:1.10.1

# Set default path
ENV PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/share/centrifydc/kerberos/bin"

# Use nginx as the web server
ENV NGINX_CONFIG='/etc/nginx/nginx.conf' 
ENV NGINX_CONF_D='/etc/nginx/conf.d' 

# Remove default config that overrides our specific settings
RUN rm -rf $NGINX_CONFIG && rm -rf $NGINX_CONF_D/*

# Copy files
COPY ./pipeline/nginx.conf $NGINX_CONFIG
COPY ./dist /data/www

RUN mkdir /opt/container
COPY ./pipeline/run.sh /opt/container/run.sh
COPY ./pipeline/ocp-api-client.sh /opt/container/
COPY ./pipeline/krb5.keytab.* /opt/container/
RUN chmod -R 777 /opt/container

# Support running as arbitrary user which belongs to the root group
RUN chmod -R g+rwx /var/run /var/log/nginx /etc/nginx

# Users are not allowed to listen on priviliged ports 
EXPOSE 8080

CMD ["/opt/container/run.sh"]
