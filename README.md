## Getting Started

First, run the development server:

```bash
  yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the
result.

You can start editing the page by modifying `app/page.tsx`. The page
auto-updates as you edit the file.

This project uses
[`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to
automatically optimize and load Inter, a custom Google Font.

## Husky scripts

This project uses [`husky`](https://typicode.github.io/husky/#/) to run scripts
on git hooks. The following hooks are configured:

- `pre-commit`: runs `yarn prettier`

To add a command to a hook or create a new one, use husky add <file> [cmd].

```
npx husky add .husky/pre-commit "npm test"
git add .husky/pre-commit
```

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js
  features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out
[the Next.js GitHub repository](https://github.com/vercel/next.js/) - your
feedback and contributions are welcome!

## Debug re-renders

[Why Did You Render](https://github.com/welldone-software/why-did-you-render) is
installed to help debug unnecessary re-renders. Do not enable it in production!
To enable in local develpment mode set the environment variable
`VITE_ENABLE_WHY_DID_YOU_RENDER=true` in your `.env.local` file.

```bash
VITE_ENABLE_WHY_DID_YOU_RENDER=true
```

---

# CI/CD

## Overview

This project integrates various key components aimed at setting up, deploying,
and managing an environment in the OpenShift Container Platform (OCP). The
documentation below provides an in-depth understanding of how to use and manage
each part of the project.

## Table of Contents

- [CI/CD](#cicd)
  - [Overview](#overview)
  - [Table of Contents](#table-of-contents)
  - [Detailed Explanation](#detailed-explanation)
    - [Understanding Key Variables](#understanding-key-variables)
  - [Key Components](#key-components)
    - [Keytab Environment](#keytab-environment)
    - [What is a Keytab File?](#what-is-a-keytab-file)
    - [Why is it Important in `run.sh`?](#why-is-it-important-in-runsh)
    - [How Does `run.sh` Handle the Keytab File?](#how-does-runsh-handle-the-keytab-file)
    - [Security Considerations](#security-considerations)
  - [Pipeline.json Configuration File](#pipelinejson-configuration-file)
    - [Overview](#overview-1)
    - [Key Fields](#key-fields)
  - [We must keep Best Practices](#we-must-keep-best-practices)
  - [Further Reading](#further-reading)

---

## Detailed Explanation

### Understanding Key Variables

- **OCP_ENV**: Identifies the OCP environment where the script operates.
- **CACHED_KEYTAB**: Optional variable containing a pre-existing keytab file
  location.

---

## Key Components

### Keytab Environment

### What is a Keytab File?

A keytab is a file containing pairs of Kerberos principals and an encrypted copy
of that principal's key. This file allows you to log into various services
without being prompted for a password. Keytab files are commonly used in
automated processes that require authentication but don't have the ability to
interact with the user for password input.

### Why is it Important in `run.sh`?

The `run.sh` script leverages the `CACHED_KEYTAB` environment variable to
determine whether to use an existing keytab file or to fetch a new one. This
approach provides flexibility for different operational scenarios:

1. **Dev/Test Environment**: You might want to reuse a keytab file for
   simplicity.
2. **Production Environment**: A new keytab file could be fetched for each run
   to enhance security.

### How Does `run.sh` Handle the Keytab File?

The script conducts a branching logic based on the `CACHED_KEYTAB` variable.

- **Fetch New Keytab**: If `CACHED_KEYTAB` is not set, the script will attempt
  to fetch a new keytab file by invoking the `ocp-api-client.sh` script up to 5
  times (controlled by the loop with the `until [ $n -ge 5 ]` condition).

  - If successful (`exitCode` is 0), the process breaks out of the loop.
  - If it fails (`exitCode` is not 0), the script sleeps for 30 seconds and
    tries again.

- **Use Cached Keytab**: If `CACHED_KEYTAB` is set, the script simply copies the
  keytab file from `/opt/container/${CACHED_KEYTAB}` to `/etc/krb5.keytab`.

### Security Considerations

Always ensure that the keytab file is securely stored, particularly if you are
caching it. Unsecured keytab files pose a security risk as they can be used to
authenticate illicitly to services.

1. **Initial OCP Environment Check**: Logs the current value of `OCP_ENV`.
2. **Keytab File Handling**: Two distinct pathways based on `CACHED_KEYTAB`.
   - **Fetch New**: If not set, attempts to fetch a new keytab file by invoking
     `ocp-api-client.sh`.
   - **Use Cached**: If set, uses the pre-existing keytab file.
3. **Server Initialization**: Bootstraps the Nginx server if the keytab is
   successfully processed.

---

## Pipeline.json Configuration File

### Overview

The `Pipeline.json` file holds configuration data that CI/CD processes utilize
for setting up and deploying the project within OCP.

### Key Fields

- **app_version**: Version of the application for deployment tracking.
- **team**: Team responsible for the project.
- **project_type**: Type of the project (in this case, Node.js).
- **nodejs_version**: Node.js runtime version.
- **target_ocp_cluster**: OCP cluster targeted for deployment.

---

## We must keep Best Practices

- Keep your `Pipeline.json` file updated to match your project requirements.
- Periodically validate the `run.sh` script to ensure it aligns with any updates
  to OCP or your environment.
- Store sensitive information like keytabs securely.

---

## Further Reading

- [OpenShift Container Platform Documentation](#)
- [Nginx Official Documentation](#)
- [Bash Scripting Tutorial](#)
