# Semantic Versioning Document

## Introduction

This document aims to provide a guide for using semantic versioning in our
project. We follow the MAJOR.MINOR.PATCH structure, where:

- MAJOR: Incremented when incompatible API changes are made.
- MINOR: Incremented when adding backward-compatible functionalities.
- PATCH: Incremented when making backward-compatible bug fixes.

## How to Increment Versions

- Increment the MAJOR version when you make incompatible API changes, such as
  removing or altering existing functionalities that break compatibility with
  previous versions.
- Increment the MINOR version when you add functionalities in a
  backward-compatible manner.
- Increment the PATCH version when you make backward-compatible bug fixes.

## Versioning the Code

In order to propagate the version of our application across our codebase, we leverage the app_version property within our pipeline.json file.
Thanks to the capabilities provided by the Vite builder tool, this file can be easily imported and utilized within our application.
Consequently, updating the application's version is as simple as modifying the app_version value in the pipeline.json file.


Preparing the First Release
Although we've done a lot of work, we haven't yet carried out our first release. To prepare for this, we should follow these steps:

Review all the work we've done and decide what version our first release should be. As a general rule, if we've already added features and fixed bugs, we might consider making our first release as 1.0.0.
Update the VERSION constant in our code to reflect the version of our first release.
Create a release note detailing all the features and bug fixes included in the release.
Version Documentation
For each new version, we create a release note detailing what changes have been included. This documentation is vital for tracking changes in our code over time.

```
