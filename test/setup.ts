import '@testing-library/jest-dom'
import nodeFetch, { Request, Response } from 'node-fetch'
import { ModuleRegistry } from '@ag-grid-community/core'
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model'
import { LicenseManager } from '@ag-grid-enterprise/core'
import { MenuModule } from '@ag-grid-enterprise/menu'

LicenseManager.setLicenseKey(
  'Using_this_AG_Grid_Enterprise_key_( AG-040901 )_in_excess_of_the_licence_granted_is_not_permitted___Please_report_misuse_to_( <EMAIL> )___For_help_with_changing_this_key_please_contact_( <EMAIL> )___( Point72, L.P. )_is_granted_a_( Multiple Applications )_Developer_License_for_( 60 )_Front-End_JavaScript_developers___All_Front-End_JavaScript_developers_need_to_be_licensed_in_addition_to_the_ones_working_with_AG_Grid_Enterprise___This_key_has_not_been_granted_a_Deployment_License_Add-on___This_key_works_with_AG_Grid_Enterprise_versions_released_before_( 2 June 2024 )____[v2]_MTcxNzI4MjgwMDAwMA==0a5ea231b6a953e656193a4331054da6'
)
ModuleRegistry.registerModules([MenuModule])
ModuleRegistry.registerModules([ClientSideRowModelModule])

Object.assign(global, { fetch: nodeFetch, Request, Response })

// set timezone in to get deterministic results in tests
process.env.TZ = 'CET'
