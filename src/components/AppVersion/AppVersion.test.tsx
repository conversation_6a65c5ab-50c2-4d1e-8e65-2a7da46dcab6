import { app_version as version } from '@/../pipeline.json'
import { getReduxWrapper } from '@/core/testing'
import { render, screen } from '@testing-library/react'
import { AppVersion } from './AppVersion'

describe('test App Version component', () => {
  it('should render app version based on pipeline', () => {
    const { wrapper } = getReduxWrapper()
    render(<AppVersion />, {
      wrapper
    })
    expect(screen.getByText(`App Version: ${version}`)).toBeInTheDocument()
  })
  it('should not render app version if footer bar is not visible', () => {
    const { wrapper } = getReduxWrapper({
      users: {
        userPreferences: {
          componentVisibility: {
            isFooterBarVisible: false
          }
        }
      }
    })
    render(<AppVersion />, {
      wrapper
    })
    expect(
      screen.queryByText(`App Version: ${version}`)
    ).not.toBeInTheDocument()
  })
})
