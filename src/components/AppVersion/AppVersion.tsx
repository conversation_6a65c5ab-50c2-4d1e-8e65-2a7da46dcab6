import { app_version as version } from '@/../pipeline.json'
import { userPreferencesSelector } from '@/core/redux/features/users/selectors/userPreferencesSelector'
import { useAppSelect } from '@/core/redux/hooks'
import { FC } from 'react'
import './AppVersion.scss'

export const AppVersion: FC = () => {
  const { componentVisibility } = useAppSelect(userPreferencesSelector)

  if (!componentVisibility?.isFooterBarVisible) {
    return null // This Only hides the version number, but not the component itself (whole Bar)
  }
  return <span className="app-version-footer">{`App Version: ${version}`}</span>
}
