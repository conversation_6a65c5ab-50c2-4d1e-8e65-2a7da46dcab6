import { Button } from '@/components/PresetsDropdown/Button'
import { Menu } from '@/components/PresetsDropdown/Menu'
import { IAction } from '@core-clib/web-components'
import { FC, useState } from 'react'
import styles from './PresetsDropdown.module.scss'

type Props = {
  presets: IAction[]
  onEdit: (item: IAction) => void
  onDelete: (item: IAction) => void
  onSend: (item: IAction) => void
  onMoveUp: (item: IAction) => void
  onMoveDown: (item: IAction) => void
  onCreatePreset: VoidFunction
}

export const PresetsDropdown: FC<Props> = (props) => {
  const {
    presets,
    onSend,
    onDelete,
    onEdit,
    onCreatePreset,
    onMoveDown,
    onMoveUp
  } = props
  const [isOpen, setIsOpen] = useState(false)
  const toggle = () => setIsOpen((prev) => !prev)
  const [selectedItem, setSelectedItem] = useState<IAction | undefined>()
  const selectItem = (item: IAction) => setSelectedItem(item)

  return (
    <div
      className={styles.PresetsDropdown}
      data-testid="PresetsDropdownComponent"
    >
      <Button selectedItem={selectedItem} isOpen={isOpen} toggle={toggle} />
      {isOpen && (
        <Menu
          onSelected={selectItem}
          presets={presets}
          onEdit={onEdit}
          onDelete={onDelete}
          onSend={onSend}
          onCreatePreset={onCreatePreset}
          onMoveUp={onMoveUp}
          onMoveDown={onMoveDown}
        />
      )}
    </div>
  )
}
