import { cleanup, fireEvent, render } from '@testing-library/react'
import { vi } from 'vitest'
import { Button } from './Button'

describe('Button', () => {
  beforeEach(() => {
    cleanup()
    vi.resetAllMocks()
  })

  it('should handle click events', () => {
    const handleClick = vi.fn()

    const { getByTestId } = render(
      <Button toggle={handleClick} isOpen={false} />
    )

    fireEvent.click(getByTestId('PresetDropdownButton'))

    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('should display the label when a selected item is provided', () => {
    const selectedItem = { label: 'Test Label', id: 'test' }

    const { getByText } = render(
      <Button toggle={vi.fn()} isOpen={false} selectedItem={selectedItem} />
    )

    expect(getByText('Test Label')).toBeInTheDocument()
  })

  it('should display "Select Preset" when no selected item is provided', () => {
    const { getByText } = render(<Button toggle={vi.fn()} isOpen={false} />)

    expect(getByText('Select Preset')).toBeInTheDocument()
  })

  it('should render the correct icon based on the isOpen prop', () => {
    const { rerender, getByTestId } = render(
      <Button toggle={vi.fn()} isOpen={false} />
    )

    expect(getByTestId('PresetDropdownButton')).toBeInTheDocument()

    rerender(<Button toggle={vi.fn()} isOpen={true} />)

    expect(getByTestId('PresetDropdownButton')).toBeInTheDocument()
  })
})
