import { IAction } from '@core-clib/web-components'
import { FC } from 'react'
import styles from './Button.module.scss'

type Props = {
  toggle: VoidFunction
  selectedItem?: IAction
  isOpen: boolean
  isChanged?: boolean
}

export const Button: FC<Props> = ({
  selectedItem,
  toggle,
  isOpen,
  isChanged
}) => {
  return (
    <button
      className={styles.Button}
      onClick={toggle}
      data-testid="PresetDropdownButton"
    >
      {isChanged && (
        <div className={styles.action} role="button">
          <i className="ci-floppy-disk" />
        </div>
      )}
      <div>{selectedItem?.label ?? 'Select Preset'}</div>
      <div className={styles.action} role="button">
        <i className={isOpen ? 'ci-arrow-up2' : 'ci-arrow-down2'} />
      </div>
    </button>
  )
}
