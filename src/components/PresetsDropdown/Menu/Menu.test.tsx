import { ReduxWrapper } from '@/core/testing'
import { render, screen, fireEvent, cleanup } from '@testing-library/react'
import { IAction } from '@core-clib/web-components'
import { Menu } from './Menu'
import { vi } from 'vitest'

describe('<Menu />', () => {
  const presets: IAction[] = [
    {
      id: 'preset1',
      label: 'Preset 1',
      group: '1'
    },
    {
      id: 'preset2',
      label: 'Preset 2',
      group: '2'
    }
  ]

  const mockProps = {
    presets,
    onSelected: vi.fn(),
    onCreatePreset: vi.fn(),
    onDelete: vi.fn(),
    onEdit: vi.fn(),
    onSend: vi.fn(),
    onMoveDown: vi.fn(),
    onMoveUp: vi.fn()
  }

  beforeEach(() => {
    cleanup()
    render(<Menu {...mockProps} />, { wrapper: ReduxWrapper() })
  })

  it('renders the component without crashing', () => {
    expect(screen.getByTestId('PresetDropdownMenu')).toBeInTheDocument()
  })

  it('calls the onCreatePreset callback when the add button is clicked', () => {
    fireEvent.click(screen.getByText('Create Preset'))
    expect(mockProps.onCreatePreset).toHaveBeenCalledTimes(1)
  })

  it('renders the provided presets', () => {
    presets.forEach((preset) => {
      expect(screen.getByText(preset.label as string)).toBeInTheDocument()
    })
  })

  it('calls onSelect when a preset is clicked', () => {
    fireEvent.click(screen.getByText(presets[0].label as string))
    expect(mockProps.onSelected).toHaveBeenCalledWith(presets[0])
  })

  it('calls onMoveUp when the move up button of a preset is clicked', () => {
    const firstPresetLabel = presets[0].label ?? ''

    const moveUpButton = screen.getByTestId(
      `PresetsDropdownMoveUpButton-${firstPresetLabel}`
    )

    fireEvent.click(moveUpButton)

    expect(mockProps.onMoveUp).toHaveBeenCalledWith(presets[0])
  })

  it('calls onMoveUp when the move up button of a preset is clicked', () => {
    const firstPresetLabel = presets[0].label ?? ''

    const moveUpButton = screen.getByTestId(
      `PresetsDropdownMoveUpButton-${firstPresetLabel}`
    )

    fireEvent.click(moveUpButton)

    expect(mockProps.onMoveUp).toHaveBeenCalledWith(presets[0])
  })

  it('calls the onSelect method when a preset is clicked', () => {
    const presetLabel = presets[0].label ?? ''

    const presetElement = screen.getByText(presetLabel)
    fireEvent.click(presetElement)

    expect(mockProps.onSelected).toHaveBeenCalledWith(presets[0])
  })

  it('opens the submenu when the cog button is clicked', () => {
    const firstPresetLabel = presets[0].label ?? ''

    const cogButton = screen.getByTestId(
      `PresetsDropdownSubMenuButton-${firstPresetLabel}`
    )

    fireEvent.click(cogButton)
    const submenuElement = screen.getByTestId('PresetsDropdownSubMenu')

    expect(submenuElement).toBeInTheDocument()
  })
})
