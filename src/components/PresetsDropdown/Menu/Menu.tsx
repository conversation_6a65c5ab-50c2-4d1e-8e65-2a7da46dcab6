import { useAppSelect } from '@/core/redux/hooks'
import { IAction } from '@core-clib/web-components'
import classNames from 'classnames'
import { FC } from 'react'
import styles from './Menu.module.scss'
import { MenuItems } from './MenuItems'

export type MenuProps = {
  presets: IAction[]
  onSelected: (item: IAction) => void
  onEdit: (item: IAction) => void
  onDelete: (item: IAction) => void
  onSend: (item: IAction) => void
  onMoveUp: (item: IAction) => void
  onMoveDown: (item: IAction) => void
  onCreatePreset: VoidFunction
}

export const Menu: FC<MenuProps> = (props) => {
  const { onCreatePreset } = props
  const theme = useAppSelect((state) => state.users.theme)

  return (
    <div
      className={classNames(styles.Menu, theme, 'core-clib-selection-list')}
      data-testid="PresetDropdownMenu"
    >
      <div
        key="add-preset"
        role="button"
        className={classNames(styles.item, styles.addAction)}
        onClick={onCreatePreset}
      >
        <div>
          <i className="ci-PlusCircle" />
        </div>
        <div>Create Preset</div>
      </div>
      <MenuItems {...props} />
    </div>
  )
}
