import { render } from '@testing-library/react'
import { MenuItems } from './MenuItems'
import { vi } from 'vitest'

describe('MenuItems', () => {
  const mockPresets = [
    { id: '1', label: 'Test Item 1' },
    { id: '2', label: 'Test Item 2' },
    { id: '3', label: 'Test Item 3' }
  ]

  const mockOnSelect = vi.fn()
  const mockOnMoveUp = vi.fn()
  const mockOnMoveDown = vi.fn()
  const mockOnDelete = vi.fn()
  const mockOnEdit = vi.fn()
  const mockOnSend = vi.fn()

  it('renders without crashing', () => {
    render(
      <MenuItems
        presets={mockPresets}
        onSelected={mockOnSelect}
        onMoveUp={mockOnMoveUp}
        onMoveDown={mockOnMoveDown}
        onDelete={mockOnDelete}
        onEdit={mockOnEdit}
        onSend={mockOnSend}
      />
    )
  })

  it('renders correct number of MenuItem components', () => {
    const { getAllByTestId } = render(
      <MenuItems
        presets={mockPresets}
        onSelected={mockOnSelect}
        onMoveUp={mockOnMoveUp}
        onMoveDown={mockOnMoveDown}
        onDelete={mockOnDelete}
        onEdit={mockOnEdit}
        onSend={mockOnSend}
      />
    )
    const menuItems = getAllByTestId('PresetsDropdownMenuItem')
    expect(menuItems.length).toBe(mockPresets.length)
  })
})
