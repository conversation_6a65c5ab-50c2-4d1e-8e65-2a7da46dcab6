import { FC, useMemo } from 'react'
import { MenuProps } from '../Menu'
import styles from './MenuItems.module.scss'
import { MenuItem } from './MenuItem/MenuItem'

type Props = Pick<
  MenuProps,
  | 'onSelected'
  | 'onMoveDown'
  | 'onMoveUp'
  | 'onDelete'
  | 'onEdit'
  | 'onSend'
  | 'presets'
>

export const MenuItems: FC<Props> = (props) => {
  const { presets } = props

  const menuItems = useMemo(() => {
    let lastGroup: string | undefined

    return presets
      .sort((a, b) => ((a.group ?? 0) > (b.group ?? 0) ? 1 : -1))
      .flatMap((item) => {
        const component = [<MenuItem {...props} item={item} />]

        if (lastGroup !== item.group) {
          component.unshift(
            <div className={styles.group} key={item.group}>
              {item.group}
              <div className={styles.line} />
            </div>
          )
        }

        lastGroup = item.group

        return component
      })
  }, [presets])

  return <>{menuItems}</>
}
