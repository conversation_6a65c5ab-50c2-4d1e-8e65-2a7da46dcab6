import { render, fireEvent } from '@testing-library/react'
import { MenuItem } from './MenuItem'
import { vi } from 'vitest'

describe('MenuItem', () => {
  const mockItem = {
    id: '1',
    label: 'Test Item'
  }

  const mockOnSelect = vi.fn()
  const mockOnMoveUp = vi.fn()
  const mockOnMoveDown = vi.fn()
  const mockOnDelete = vi.fn()
  const mockOnEdit = vi.fn()
  const mockOnSend = vi.fn()

  it('renders without crashing', () => {
    render(
      <MenuItem
        item={mockItem}
        onSelected={mockOnSelect}
        onMoveUp={mockOnMoveUp}
        onMoveDown={mockOnMoveDown}
        onDelete={mockOnDelete}
        onEdit={mockOnEdit}
        onSend={mockOnSend}
      />
    )
  })

  it('calls onSelect when clicked', () => {
    const { getByRole } = render(
      <MenuItem
        item={mockItem}
        onSelected={mockOnSelect}
        onMoveUp={mockOnMoveUp}
        onMoveDown={mockOnMoveDown}
        onDelete={mockOnDelete}
        onEdit={mockOnEdit}
        onSend={mockOnSend}
      />
    )

    fireEvent.click(getByRole('button', { name: mockItem.label }))
    expect(mockOnSelect).toHaveBeenCalledWith(mockItem)
  })

  it('toggles submenu when submenu button is clicked', () => {
    const { getByTestId } = render(
      <MenuItem
        item={mockItem}
        onSelected={mockOnSelect}
        onMoveUp={mockOnMoveUp}
        onMoveDown={mockOnMoveDown}
        onDelete={mockOnDelete}
        onEdit={mockOnEdit}
        onSend={mockOnSend}
      />
    )

    const submenuButton = getByTestId(
      `PresetsDropdownSubMenuButton-${mockItem.label}`
    )

    fireEvent.click(submenuButton)

    const submenu = getByTestId('PresetsDropdownSubMenu')
    expect(submenu).toBeInTheDocument()

    fireEvent.click(submenuButton)
    expect(submenu).not.toBeInTheDocument()
  })
})
