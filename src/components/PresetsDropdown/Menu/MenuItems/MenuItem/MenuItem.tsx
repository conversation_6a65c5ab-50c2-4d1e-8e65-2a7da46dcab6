import classNames from 'classnames'
import { FC, useState } from 'react'
import { Submenu } from './Submenu'
import { MenuProps } from '../../Menu'
import { IAction } from '@core-clib/web-components'
import styles from './MenuItem.module.scss'

type Props = Pick<
  MenuProps,
  'onSelected' | 'onMoveDown' | 'onMoveUp' | 'onDelete' | 'onEdit' | 'onSend'
> & { item: IAction }

export const MenuItem: FC<Props> = ({
  item,
  onSelected,
  onMoveDown,
  onMoveUp,
  onDelete,
  onEdit,
  onSend
}) => {
  const [openedSubmenuId, setOpenedSubmenuId] = useState<string | null>()

  const toggleSubmenu = (id: string | null) => {
    if (id === null || openedSubmenuId === id) {
      setOpenedSubmenuId(null)
    } else {
      setOpenedSubmenuId(id)
    }
  }

  return (
    <div
      key={item.id}
      onClick={() => onSelected(item)}
      role="button"
      className={styles.item}
      data-testid="PresetsDropdownMenuItem"
    >
      <div className={styles.label}>{item.label}</div>
      <div className={styles.actions}>
        <div
          role="button"
          onClick={() => onMoveUp(item)}
          data-testid={`PresetsDropdownMoveUpButton-${item.label}`}
        >
          <i className="ci-arrow-up2" />
        </div>
        <div
          role="button"
          onClick={() => onMoveDown(item)}
          data-testid={`PresetsDropdownMoveDownButton-${item.label}`}
        >
          <i className="ci-arrow-down2" />
        </div>
        <div
          role="button"
          title="Edit Preset"
          className={classNames({
            [styles.activeSubmenu]: !!openedSubmenuId
          })}
          data-testid={`PresetsDropdownSubMenuButton-${item.label}`}
          onClick={(event) => {
            event.stopPropagation()
            toggleSubmenu(item.id)
          }}
        >
          <i className="ci-cog2" />
        </div>
        <div>
          <i className="ci-star-empty3" />
        </div>
      </div>
      {openedSubmenuId === item.id && (
        <Submenu
          onClose={() => toggleSubmenu(null)}
          onDelete={onDelete}
          onEdit={onEdit}
          onSend={onSend}
          preset={item}
        />
      )}
    </div>
  )
}
