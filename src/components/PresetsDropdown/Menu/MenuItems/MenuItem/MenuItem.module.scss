.item {
  display: flex;
  align-items: center;
  padding: 0 16px;
  height: 26px;
  min-width: 180px;
  max-width: 230px;
  gap: 12px;
  justify-content: space-between;
  cursor: pointer;
  position: relative;

  &.addAction {
    justify-content: flex-start;
    color: var(--font-color);
    gap: 3px;
  }

  .label {
    flex: 0.5;
  }

  .actions {
    display: flex;
    visibility: hidden;
    flex: 0.5;

    & > div {
      width: 20px;
      height: 26px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--font-color);
      cursor: pointer;

      &.activeSubmenu {
        & > i {
          color: var(--gold-3);
        }
      }
    }
  }

  &:hover {
    background: var(--selection-list-hover-background-color);

    .actions {
      visibility: visible;
    }
  }
}
