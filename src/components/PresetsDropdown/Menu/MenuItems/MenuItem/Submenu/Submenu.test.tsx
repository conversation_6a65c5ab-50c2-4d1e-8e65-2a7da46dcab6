import { IAction } from '@core-clib/web-components' // import from the correct location
import { fireEvent, render } from '@testing-library/react'
import { vi } from 'vitest'
import { Submenu } from './Submenu' // import from the actual location

describe('Submenu Component', () => {
  let mockOnClose: VoidFunction
  let mockOnEdit: VoidFunction
  let mockOnDelete: VoidFunction
  let mockOnSend: VoidFunction

  beforeEach(() => {
    mockOnClose = vi.fn()
    mockOnEdit = vi.fn()
    mockOnDelete = vi.fn()
    mockOnSend = vi.fn()
  })

  it('should call the onClick function when a menu item is clicked', () => {
    const menuItem: IAction = {
      id: '1',
      label: 'test'
    }

    const { getByText } = render(
      <Submenu
        onClose={mockOnClose}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onSend={mockOnSend}
        preset={menuItem}
      />
    )

    const editButton = getByText(/Edit/i)
    fireEvent.click(editButton)
    expect(mockOnEdit).toHaveBeenCalledTimes(1)
    expect(mockOnEdit).toHaveBeenCalledWith(menuItem)

    const deleteButton = getByText(/Delete/i)
    fireEvent.click(deleteButton)
    expect(mockOnDelete).toHaveBeenCalledTimes(1)
    expect(mockOnDelete).toHaveBeenCalledWith(menuItem)

    const sendButton = getByText(/Send/i)
    fireEvent.click(sendButton)
    expect(mockOnSend).toHaveBeenCalledTimes(1)
    expect(mockOnSend).toHaveBeenCalledWith(menuItem)
  })
})
