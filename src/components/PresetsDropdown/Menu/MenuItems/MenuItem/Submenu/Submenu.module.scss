.SubMenu {
  position: absolute;
  display: flex;
  background: transparent;
  justify-content: center;
  flex-direction: column;
  background: var(--application-bg);
  padding: 3px 0;
  box-shadow: 2px 2px 12px rgba(0,0,0,0.3);
  left: calc(100% + 3px);

  div {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 3px 12px;

    &:hover {
      background: var(--selection-list-hover-background-color);
    }
  }
}
