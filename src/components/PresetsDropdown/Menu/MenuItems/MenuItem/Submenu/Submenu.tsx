import { IAction } from '@core-clib/web-components'
import classNames from 'classnames'
import { FC, useEffect, useRef } from 'react'
import styles from './Submenu.module.scss'

type Props = {
  onClose: VoidFunction
  onEdit: (item: IAction) => void
  onDelete: (item: IAction) => void
  onSend: (item: IAction) => void
  preset: IAction
}

export const enum SubMenuActions {
  edit = 'edit',
  delete = 'delete',
  send = 'send'
}

export const Submenu: FC<Props> = (props) => {
  const { onClose, onDelete, onEdit, onSend, preset } = props

  const ref = useRef<HTMLDivElement | null>(null)

  const actionsMap = {
    [SubMenuActions.delete]: onDelete,
    [SubMenuActions.edit]: onEdit,
    [SubMenuActions.send]: onSend
  }

  const menuItems = [
    { id: SubMenuActions.edit, label: 'Edit', icon: 'ci-pencil' },
    { id: SubMenuActions.send, label: 'Send', icon: 'ci-share3' },
    { id: SubMenuActions.delete, label: 'Delete', icon: 'ci-bin' }
  ]

  useEffect(() => {
    const container = ref.current

    const onClick = (event: Event) => {
      const element = event.currentTarget as HTMLElement

      if (element === ref.current) {
        return
      }

      const queue = [element.parentElement, ...element.children]

      while (queue.length > 0) {
        const currentElement = queue.shift() as HTMLElement

        if (currentElement === ref.current) {
          return
        }

        queue.push(currentElement.parentElement, ...currentElement.children)
      }

      onClose()
    }

    window.addEventListener('onclick', onClick)
    container?.addEventListener('mouseleave', onClose)

    return () => {
      window.removeEventListener('onclick', onClick)
      container?.removeEventListener('mouseleave', onClose)
    }
  }, [])

  return (
    <div
      ref={ref}
      className={classNames(styles.SubMenu)}
      data-testid="PresetsDropdownSubMenu"
    >
      {menuItems.map((item) => (
        <div
          key={item.id}
          role="button"
          title={item.label}
          onClick={() => actionsMap[item.id as SubMenuActions](preset)}
        >
          {item.icon && <i className={item.icon} />}
          {item.label}
        </div>
      ))}
    </div>
  )
}
