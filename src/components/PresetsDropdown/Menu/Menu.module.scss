.Menu {
  position: absolute;
  display: flex;
  flex-direction: column;
  top: 100%;
  background: var(--application-bg);
  z-index: 2;
  border-radius: 3px;
  box-shadow: 2px 2px 12px rgba(0, 0, 0, 0.3);

  .addAction {
    display: flex;
    gap: 0.5rem;
    padding: 10px;
    cursor: pointer;
    color: var(--font-color);
    height: 26px;
    align-items: center;

    &:hover {
      background: var(--selection-list-hover-background-color);
    }
  }
}
