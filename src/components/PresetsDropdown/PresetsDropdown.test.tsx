import { IAction } from '@core-clib/web-components'
import { render, fireEvent } from '@testing-library/react'
import { PresetsDropdown } from './PresetsDropdown'
import { vi } from 'vitest'
import { ReduxWrapper } from '@/core/testing'

describe('PresetsDropdown', () => {
  const presetsMock: IAction[] = [
    {
      id: '1',
      label: 'Preset 1'
    },
    {
      id: '2',
      label: 'Preset 2'
    },
    {
      id: '3',
      label: 'Preset 3'
    },
    {
      id: '4',
      label: 'Preset 4'
    }
  ]

  const mockFunctions = {
    onSend: vi.fn(),
    onDelete: vi.fn(),
    onEdit: vi.fn(),
    onCreatePreset: vi.fn(),
    onMoveUp: vi.fn(),
    onMoveDown: vi.fn()
  }

  test('renders PresetsDropdown component', () => {
    const { getByTestId } = render(
      <PresetsDropdown
        presets={presetsMock}
        onSend={mockFunctions.onSend}
        onDelete={mockFunctions.onDelete}
        onEdit={mockFunctions.onEdit}
        onCreatePreset={mockFunctions.onCreatePreset}
        onMoveUp={mockFunctions.onMoveUp}
        onMoveDown={mockFunctions.onMoveDown}
      />,
      { wrapper: ReduxWrapper() }
    )

    // Assert that the PresetsDropdown label is rendered
    expect(getByTestId('PresetsDropdownComponent')).toBeInTheDocument()
  })

  test('opens and closes dropdown on button click', () => {
    const { getByTestId, queryByTestId } = render(
      <PresetsDropdown
        presets={presetsMock}
        onSend={mockFunctions.onSend}
        onDelete={mockFunctions.onDelete}
        onEdit={mockFunctions.onEdit}
        onCreatePreset={mockFunctions.onCreatePreset}
        onMoveUp={mockFunctions.onMoveUp}
        onMoveDown={mockFunctions.onMoveDown}
      />,
      { wrapper: ReduxWrapper() }
    )

    const dropdownButton = getByTestId('PresetDropdownButton')

    // Dropdown is initially closed
    expect(queryByTestId('PresetDropdownMenu')).not.toBeInTheDocument()

    // Clicking the button opens the dropdown
    fireEvent.click(dropdownButton)
    expect(queryByTestId('PresetDropdownMenu')).toBeInTheDocument()

    // Clicking the button again closes the dropdown
    fireEvent.click(dropdownButton)
    expect(queryByTestId('PresetDropdownMenu')).not.toBeInTheDocument()
  })
})
