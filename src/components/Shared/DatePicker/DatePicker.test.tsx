import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import { DatePicker } from './DatePicker'

describe('DatePicker', () => {
  it('renders the initial date', () => {
    const initialDate = '2022-01-01T12:00'
    render(<DatePicker initialDate={initialDate} onDateChange={vi.fn()} />)
    const dateElement = screen.getByText(initialDate)
    expect(dateElement).toBeInTheDocument()
  })
})
