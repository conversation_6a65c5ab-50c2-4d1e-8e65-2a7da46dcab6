import { formatDateToISOString, formatDateToLocal } from '@/core/utils/date'
import { Field } from '@core-clib/react'
import { FC, useMemo } from 'react'
import styles from './DatePicker.module.scss'

type Props = {
  initialDate?: string
  onDateChange: (date: string | undefined) => void
}

export const DatePicker: FC<Props> = ({ initialDate, onDateChange }) => {
  const localizedDate = useMemo(
    () => (initialDate ? formatDateToISOString(initialDate) : ''),
    [initialDate]
  )

  return (
    <div className={styles.DatePickerComponent}>
      <div className={styles.dateTimePlug}>
        <span>{initialDate}</span>
        <Field
          minimal={true}
          className="field-container"
          data-testid="field-container"
        >
          <input
            name="field_input_date"
            type="datetime-local"
            placeholder="field_input_date"
            value={localizedDate}
            step="1"
            onChange={(e) => {
              onDateChange(formatDateToLocal(new Date(e.target.value)))
            }}
          />
        </Field>
      </div>
    </div>
  )
}
