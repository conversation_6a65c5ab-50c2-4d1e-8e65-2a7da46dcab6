import { IAction } from '@core-clib/web-components'
import { FC } from 'react'
import styles from './Dropdown.module.scss'

interface DropdownProps {
  options: IAction[]
  selectedOptionId: string
  onSelected: (action: IAction) => void
}

export const Dropdown: FC<DropdownProps> = ({
  onSelected,
  options,
  selectedOptionId
}) => {
  const handleSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedId = event.target.value
    onSelected(options.find((option) => option.id === selectedId) as IAction)
  }

  return (
    <div className="Dropdown">
      <select
        value={selectedOptionId}
        onChange={handleSelectChange}
        title="filter options"
        className={styles.Dropdown}
      >
        {options.map((option) => (
          <option key={option.id} value={option.id}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  )
}
