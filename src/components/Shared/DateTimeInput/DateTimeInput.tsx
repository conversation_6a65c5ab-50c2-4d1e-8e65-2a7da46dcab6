import { useState } from 'react'
import { DateTimeCalendar } from '../DateTimeCalendar'
import styles from './DateTime.module.scss'
import { format } from 'date-fns'

interface DateTimeInputProps {
  dateChangeHandler: (date: Date) => void
  onClear: () => void
  value?: string
  time?: boolean
}

export const DateTimeInput: React.FC<DateTimeInputProps> = ({
  dateChangeHandler,
  onClear,
  value,
  time
}) => {
  const [showCalendar, setShowCalendar] = useState(false)

  const handleDateChange = (inputDate: Date) => {
    const year = inputDate.getFullYear()

    if (year > 1000) {
      dateChangeHandler(inputDate)
    }

    setShowCalendar(false)
  }

  const formatDateTime = (dateString: string | undefined) => {
    if (!dateString) {
      return (
        <>
          <span>mm/dd/yyyy</span>
          <span>{time ? 'hh:mm:ss a' : ''}</span>
        </>
      )
    }

    const date = new Date(dateString)

    const formattedDate = format(date, 'MM/dd/yyyy')
    const formattedTime = format(date, 'hh:mm:ss a')

    return (
      <>
        <span>{formattedDate}</span>
        {time ? <span>{formattedTime}</span> : null}
      </>
    )
  }

  return (
    <>
      <DateTimeCalendar
        isOpen={showCalendar}
        onApply={handleDateChange}
        onSetToday={handleDateChange}
        onCancel={() => setShowCalendar(false)}
        dateTime={value ?? new Date()}
        className={styles.Calendar}
        onClear={onClear}
        time={time}
        clear
      />
      <div
        className={styles.DateTimeField}
        role="button"
        onClick={() => setShowCalendar(true)}
      >
        <div>
          <div data-testid="datetime">{formatDateTime(value)}</div>
          <i className="ci-calendar" />
        </div>
      </div>
    </>
  )
}
