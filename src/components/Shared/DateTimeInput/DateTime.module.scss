.CommonFilterInput {
  width: 100%;
  color: var(--ag-foreground-color, #000);
  border: 1px solid var(--ag-simple-filter-field-border-color, #ccc);
  padding: 0.2rem;
  height: 26px;
  background-color: var(--ag-simple-filter-field-background-color);
  border-color: var(--ag-simple-filter-field-border-color);
  border-width: 1px;
  font-size: 11px;
}

.DateInput {
  @extend .CommonFilterInput;
  flex: 3;
}

.TimeInput {
  @extend .CommonFilterInput;
  flex: 2;
}

.DateTimeField {
  width: 100%;
  border: 1px solid var(--ag-simple-filter-field-border-color, #ccc);
  background-color: var(--ag-simple-filter-field-background-color);
  font-size: 11px;
  border-top: none;

  & > div {
    width: 100%;
    display: flex;
    padding: 2px 4px 2px 8px;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    & > div {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 10px;
    }
  }
}

.Calendar {
  position: fixed;
}
