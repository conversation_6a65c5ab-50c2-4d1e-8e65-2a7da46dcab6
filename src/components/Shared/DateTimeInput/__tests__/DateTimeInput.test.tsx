import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import { describe, it, expect, vi } from 'vitest'
import { DateTimeInput } from '../DateTimeInput'

const normalizeHTML = (html: string) => html.replace(/\s+/g, '')

describe('DateTimeInput', () => {
  it('renders with value prop', () => {
    render(
      <DateTimeInput
        dateChangeHandler={vi.fn()}
        onClear={vi.fn()}
        value="2023-10-01T12:00:00.000Z"
      />
    )

    expect(screen.getByTestId('datetime')).toHaveTextContent('10/01/2023')
  })

  it('has the expected HTML structure', () => {
    render(
      <DateTimeInput
        dateChangeHandler={vi.fn()}
        onClear={vi.fn()}
        value="2023-10-01T12:00:00.000Z"
        time
      />
    )

    const expectedHTML = `
      <div data-testid="datetime">
        <span>10/01/2023</span>
        <span>02:00:00PM</span>
      </div>
    `

    const receivedHTML = screen.getByTestId('datetime').outerHTML

    expect(normalizeHTML(receivedHTML)).toBe(normalizeHTML(expectedHTML))
  })
})
