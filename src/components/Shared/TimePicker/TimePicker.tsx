import { padTimeFigures } from '@/core/utils/date'
import { Field } from '@core-clib/react'
import { ChangeEvent, FC } from 'react'
import styles from './TimePicker.module.scss'

export const TimePicker: FC<{
  date: Date | undefined
  onChangeTime: (event: ChangeEvent<HTMLInputElement>) => void
  error?: string
}> = ({ date, onChangeTime, error }) => {
  const getTimeFromDate = (date: Date | undefined) => {
    if (!date) {
      return undefined
    }

    return `${padTimeFigures(date.getHours())}:${padTimeFigures(
      date.getMinutes()
    )}:${padTimeFigures(date.getSeconds())}`
  }

  return date ? (
    <Field className={styles.container} errorText={error}>
      <input
        type="time"
        value={getTimeFromDate(date)}
        step="2"
        onChange={onChangeTime}
        data-testid="timeInput"
        aria-label="Time picker"
        aria-invalid={!!error}
      />
    </Field>
  ) : null
}
