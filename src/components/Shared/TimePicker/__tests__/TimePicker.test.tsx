import { TimePicker } from '@/components/Shared/TimePicker'
import { render, screen, fireEvent } from '@testing-library/react'
import { vi } from 'vitest'
import '@testing-library/jest-dom'

vi.mock('@/core/utils/date', () => ({
  padTimeFigures: (num: number) => num.toString().padStart(2, '0')
}))

vi.mock('@core-clib/react', () => ({
  Field: ({ children, errorText, className }: any) => (
    <div data-testid="Field" className={className}>
      {errorText && <div data-testid="errorText">{errorText}</div>}
      {children}
    </div>
  )
}))

vi.mock('./TimePicker.module.scss', () => ({
  default: {},
  container: 'container'
}))

describe('TimePicker Component', () => {
  it('should render null when date is undefined', () => {
    const { container } = render(
      <TimePicker date={undefined} onChangeTime={vi.fn()} />
    )
    expect(container.firstChild).toBeNull()
  })

  it('should render input with correct time value when date is provided', () => {
    const date = new Date('2021-01-01T12:34:56')
    render(<TimePicker date={date} onChangeTime={vi.fn()} />)

    const input = screen.getByTestId('timeInput')

    expect(input).toBeInTheDocument()
    expect(input).toHaveValue('12:34:56')
  })

  it('should call onChangeTime when input value changes', () => {
    const date = new Date('2021-01-01T12:34:56')
    const onChangeTime = vi.fn()

    render(<TimePicker date={date} onChangeTime={onChangeTime} />)

    const input = screen.getByTestId('timeInput')

    fireEvent.change(input, { target: { value: '13:45:00' } })

    expect(onChangeTime).toHaveBeenCalled()
    expect(onChangeTime).toHaveBeenCalledWith(expect.any(Object))
  })

  it('should display error text when error prop is provided', () => {
    const date = new Date('2021-01-01T12:34:56')
    const error = 'An error occurred'
    render(<TimePicker date={date} onChangeTime={vi.fn()} error={error} />)

    const errorText = screen.getByTestId('errorText')
    expect(errorText).toBeInTheDocument()
    expect(errorText).toHaveTextContent(error)
  })
})
