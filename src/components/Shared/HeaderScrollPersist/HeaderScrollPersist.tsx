import { appConfig } from '@/core/config'
import { HeaderScroll } from '@core-clib/react'
import { debounce } from 'lodash'
import React, { useState, ReactNode } from 'react'

interface HeaderScrollSavedProps {
  storageKey: string
  children: ReactNode
}

export const HeaderScrollPersist: React.FC<HeaderScrollSavedProps> = ({
  children,
  storageKey
}) => {
  const [storedScrollMargin, setStoredScrollMargin] = useState(
    Number(localStorage.getItem(storageKey)) || 0
  )

  const updateScrollMargin = debounce((scrollMargin: number) => {
    if (storedScrollMargin !== scrollMargin) {
      localStorage.setItem(storageKey, scrollMargin.toString())
      setStoredScrollMargin(scrollMargin)
    }
  }, appConfig.defaultDebounceTimeMs)

  return (
    <div id="topheader-summary">
      <HeaderScroll
        scrollMargin={storedScrollMargin}
        onScrollMarginChanged={updateScrollMargin}
      >
        {children}
      </HeaderScroll>
    </div>
  )
}
