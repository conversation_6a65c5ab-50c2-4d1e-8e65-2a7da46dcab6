.container {
  width: 78%;
  padding-bottom: 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;

  & > div {
    display: flex;
    gap: 5px;

    core-clib-button[intent='success'].applyButton {
      --background-color: var(--core-green);
    }
  }
}

.button {
  cursor: pointer;
  padding: 3px 10px;

  &.secondary {
    background: transparent;
    border: 1px solid var(--mint-5);
    color: var(--mint-5);
    border-radius: 4px;

    &:disabled {
      color: var(--gray-3);
      border: 1px solid var(--gray-5);
    }
  }

  &.primary {
    background: var(--mint-5);
    color: var(--white);
    border-radius: 4px;
    border: 1px solid var(--mint-5);

    &:disabled {
      background: var(--gray-5);
      color: var(--gray-3);
      border: 1px solid var(--gray-5);
    }
  }
}
