import { Buttons } from '@/components/Shared/DateTimeCalendar/Buttons'
import { render, screen, fireEvent } from '@testing-library/react'
import { vi } from 'vitest'
import '@testing-library/jest-dom'

vi.mock('./Buttons.module.scss', () => ({
  default: {},
  container: 'container'
}))

vi.mock('@core-clib/react', () => {
  const actualModule = vi.importActual('@core-clib/react')
  return {
    ...actualModule,
    Button: ({ onClick, text, disabled, className, intent }: any) => (
      <button
        data-testid={`button-${text}`}
        onClick={onClick}
        disabled={disabled}
        className={className}
        data-intent={intent}
      >
        {text}
      </button>
    ),
    TooltipWrapper: ({ text, children }: any) => (
      <div data-testid="TooltipWrapper" title={text}>
        {children}
      </div>
    )
  }
})

describe('Buttons Component', () => {
  const onClickApply = vi.fn()
  const onClickCancel = vi.fn()
  const onClickSetToday = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders all buttons', () => {
    render(
      <Buttons
        date={new Date()}
        onClickApply={onClickApply}
        onClickCancel={onClickCancel}
        onClickSetToday={onClickSetToday}
      />
    )

    expect(screen.getByTestId('button-Apply')).toBeInTheDocument()
    expect(screen.getByTestId('button-Cancel')).toBeInTheDocument()
    expect(screen.getByTestId('button-Now')).toBeInTheDocument()
  })

  it('disables Apply button when date is undefined', () => {
    render(
      <Buttons
        date={undefined}
        onClickApply={onClickApply}
        onClickCancel={onClickCancel}
        onClickSetToday={onClickSetToday}
      />
    )

    const applyButton = screen.getByTestId('button-Apply')
    expect(applyButton).toBeDisabled()
  })

  it('disables Apply button when date is in the future', () => {
    const futureDate = new Date(Date.now() + 1000 * 60 * 60 * 24) // 1 day ahead
    render(
      <Buttons
        date={futureDate}
        onClickApply={onClickApply}
        onClickCancel={onClickCancel}
        onClickSetToday={onClickSetToday}
      />
    )

    const applyButton = screen.getByTestId('button-Apply')
    expect(applyButton).toBeDisabled()
  })

  it('enables Apply button when date is in the past or present', () => {
    const pastDate = new Date(Date.now() - 1000 * 60 * 60 * 24) // 1 day before
    render(
      <Buttons
        date={pastDate}
        onClickApply={onClickApply}
        onClickCancel={onClickCancel}
        onClickSetToday={onClickSetToday}
      />
    )

    const applyButton = screen.getByTestId('button-Apply')
    expect(applyButton).toBeEnabled()
  })

  it('calls onClickApply when Apply button is clicked', () => {
    const pastDate = new Date()
    render(
      <Buttons
        date={pastDate}
        onClickApply={onClickApply}
        onClickCancel={onClickCancel}
        onClickSetToday={onClickSetToday}
      />
    )

    const applyButton = screen.getByTestId('button-Apply')
    fireEvent.click(applyButton)

    expect(onClickApply).toHaveBeenCalledTimes(1)
  })

  it('does not call onClickApply when Apply button is disabled', () => {
    render(
      <Buttons
        date={undefined}
        onClickApply={onClickApply}
        onClickCancel={onClickCancel}
        onClickSetToday={onClickSetToday}
      />
    )

    const applyButton = screen.getByTestId('button-Apply')
    fireEvent.click(applyButton)

    expect(onClickApply).not.toHaveBeenCalled()
  })

  it('calls onClickCancel when Cancel button is clicked', () => {
    render(
      <Buttons
        date={new Date()}
        onClickApply={onClickApply}
        onClickCancel={onClickCancel}
        onClickSetToday={onClickSetToday}
      />
    )

    const cancelButton = screen.getByTestId('button-Cancel')
    fireEvent.click(cancelButton)

    expect(onClickCancel).toHaveBeenCalledTimes(1)
  })

  it('calls onClickSetToday when Now button is clicked', () => {
    render(
      <Buttons
        date={new Date()}
        onClickApply={onClickApply}
        onClickCancel={onClickCancel}
        onClickSetToday={onClickSetToday}
      />
    )

    const nowButton = screen.getByTestId('button-Now')
    fireEvent.click(nowButton)

    expect(onClickSetToday).toHaveBeenCalledTimes(1)
  })

  it('wraps Apply button with TooltipWrapper when disabled due to future date', () => {
    const futureDate = new Date(Date.now() + 1000 * 60 * 60 * 24)
    render(
      <Buttons
        date={futureDate}
        onClickApply={onClickApply}
        onClickCancel={onClickCancel}
        onClickSetToday={onClickSetToday}
      />
    )

    const tooltipWrapper = screen.getByTestId('TooltipWrapper')
    expect(tooltipWrapper).toBeInTheDocument()
    expect(tooltipWrapper).toHaveAttribute(
      'title',
      'Selecting dates in the future is prohibited!'
    )
  })

  it('does not wrap Apply button with TooltipWrapper when enabled', () => {
    render(
      <Buttons
        date={new Date()}
        onClickApply={onClickApply}
        onClickCancel={onClickCancel}
        onClickSetToday={onClickSetToday}
      />
    )

    expect(screen.queryByTestId('TooltipWrapper')).not.toBeInTheDocument()
  })
})
