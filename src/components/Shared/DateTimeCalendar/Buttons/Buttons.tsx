import { TooltipWrapper } from '@core-clib/react'
import { FC, PropsWithChildren } from 'react'
import styles from './Buttons.module.scss'
import classNames from 'classnames'

type Props = {
  date: Date | undefined
  clear?: boolean
  futureSelect?: boolean
  onClickApply: () => void
  onClickCancel: () => void
  onClickSetToday: () => void
  onClickClear?: () => void
}

export const Buttons: FC<Props> = ({
  date,
  clear,
  onClickSetToday,
  onClickCancel,
  onClickApply,
  onClickClear,
  futureSelect
}) => {
  const isApplyButtonBlocked =
    !date || (!futureSelect && Date.now() < date.getTime())

  return (
    <div className={styles.container}>
      <div>
        <ApplyButtonHOC isApplyButtonBlocked={isApplyButtonBlocked}>
          <button
            aria-label={
              isApplyButtonBlocked
                ? 'Apply button (disabled: future date selected)'
                : 'Apply button'
            }
            className={classNames(styles.button, styles.primary)}
            disabled={isApplyButtonBlocked}
            onClick={onClickApply}
            data-testid="button-Apply"
          >
            Apply
          </button>
        </ApplyButtonHOC>
        <button
          onClick={clear ? onClickClear : onClickCancel}
          className={classNames(styles.button, styles.secondary)}
          data-testid={clear ? 'button-Clear' : 'button-Cancel'}
        >
          {clear ? 'Clear' : 'Cancel'}
        </button>
      </div>
      <button
        onClick={onClickSetToday}
        className={classNames(styles.button, styles.secondary)}
        data-testid="button-Now"
      >
        Now
      </button>
    </div>
  )
}

function ApplyButtonHOC({
  children,
  isApplyButtonBlocked
}: PropsWithChildren & { isApplyButtonBlocked: boolean }) {
  return isApplyButtonBlocked ? (
    <TooltipWrapper text="Selecting dates in the future is prohibited!">
      {children}
    </TooltipWrapper>
  ) : (
    children
  )
}
