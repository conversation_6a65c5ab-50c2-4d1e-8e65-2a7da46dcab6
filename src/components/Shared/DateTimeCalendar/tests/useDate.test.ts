import { describe, it, expect } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useDate } from '../useDate'

describe('useDate', () => {
  it('Given initial date with time, When setDate is called, Then time is preserved', () => {
    const initialDate = new Date(2023, 0, 1, 10, 30, 0)
    const { result } = renderHook(() => useDate(initialDate, true))

    act(() => {
      result.current.setDate(new Date(2023, 0, 2))
    })

    expect(result.current.date?.getFullYear()).toBe(2023)
    expect(result.current.date?.getMonth()).toBe(0)
    expect(result.current.date?.getDate()).toBe(2)
    expect(result.current.date?.getHours()).toBe(10)
    expect(result.current.date?.getMinutes()).toBe(30)
  })

  it('Given initial date with time, When setDateTime is called, Then time is updated', () => {
    const initialDate = new Date(2023, 0, 1, 10, 30, 0)
    const { result } = renderHook(() => useDate(initialDate, true))

    act(() => {
      result.current.setDateTime((prev) => {
        if (!prev) return
        const updated = new Date(prev)
        updated.setHours(12)
        updated.setMinutes(45)
        return updated
      })
    })

    expect(result.current.date?.getHours()).toBe(12)
    expect(result.current.date?.getMinutes()).toBe(45)
  })
})
