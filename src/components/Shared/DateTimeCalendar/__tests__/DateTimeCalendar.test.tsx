import { DateTimeCalendar } from '@/components/Shared/DateTimeCalendar'
import { render, screen, fireEvent } from '@testing-library/react'
import { vi } from 'vitest'
import '@testing-library/jest-dom'
import * as useDateMock from '@/components/Shared/DateTimeCalendar/useDate'

vi.mock('@core-clib/react', () => ({
  DatePicker: ({ value }: any) => (
    <div data-testid="DatePicker">
      <div data-testid="selected-date">{value?.toString()}</div>
    </div>
  )
}))

vi.mock('@/components/Shared/TimePicker', () => ({
  TimePicker: ({ onChangeTime, error }: any) => (
    <div data-testid="TimePicker">
      <input data-testid="time-input" type="time" onChange={onChangeTime} />
      {error && <div data-testid="error-message">{error}</div>}
    </div>
  )
}))

vi.mock('@/components/Shared/DateTimeCalendar/Buttons', () => ({
  Buttons: ({ onClickApply, onClickCancel, onClickSetToday }: any) => (
    <div data-testid="Buttons">
      <button data-testid="apply-button" onClick={onClickApply}>
        Apply
      </button>
      <button data-testid="cancel-button" onClick={onClickCancel}>
        Cancel
      </button>
      <button data-testid="set-today-button" onClick={onClickSetToday}>
        Set Today
      </button>
    </div>
  )
}))

vi.mock('@/core/hooks/useClickOutside', () => ({
  useClickOutside: vi.fn()
}))

vi.mock('@/components/Shared/DateTimeCalendar/useDate', () => ({
  useDate: vi.fn()
}))

describe('DateTimeCalendar Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  const useDate = vi
    .spyOn(useDateMock, 'useDate')
    .mockReturnValue({ date: new Date(), setDate: vi.fn() })

  it('should not render when isOpen is false', () => {
    const { container } = render(
      <DateTimeCalendar isOpen={false} onApply={vi.fn()} onCancel={vi.fn()} />
    )
    expect(container.firstChild).toBeNull()
  })

  it('should render correctly when isOpen is true', () => {
    useDate.mockReturnValue({ date: new Date(), setDate: vi.fn() })

    render(
      <DateTimeCalendar
        isOpen={true}
        onApply={vi.fn()}
        onCancel={vi.fn()}
        time
      />
    )
    expect(screen.getByTestId('DatePicker')).toBeInTheDocument()
    expect(screen.getByTestId('TimePicker')).toBeInTheDocument()
    expect(screen.getByTestId('Buttons')).toBeInTheDocument()
  })

  it('should call onCancel when the cancel button is clicked', () => {
    const onCancel = vi.fn()
    useDate.mockReturnValue({ date: new Date(), setDate: vi.fn() })

    render(
      <DateTimeCalendar isOpen={true} onApply={vi.fn()} onCancel={onCancel} />
    )

    fireEvent.click(screen.getByTestId('cancel-button'))
    expect(onCancel).toHaveBeenCalled()
  })

  it('should call onApply with the selected date when the apply button is clicked', () => {
    const onApply = vi.fn()
    const testDate = new Date('2021-01-01T12:00:00')
    useDate.mockReturnValue({ date: testDate, setDate: vi.fn() })

    render(
      <DateTimeCalendar isOpen={true} onApply={onApply} onCancel={vi.fn()} />
    )

    fireEvent.click(screen.getByTestId('apply-button'))
    expect(onApply).toHaveBeenCalledWith(testDate)
  })

  it('should not display an error when a future date is selected', () => {
    const futureDate = new Date(Date.now() + 86400000) // Tomorrow
    useDate.mockReturnValue({ date: futureDate, setDate: vi.fn() })

    render(
      <DateTimeCalendar
        isOpen={true}
        onApply={vi.fn()}
        onCancel={vi.fn()}
        time
        isFutureSelectAllowed
      />
    )

    fireEvent.click(screen.getByTestId('apply-button'))
    expect(screen.queryByTestId('error-message')).toBeNull()
  })

  it('should call onSetToday and onCancel when the set today button is clicked', () => {
    const onSetToday = vi.fn()
    const onCancel = vi.fn()
    useDate.mockReturnValue({ date: new Date(), setDate: vi.fn() })

    render(
      <DateTimeCalendar
        isOpen={true}
        onApply={vi.fn()}
        onCancel={onCancel}
        onSetToday={onSetToday}
      />
    )

    fireEvent.click(screen.getByTestId('set-today-button'))
    expect(onSetToday).toHaveBeenCalledWith(expect.any(Date))
    expect(onCancel).toHaveBeenCalled()
  })

  it('should update the date when time input changes', () => {
    const setDateTime = vi.fn()
    const initialDate = new Date('2021-01-01T12:00:00')
    useDate.mockReturnValue({ date: initialDate, setDateTime })

    render(
      <DateTimeCalendar
        isOpen={true}
        onApply={vi.fn()}
        onCancel={vi.fn()}
        time
      />
    )

    fireEvent.change(screen.getByTestId('time-input'), {
      target: { value: '14:30:00' }
    })
    expect(setDateTime).toHaveBeenCalledWith(expect.any(Function))
  })
})
