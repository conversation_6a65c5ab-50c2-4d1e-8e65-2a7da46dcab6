import { Buttons } from '@/components/Shared/DateTimeCalendar/Buttons'
import { useDate } from '@/components/Shared/DateTimeCalendar/useDate'
import { TimePicker } from '@/components/Shared/TimePicker'
import { useClickOutside } from '@/core/hooks/useClickOutside'
import { DatePicker } from '@core-clib/react'
import classNames from 'classnames'
import { ChangeEvent, FC, MutableRefObject, useRef, useState } from 'react'
import styles from './DateTimeCalendar.module.scss'

type Props = {
  isOpen: boolean
  onApply: (date: Date) => void
  dateTime?: Date | string
  onCancel: () => void
  onSetToday?: (date: Date) => void
  onClear?: () => void
  className?: string
  time?: boolean
  clear?: boolean
  isFutureSelectAllowed?: boolean
}

export const DateTimeCalendar: FC<Props> = ({
  isOpen,
  onApply,
  dateTime,
  onCancel,
  onSetToday,
  className,
  clear,
  onClear,
  time,
  isFutureSelectAllowed = true
}) => {
  const containerRef = useRef<HTMLElement>(null)
  const { date, setDate, setDateTime } = useDate(dateTime, isOpen)
  const [error, setError] = useState<string | undefined>(undefined)

  useClickOutside(containerRef, isOpen, onCancel)

  const onClickCancel = () => {
    setError(undefined)
    onCancel()
  }

  const onChangeTime = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.currentTarget.value
    const [hours, minutes, seconds] = value.split(':')

    if (!hours || !minutes || !seconds) {
      return
    }

    setDateTime((prev) => {
      if (!prev) {
        return
      }

      const updatedDate = new Date(prev)
      updatedDate?.setHours(parseInt(hours))
      updatedDate?.setMinutes(parseInt(minutes))
      updatedDate?.setSeconds(parseInt(seconds))

      return updatedDate
    })
  }

  const onClickApply = () => {
    setError(undefined)

    if (!date) {
      setError('Please select a date')
      return
    }

    if (!isFutureSelectAllowed && date.getTime() > Date.now()) {
      setError(`Selecting future dates is prohibited.`)

      return
    }

    onApply(date)
    onClickCancel()
  }

  const onClickSetToday = () => {
    onSetToday?.(new Date())
    onClickCancel()
  }

  const onClickClear = () => {
    onClear?.()
    onClickCancel()
  }

  return isOpen ? (
    <div
      aria-label="Date and Time Picker"
      ref={containerRef as MutableRefObject<HTMLDivElement | null>}
      className={classNames(styles.container, className)}
      data-testid="dateTimeCalendar"
    >
      <DatePicker
        mode="single"
        value={date}
        onSelect={setDate}
        displayedMonth={date}
        setDisplayedMonth={setDate}
      />
      {time && (
        <TimePicker date={date} onChangeTime={onChangeTime} error={error} />
      )}
      <Buttons
        date={date}
        onClickApply={onClickApply}
        onClickCancel={onClickCancel}
        onClickSetToday={onClickSetToday}
        clear={clear}
        onClickClear={onClickClear}
        futureSelect={isFutureSelectAllowed}
      />
    </div>
  ) : null
}
