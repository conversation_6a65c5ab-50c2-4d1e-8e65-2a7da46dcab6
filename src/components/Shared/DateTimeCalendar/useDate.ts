import { preserveTime } from '@/core/utils/date'
import { useEffect, useState } from 'react'

export const useDate = (incomeDate: unknown, isOpen: boolean) => {
  const [date, setDateTime] = useState<Date>()

  const setDate = (
    incoming: Date | ((prev: Date | undefined) => Date | undefined)
  ) => {
    setDateTime((prev) =>
      typeof incoming === 'function'
        ? preserveTime(prev, incoming(prev))
        : preserveTime(prev, incoming)
    )
  }

  useEffect(() => {
    setDateTime(initializeDate(incomeDate))
  }, [isOpen])

  return {
    date,
    setDate,
    setDateTime
  }
}

// We don't know what exactly the date is, so we need to check it
function initializeDate(date: unknown): Date {
  if (date instanceof Date) {
    return date as Date
  }

  if (typeof date === 'string') {
    return date ? new Date(date) : new Date()
  }

  throw Error(`Incorrect date is provided to DateTimeCalendar: ${date}`)
}
