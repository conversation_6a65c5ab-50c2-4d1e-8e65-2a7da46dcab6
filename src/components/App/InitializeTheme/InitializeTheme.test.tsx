import { Theme } from '@/components/App/InitializeTheme/useInitializeTheme'
import { Glue42 } from '@glue42/desktop'
import { InitializeTheme } from './InitializeTheme'
import { setUrlParam } from '@/core/testing/mocks'
import { renderWithReduxInitialState } from '@/core/testing'
import Glue = Glue42.Glue

describe('InitializeTheme', () => {
  afterEach(() => {
    document.body.className = ''
  })

  it('should add a class indicating the dark theme, based on the URL query parameter', () => {
    // given
    setUrlParam('theme', 'core-dark-theme')

    // when
    renderWithReduxInitialState(<InitializeTheme />)

    // then
    expect(document.body).toHaveClass('core-dark-theme')
  })

  it('should add a class indicating the light theme, based on the URL query parameter', () => {
    // given
    setUrlParam('theme', 'core-light-theme')

    // when
    renderWithReduxInitialState(<InitializeTheme />)

    // then
    expect(document.body).toHaveClass('core-light-theme')
  })

  it('should add a class indicating the dark theme if the URL query parameter does not occur', () => {
    // given
    setUrlParam('theme', '')

    // when
    renderWithReduxInitialState(<InitializeTheme />)

    // then
    expect(document.body).toHaveClass('core-dark-theme')
  })

  it('should add a class indicating the dark theme if the URL query parameter does not match any of the predefined themes', () => {
    // given
    setUrlParam('theme', 'green-purple-123')

    // when
    renderWithReduxInitialState(<InitializeTheme />)

    // then
    expect(document.body).toHaveClass('core-dark-theme')
  })

  it('should use glue theme and ignore query params', () => {
    window.glue = {} as Glue
    // given
    setUrlParam('theme', Theme.Light)

    // when
    renderWithReduxInitialState(<InitializeTheme />)

    // then
    expect(document.body).not.toHaveClass(Theme.Light)
  })
})
