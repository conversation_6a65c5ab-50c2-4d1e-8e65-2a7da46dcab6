import { logger } from '@/core/utils/logger'
import { useEffect } from 'react'
import { getParamFromUrl } from '@/core/utils/router'
import { useAppDispatch } from '@/core/redux/hooks'
import { setUserApplicationTheme } from '@/core/redux/features/users'
import { getGlue } from '@/core/services/glue'

export enum Theme {
  Dark = 'core-dark-theme',
  Light = 'core-light-theme'
}

export const matchTheme = (name: unknown): Theme => {
  switch (name) {
    case Theme.Dark:
      return Theme.Dark
    case Theme.Light:
      return Theme.Light
    default:
      return Theme.Dark
  }
}

export const useInitializeTheme = () => {
  const dispatch = useAppDispatch()
  useEffect(() => {
    const glue = getGlue()

    if (glue) {
      // Subscribe to theme changes
      glue.themes?.onChanged((theme) => {
        const currentTheme = theme.name === 'light' ? Theme.Light : Theme.Dark
        document.body.classList.remove(Theme.Dark, Theme.Light)
        document.body.classList.add(currentTheme)
        dispatch(setUserApplicationTheme(currentTheme))
      })
      logger.info('The theme is defined by Glue. Query param is ignored.')
    } else {
      const themeNameFromUrl = getParamFromUrl('theme')
      const currentTheme = matchTheme(themeNameFromUrl)
      document.body.classList.add(currentTheme)
      dispatch(setUserApplicationTheme(currentTheme))
    }
  }, [])
}
