import {
  aggregatorApiEndpoints,
  ConnectionDetails
} from '@/core/services/simultaneousAccess'
import socket from 'socket.io-client'
import { env } from '@/core/config'
import { useEffect, useState } from 'react'
import { useAppSelect, useNotifications } from '@/core/redux/hooks'
import { logger } from '@/core/utils/logger'
import { aggregatorClient } from '@/core/services/http'

const handleServiceError = (
  notification: {
    addDangerNotification: (message: string) => void
  },
  context: object,
  error: Error
) => {
  const errorMessage = `Multi Instance Service is not initialized. Error: ${
    (error as Error).message
  }`

  logger.error(errorMessage, context, error)
  notification.addDangerNotification(errorMessage)
}

const socketConnection = socket(env.aggregatorApiUrl, {
  transports: ['websocket']
})

export const useSimultaneousAccess = () => {
  const reportName = useAppSelect((state) => state.report.reportName)
  const [connectionDetails, setConnectionDetails] = useState<
    ConnectionDetails | undefined
  >(undefined)
  const [isMultiInstance, setIsMultiInstance] = useState(false)
  const [userIp, setUserIp] = useState<string | undefined>(undefined)
  const notification = useNotifications()

  const closeOtherInstances = () => {
    socketConnection.emit('clearOtherDeviceInstance', connectionDetails)
    setIsMultiInstance(false)
    window.location.reload()
  }

  const userProfile = useAppSelect((state) => state.users.profile)

  useEffect(() => {
    aggregatorClient
      .get<string>(aggregatorApiEndpoints.getClientDetails)
      .then((response) => setUserIp(response.data))
      .catch((error) =>
        handleServiceError(
          notification,
          {
            service: env.aggregatorApiUrl,
            endpoint: aggregatorApiEndpoints.getClientDetails
          },
          error
        )
      )
  }, [])

  useEffect(() => {
    if (userIp && userProfile?.UserId) {
      setConnectionDetails({
        appName: `${window.location.href}+${reportName}`,
        userId: userProfile.UserId,
        ipAddress: userIp
      })
    }
  }, [userIp, userProfile, reportName])

  useEffect(() => {
    if (connectionDetails) {
      // skip triggering Simultaneous Access when Impersonating user
      if (
        userProfile?.ImpersonatingAs === '' ||
        userProfile?.ImpersonatingAs === null
      ) {
        socketConnection.emit('aggregator_connection', connectionDetails)
      }

      socketConnection.on('onMultiIDevice', () => {
        console.info(
          'Application is started on another device.',
          connectionDetails
        )
        setIsMultiInstance(true)
      })

      socketConnection.on('disableInstance', () => {
        console.info(
          'Got signal to close the app because a session has been started on another device.',
          connectionDetails
        )
        setIsMultiInstance(true)
      })

      socketConnection.on('continueSession', () => {
        console.info(
          'Got signal to continue session on this device.',
          connectionDetails
        )
        setIsMultiInstance(false)
      })
    }
  }, [connectionDetails])

  return {
    isMultiInstance,
    closeOtherInstances
  }
}
