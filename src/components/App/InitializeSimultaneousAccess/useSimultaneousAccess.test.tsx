import { useSimultaneousAccess } from '@/components/App/InitializeSimultaneousAccess/useSimultaneousAccess'
import { getReduxWrapper } from '@/core/testing'
import { renderHook } from '@testing-library/react'
import axios from 'axios'
import { describe, vi } from 'vitest'

vi.mock('socket.io-client', async (importOriginal) => {
  const actual = (await importOriginal()) as object
  return {
    ...actual,
    on: vi.fn(),
    emit: vi.fn(),
    removeAllListeners: vi.fn()
  }
})

const { wrapper } = getReduxWrapper()

describe('useSimultaneousAccess', () => {
  afterEach(() => {
    vi.clearAllMocks()
  })

  it('should set userId and userIp on initialization', async () => {
    // arrange
    vi.spyOn(axios, 'get')
      .mockResolvedValueOnce({ data: { UserId: 'test' } })
      .mockResolvedValueOnce({ data: '*******' })

    // act
    const { result } = renderHook(() => useSimultaneousAccess(), { wrapper })

    // assert
    expect(result.current.isMultiInstance).toBeFalsy()
  })
})
