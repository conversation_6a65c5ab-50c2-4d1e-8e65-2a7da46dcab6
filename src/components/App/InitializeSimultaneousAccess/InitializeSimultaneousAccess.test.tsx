import { getReduxWrapper } from '@/core/testing'
import { render, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { InitializeSimultaneousAccess } from './InitializeSimultaneousAccess'

describe('InitializeSimultaneousAccess', () => {
  vi.mock('axios', async () => {
    return {
      default: {
        create: vi.fn(() => ({
          get: vi.fn(() => Promise.resolve({ data: 'OK' })),
          defaults: {},
          interceptors: {
            request: {
              use: vi.fn()
            }
          }
        }))
      }
    }
  })

  vi.mock('axios-retry', async () => {
    return {
      default: vi.fn()
    }
  })

  const { wrapper } = getReduxWrapper()

  test('render children', async () => {
    const { container } = render(
      <InitializeSimultaneousAccess>test</InitializeSimultaneousAccess>,
      { wrapper }
    )

    await waitFor(() => {
      expect(container.innerHTML).toBe('test')
    })
  })
})
