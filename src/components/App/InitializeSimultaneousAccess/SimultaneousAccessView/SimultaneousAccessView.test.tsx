import { SimultaneousAccessView } from '@/components/App/InitializeSimultaneousAccess/SimultaneousAccessView/SimutaneousAccessView'
import { fireEvent, render } from '@testing-library/react'
import { test, vi } from 'vitest'

test('SimultaneousAccessView renders correctly', async () => {
  const { findByTestId } = render(
    <SimultaneousAccessView closeOtherDeviceInstances={vi.fn()} />
  )

  const panel = await findByTestId('SimultaneousAccessView')

  // Test that the component is rendered
  expect(panel).toBeInTheDocument()
})

test('SimultaneousAccessView calls closeOtherDeviceInstances on button click', async () => {
  const closeOtherDeviceInstances = vi.fn()

  const { findByText } = render(
    <SimultaneousAccessView
      closeOtherDeviceInstances={closeOtherDeviceInstances}
    />
  )

  // Finding the button
  const button = (await findByText('Continue')) as HTMLButtonElement

  // Click the button
  fireEvent.click(button)

  expect(closeOtherDeviceInstances).toHaveBeenCalledTimes(1)
})
