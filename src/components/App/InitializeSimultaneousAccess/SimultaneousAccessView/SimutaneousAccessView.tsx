import { FC } from 'react'
import { <PERSON><PERSON>, Panel } from '@core-clib/react'
import style from './SimultaneousAccessView.module.scss'

type Props = {
  closeOtherDeviceInstances: VoidFunction
}

export const SimultaneousAccessView: FC<Props> = ({
  closeOtherDeviceInstances
}) => {
  return (
    <aside
      className={style.SimultaneousAccessView}
      data-testid="SimultaneousAccessView"
    >
      <Panel
        className={style.panel}
        displayTitle="Breaching Simultaneous Access"
        icon="ci-warning2"
      >
        <div className={style.body}>
          <h3>An M72 session is already running on another device.</h3>
          <p>
            Clicking 'Continue' will start a new session on this device and
            terminate the other session.
          </p>

          <div>
            <Button
              onClick={closeOtherDeviceInstances}
              intent="success"
              text="Continue"
            ></Button>
          </div>
        </div>
      </Panel>
    </aside>
  )
}
