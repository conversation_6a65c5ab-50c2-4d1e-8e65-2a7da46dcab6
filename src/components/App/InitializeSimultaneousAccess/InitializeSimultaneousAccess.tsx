import { SimultaneousAccessView } from '@/components/App/InitializeSimultaneousAccess/SimultaneousAccessView/SimutaneousAccessView'
import { FC, PropsWithChildren } from 'react'
import { useSimultaneousAccess } from '@/components/App/InitializeSimultaneousAccess/useSimultaneousAccess'

export const InitializeSimultaneousAccess: FC<PropsWithChildren> = ({
  children
}) => {
  const { isMultiInstance, closeOtherInstances } = useSimultaneousAccess()
  return isMultiInstance ? (
    <SimultaneousAccessView closeOtherDeviceInstances={closeOtherInstances} />
  ) : (
    <>{children}</>
  )
}
