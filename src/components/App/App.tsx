import { InitializeApp } from '@/components/App/InitializeApp'
import { AppVersion } from '@/components/AppVersion'
import { env } from '@/core/config'
import { initDatadog, initMemoryMeasurement } from '@/core/services/datadog'
import { useReportNameFormatIsValid } from '@/core/utils/router'
import { LicenseManager } from '@ag-grid-enterprise/core'
import '@core-clib/ag-grid/dist/ag-theme-core-sdk.css'
import '@core-clib/css/dist/styles.css'
import '@core-clib/css/dist/themes/base.css'
import '@core-clib/css/dist/themes/dark.css'
import '@core-clib/css/dist/themes/light.css'
import '@core-clib/react/dist/style.css'
import '@core-clib/web-components/dist/style.css'
import { useEffect, useRef } from 'react'
import ReportErrorPage from '../../core/utils/router/ReportErrorPage'
import { Grid } from '../Grid'
import { TopBar } from '../TopBar'
import './App.css'
import { ErrorModal } from './Notifications/ErrorModal'

LicenseManager.setLicenseKey(env.agGridLicenseKey)

initDatadog()

export function App() {
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (!intervalRef.current) {
      intervalRef.current = initMemoryMeasurement()
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [])

  const reportNameFormatIsValid = useReportNameFormatIsValid()

  return reportNameFormatIsValid ? (
    <InitializeApp>
      <div className="grid-wrapper">
        <TopBar />
        <Grid />
        <AppVersion />
      </div>
      <ErrorModal />
    </InitializeApp>
  ) : (
    <ReportErrorPage />
  )
}
