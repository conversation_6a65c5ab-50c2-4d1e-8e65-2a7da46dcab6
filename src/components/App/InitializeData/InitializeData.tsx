import { Loader } from '@/components/App/Loader'
import { startLatestAsOfUtcListener } from '@/core/redux/features/report'
import { useRepoNettingParamEffect } from '@/core/redux/features/report/hooks/useRepoNettingParamEffect'
import { initializeReportDataThunk } from '@/core/redux/features/report/thunk/initializeReportDataThunk'
import { useUserPreferencesEffect } from '@/core/redux/features/users/hooks'
import { useAppDispatch, useAppSelect } from '@/core/redux/hooks'
import { useDatadogUserRegistrationEffect } from '@/core/services/datadog'
import { logger } from '@/core/utils/logger'
import { useGetReportNameFromRoute } from '@/core/utils/router'
import { FC, PropsWithChildren, useEffect } from 'react'

/**
 * The component renders its children only when it has finished loading the initial data.
 */
export const InitializeData: FC<PropsWithChildren> = ({ children }) => {
  const reportName = useGetReportNameFromRoute()
  const isInitialized = useAppSelect((state) => state.report.isInitialized)
  const isLiveModeEnabled = useAppSelect(
    (state) => state.report.isLiveModeEnabled
  )
  const dispatch = useAppDispatch()

  if (!reportName) {
    logger.error('Report name is empty!')
    throw Error('Report name is empty!')
  }

  useEffect(() => {
    if (!isInitialized && reportName) {
      dispatch(initializeReportDataThunk(reportName))
    } else if (isInitialized && isLiveModeEnabled) {
      dispatch(startLatestAsOfUtcListener())
    }
    // Don't make isLiveModeEnabled a dependency, as it's changes are handled by the latestAsOfUtcListener middleware
  }, [dispatch, reportName, isInitialized])

  useRepoNettingParamEffect()
  useUserPreferencesEffect()
  useDatadogUserRegistrationEffect()

  return isInitialized ? <>{children}</> : <Loader />
}
