import {
  allHandlers,
  getInitializeAppWrapper,
  setupMockServer
} from '@/core/testing'
import { render, waitFor } from '@testing-library/react'
import { InitializeData } from './InitializeData'

setupMockServer(...allHandlers)

describe('InitializeData', () => {
  it('should display the children when the data is loaded', async () => {
    // given
    const url = '/reports/pnl-live'

    const { queryByText, getByTestId } = render(
      <InitializeData>Children component</InitializeData>,

      getInitializeAppWrapper(url)
    )

    // then
    expect(queryByText('Children component')).not.toBeInTheDocument()
    expect(getByTestId('loader')).toBeVisible()
    waitFor(() => expect(queryByText('Children component')).toBeVisible())
  })
})
