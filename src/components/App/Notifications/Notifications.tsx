import { useAppSelect } from '@/core/redux/hooks'
import { ToastContainer } from '@core-clib/react'

export const Notifications: React.FC = () => {
  const onToastsUpdated = () => {
    return
  }

  const toasts = useAppSelect((state) => state.notifications.toasts)

  return (
    <>
      {toasts.length > 0 ? (
        <ToastContainer toasts={toasts} onToastsUpdated={onToastsUpdated} />
      ) : null}
    </>
  )
}
