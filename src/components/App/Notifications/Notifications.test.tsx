import { addNotification } from '@/core/redux/features/notifications'
import {
  renderWithMockedState,
  renderWithReduxInitialState
} from '@/core/testing'
import { waitFor } from '@testing-library/react'
import { Notifications } from '.'

describe('Notifications', () => {
  it('should render', () => {
    renderWithMockedState(<Notifications />)
  })

  it('should render with notifications', async () => {
    const { getByText, rerender, store } = renderWithReduxInitialState(
      <Notifications />
    )

    // dispatch notification
    await waitFor(() =>
      store.dispatch(
        addNotification({
          text: 'testmessage',
          intent: 'danger',
          id: Date.now().toString()
        })
      )
    )

    // rerender component
    await waitFor(() => {
      rerender(<Notifications />)
    })
    expect(getByText('testmessage')).toBeInTheDocument()
  })
  it('should render notifications with Title', async () => {
    const { getByText, rerender, store } = renderWithReduxInitialState(
      <Notifications />
    )

    // dispatch notification
    await waitFor(() =>
      store.dispatch(
        addNotification({
          text: {
            title: 'testtitlemessage'
          },
          intent: 'danger',
          id: Date.now().toString()
        })
      )
    )

    // rerender component
    await waitFor(() => {
      rerender(<Notifications />)
    })
    expect(getByText('testtitlemessage')).toBeInTheDocument()
  })
})
