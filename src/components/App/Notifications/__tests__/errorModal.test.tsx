import { getInitializeAppWrapper, setupMockBackend } from '@/core/testing'
import { setUrlParam } from '@/core/testing/mocks'
import { act, cleanup, render, screen, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { App } from '../../App'
import { closeModal, showErrorModal } from '@/core/redux/features/notifications'

describe('Error modal', () => {
  // Mock getComputedStyle to avoid the error
  beforeAll(() => {
    vi.spyOn(window, 'getComputedStyle').mockImplementation(() => {
      return {
        getPropertyValue: () => ''
      } as unknown as CSSStyleDeclaration
    })
  })

  beforeEach(() => {
    setupMockBackend()
  })

  afterEach(() => {
    vi.clearAllMocks()
    cleanup()
    setUrlParam('reportName', 'trades')
  })

  it('display and hide error modal', async () => {
    const { wrapper, store } = getInitializeAppWrapper()
    render(<App />, {
      wrapper
    })

    await waitFor(() => {
      expect(store.getState().report.isInitialized).toBeTruthy()
    })

    await act(() => store.dispatch(showErrorModal('test-error')))

    expect(screen.getByText('test-error')).toBeInTheDocument()

    await act(() => store.dispatch(closeModal()))

    // Use queryByText to check that the modal is no longer in the document
    expect(screen.queryByText('test-error')).not.toBeInTheDocument()
  })
})
