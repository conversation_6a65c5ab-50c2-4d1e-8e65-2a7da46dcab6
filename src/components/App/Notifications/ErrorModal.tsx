import { closeModal } from '@/core/redux/features/notifications'
import { useAppDispatch, useAppSelect } from '@/core/redux/hooks'
import { ModalDialog } from '@core-clib/react'
import { IAction } from '@core-clib/web-components'

export const ErrorModal = () => {
  const errorMessage = useAppSelect((state) => state.notifications.errorMessage)
  const dispatch = useAppDispatch()

  const footerRightActions: IAction<string>[] = [
    {
      id: 'CLOSE',
      label: 'OK',
      value: 'CLOSE'
    }
  ]

  return (
    <ModalDialog
      title="Error"
      titleIcon="ci-Plus-Stop"
      footerRightActions={footerRightActions}
      onClose={() => {
        dispatch(closeModal())
      }}
      onAction={() => {
        dispatch(closeModal())
      }}
      isOpen={Boolean(errorMessage)}
    >
      <span>{errorMessage}</span>
    </ModalDialog>
  )
}
