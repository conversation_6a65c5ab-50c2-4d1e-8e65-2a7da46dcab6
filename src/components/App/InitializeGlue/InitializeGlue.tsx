import { Loader } from '@/components/App/Loader'
import { FC, PropsWithChildren } from 'react'
import { useInitializeGlueEffect } from './hooks/useInitializeGlueEffect'
import { usePreProcessingParamsCheckEffect } from './hooks/usePreProcessingParamsCheck'

export const enum InitializationStep {
  notStarted = 'notStarted',
  notGlue = 'notGlue',
  initialized = 'initialized',
  error = 'error'
}

export const InitializeGlue: FC<PropsWithChildren> = ({ children }) => {
  const glueInitializationState = useInitializeGlueEffect()

  const isPreProcessingParamsInitialized = usePreProcessingParamsCheckEffect(
    glueInitializationState
  )

  return isPreProcessingParamsInitialized ? <>{children}</> : <Loader />
}
