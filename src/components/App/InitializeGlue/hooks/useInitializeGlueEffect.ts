import { useEffect, useState } from 'react'
import { InitializationStep } from '../InitializeGlue'
import { getApplicationTitle, initializeGlue } from '@/core/services/glue'
import { useDispatch } from 'react-redux'

export const useInitializeGlueEffect = () => {
  const [glueInitializationState, setGlueInitializationState] =
    useState<InitializationStep>(InitializationStep.notStarted)

  const dispatch = useDispatch()

  useEffect(() => {
    const initialize = async () => {
      try {
        await initializeGlue(dispatch)
        document.title = getApplicationTitle()
        setGlueInitializationState(InitializationStep.initialized)
      } catch (error) {
        setGlueInitializationState(InitializationStep.error)
      }
    }

    initialize()
  }, [])

  return glueInitializationState
}
