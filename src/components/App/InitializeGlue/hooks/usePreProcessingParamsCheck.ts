import { useEffect, useState } from 'react'
import { InitializationStep } from '../InitializeGlue'
import { useAppSelect } from '@/core/redux/hooks'

export const usePreProcessingParamsCheckEffect = (
  glueInitializationState: InitializationStep
) => {
  const preProcessingParams = useAppSelect(
    (state) => state.report.preProcessingParams
  )
  const drillThroughViewLiveUpdateAsOfUtcMetadataOrigin = useAppSelect(
    (state) => state.report.drillThroughViewLiveUpdateAsOfUtcMetadataOrigin
  )

  const [
    isPreProcessingParamsInitialized,
    setIsPreProcessingParamsInitialized
  ] = useState(false)

  useEffect(() => {
    if (
      [
        InitializationStep.initialized,
        InitializationStep.error,
        InitializationStep.notGlue
      ].includes(glueInitializationState)
    ) {
      setIsPreProcessingParamsInitialized(true)
    }
  }, [
    preProcessingParams,
    drillThroughViewLiveUpdateAsOfUtcMetadataOrigin,
    glueInitializationState
  ])

  return isPreProcessingParamsInitialized
}
