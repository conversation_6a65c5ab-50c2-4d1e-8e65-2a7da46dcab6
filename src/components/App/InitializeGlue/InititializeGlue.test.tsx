import { store } from '@/core/redux/store'
import { getReduxWrapper } from '@/core/testing'
import { render, waitFor } from '@testing-library/react'
import { expect, Mock, vi } from 'vitest'
import { InitializeGlue } from './InitializeGlue'
import {
  isAppInGlueEnvironment,
  getGlueInstance,
  getDrillThroughContext
} from '@/core/services/glue/utils/glueUtils'
import Glue from '@glue42/desktop'
vi.mock('@/core/services/glue/utils/glueUtils')
vi.mock('@glue42/desktop')

describe('InitializeGlue', () => {
  it('should render the children when the glue is initialized', async () => {
    const { wrapper } = getReduxWrapper()
    // given
    const { container } = render(
      <InitializeGlue>Children component</InitializeGlue>,
      { wrapper }
    )

    await waitFor(() => {
      expect(container.innerHTML).toBe('Children component')
    })
  })

  it('should set livemode on if DT is null', async () => {
    const { wrapper } = getReduxWrapper()
    // given
    render(<InitializeGlue>Children component</InitializeGlue>, {
      wrapper
    })

    await waitFor(() => {
      expect(store.getState().report.isLiveModeEnabled).toBeTruthy()
    })
  })
  it('should initialize Glue when in Glue environment and no Glue instance exists', async () => {
    (isAppInGlueEnvironment as Mock).mockReturnValue(true)
    ;(getGlueInstance as Mock).mockReturnValue(null)
    ;(Glue as Mock).mockResolvedValue({ channels: { join: vi.fn() } })

    const { wrapper } = getReduxWrapper()
    // given
    const { container } = render(
      <InitializeGlue>Children component</InitializeGlue>,
      {
        wrapper
      }
    )

    await waitFor(() => {
      expect(container.innerHTML).toBe('Children component')
    })

    expect(Glue).toHaveBeenCalled()
    expect(window.glue).toBeDefined()
  })
})
