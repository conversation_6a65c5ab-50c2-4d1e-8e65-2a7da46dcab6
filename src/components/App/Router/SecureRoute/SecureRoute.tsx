import { toRelativeUrl } from '@okta/okta-auth-js'
import { useOktaAuth } from '@okta/okta-react'
import { FC, useEffect } from 'react'
import { Outlet } from 'react-router-dom'
import { Loader } from '../../Loader'

export const SecureRoute: FC = () => {
  const { oktaAuth, authState } = useOktaAuth()

  useEffect(() => {
    if (!authState) {
      return
    }

    if (!authState?.isAuthenticated) {
      const originalUri = toRelativeUrl(
        window.location.href,
        window.location.origin
      )
      oktaAuth.setOriginalUri(originalUri)
      oktaAuth.signInWithRedirect()
    }
  }, [oktaAuth, !!authState, authState?.isAuthenticated])

  if (!authState || !authState?.isAuthenticated) {
    return <Loader />
  }

  return <Outlet />
}
