import { env } from '@/core/config'
import { okta } from '@/core/utils/okta/oktaInstance'
import { restoreOriginalUri } from '@/core/utils/okta/restoreOriginalUri'
import ReportErrorPage from '@/core/utils/router/ReportErrorPage'
import { Security, LoginCallback } from '@okta/okta-react'
import { BrowserRouter, Routes, Route } from 'react-router-dom'
import { FC } from 'react'
import { SecureRoute } from './SecureRoute/SecureRoute'
import { AppContainer } from '../AppContainer'

export const Router: FC = () => {
  if (env.isOkta && !okta) {
    throw new Error('Okta is not initialized')
  }

  if (env.isOkta) {
    return (
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      <Security oktaAuth={okta!} restoreOriginalUri={restoreOriginalUri}>
        <BrowserRouter>
          <Routes>
            <Route
              path="/reports/:reportName"
              element={<SecureRoute />}
              errorElement={<ReportErrorPage />}
            >
              <Route path="" element={<AppContainer />} />
            </Route>
            <Route
              path="/login/callback"
              element={<LoginCallback />}
              errorElement={<ReportErrorPage />}
            />
            <Route path="/" element={<ReportErrorPage />} />
          </Routes>
        </BrowserRouter>
      </Security>
    )
  } else {
    return (
      <BrowserRouter>
        <Routes>
          <Route path="/reports/:reportName" element={<AppContainer />} />
          <Route path="/" element={<ReportErrorPage />} />
        </Routes>
      </BrowserRouter>
    )
  }
}
