import { FC, PropsWithChildren } from 'react'
import { store } from '@/core/redux/store'
import {
  GridProvider,
  GridProviderProps
} from '@/components/Grid/context/GridProvider'
import { Notifications } from '@/components/App/Notifications'
import { InitializeTheme } from '@/components/App/InitializeTheme'
import { InitializeGlue } from '@/components/App/InitializeGlue'
import { InitializeSimultaneousAccess } from '@/components/App/InitializeSimultaneousAccess/InitializeSimultaneousAccess'
import { InitializeData } from '../InitializeData'

export type InitializeAppProps = GridProviderProps & {
  reduxStore?: typeof store
}

export const InitializeApp: FC<PropsWithChildren<InitializeAppProps>> = ({
  children
}) => (
  <InitializeGlue>
    <InitializeTheme>
      <InitializeData>
        <InitializeSimultaneousAccess>
          <GridProvider>{children}</GridProvider>
        </InitializeSimultaneousAccess>
      </InitializeData>
      <Notifications />
    </InitializeTheme>
  </InitializeGlue>
)
