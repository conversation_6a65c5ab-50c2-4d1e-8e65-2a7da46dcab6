import { PageHeaderItem } from '@core-clib/react'
import { TooltipWrapper } from '@core-clib/react'
import { PropsWithChildren } from 'react'
import { useAppSelect } from '@/core/redux/hooks'

interface MetadataItemProps {
  localDate: string
  utcString: string
  label: string
}

export const MetadataItem: React.FC<PropsWithChildren<MetadataItemProps>> = ({
  localDate,
  utcString,
  label,
  children
}) => {
  const theme = useAppSelect((state) => state.users.theme)
  return (
    <div className="item-wrapper">
      <PageHeaderItem label={label}>
        <TooltipWrapper
          tippyOptions={{
            content: `<div class="tippy-tooltip ${theme}">${localDate} (local)<br>${utcString} (UTC)</div>`,
            allowHTML: true
          }}
        >
          <div className="flex flex-row">
            <div className="datetime-plug">
              <span className="last-update">{children}</span>
            </div>
          </div>
        </TooltipWrapper>
      </PageHeaderItem>
    </div>
  )
}
