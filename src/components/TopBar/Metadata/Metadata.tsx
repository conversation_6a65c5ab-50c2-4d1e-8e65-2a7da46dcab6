import { PageHeaderItem } from '@core-clib/react'
import { useAppSelect } from '@/core/redux/hooks'
import { MetadataItem } from './MetadataItem'
import { metadataSelector } from '@/core/redux/selectors'

export const Metadata: React.FC = () => {
  const metadata = useAppSelect(metadataSelector)

  return (
    metadata &&
    Object.entries(metadata).map(([label, time]) => (
      <div className="item-wrapper" key={label}>
        <PageHeaderItem>
          <MetadataItem
            localDate={time.localDatePrecise}
            utcString={time.utcStringPrecise}
            label={label}
          >
            {time.localDate}
          </MetadataItem>
        </PageHeaderItem>
      </div>
    ))
  )
}
