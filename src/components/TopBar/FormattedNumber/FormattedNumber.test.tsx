import { FormattedValue } from './FormattedValue'
import { renderWithReduxInitialState } from '@/core/testing'

describe('FormattedValue', () => {
  test.each`
    value                             | expectedText                | expectedColor
    ${0}                              | ${'0'}                      | ${'var(--green-3)'}
    ${1234.56}                        | ${'1,235'}                  | ${'var(--green-3)'}
    ${-1234.56}                       | ${'(1,235)'}                | ${'var(--red-2)'}
    ${'foo'}                          | ${'foo'}                    | ${'var(--white)'}
    ${'2021-03-02T17:00:00.0000000Z'} | ${'03/02/2021, 6:00:00 PM'} | ${'var(--white)'}
  `(
    'renders $value as $expectedText with color $expectedColor',
    ({ value, expectedText, expectedColor }) => {
      // given
      const { getByText } = renderWithReduxInitialState(
        <FormattedValue value={value} />
      )

      // then
      const formattedNumberElement = getByText(expectedText)
      expect(formattedNumberElement).toBeInTheDocument()
      expect(formattedNumberElement).toHaveStyle(`color: ${expectedColor}`)
    }
  )
})
