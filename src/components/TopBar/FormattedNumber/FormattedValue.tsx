import { FC } from 'react'
import { formatNumber } from '@/core/utils/math'
import { formatDateToLocal } from '@/core/utils/date'
import { useAppSelect } from '@/core/redux/hooks'

interface FormattedNumberProps {
  value: string | number
}

export const FormattedValue: FC<FormattedNumberProps> = ({ value }) => {
  const theme = useAppSelect((state) => state.users.theme)
  const dateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{7}Z$/i
  let formattedValue
  let color = 'var(--ag-data-color)'

  if (typeof value === 'number') {
    if (value < 0) {
      color = theme === 'core-dark-theme' ? 'var(--red-2)' : 'var(--red-3)'
    } else if (value >= 0) {
      color = theme === 'core-dark-theme' ? 'var(--green-3)' : 'var(--green-4)'
    }
    formattedValue = formatNumber({ value })
  } else if (dateRegex.test(String(value))) {
    formattedValue = formatDateToLocal(new Date(value))
  } else {
    formattedValue = value
  }

  return <span style={{ color }}>{formattedValue}</span>
}
