import { getReduxWrapper } from '@/core/testing'
import { render, screen } from '@testing-library/react'
import { ReactNode } from 'react'
import { vi } from 'vitest'
import { TopBar } from './TopBar'

vi.mock('@core-clib/react', () => ({
  HeaderScroll: ({ children }: { children: ReactNode }) => (
    <div data-testid="header-scroll">{children}</div>
  ),
  DateHeaderFilter: ({ children }: { children: ReactNode }) => (
    <div data-testid="date-header-filter">{children}</div>
  ),
  AppHeader: ({ children }: { children: ReactNode }) => (
    <div data-testid="app-header">{children}</div>
  ),
  PageHeaderItems: ({ children }: { children: ReactNode }) => (
    <div data-testid="page-header-items">{children}</div>
  ),
  PageHeaderItem: ({ children }: { children: ReactNode }) => (
    <div data-testid="page-header-item">{children}</div>
  ),
  Collapse: ({ children }: { children: ReactNode }) => (
    <div data-testid="collapse">{children}</div>
  ),
  PageHeader: ({ children }: { children: ReactNode }) => (
    <div data-testid="page-header">{children}</div>
  ),
  TooltipWrapper: ({ children }: { children: ReactNode }) => (
    <div data-testid="tooltip-wrapper">{children}</div>
  ),
  Field: ({ children }: { children: ReactNode }) => (
    <div data-testid="field">{children}</div>
  ),
  Button: ({ children }: { children: ReactNode }) => (
    <div data-testid="button">{children}</div>
  ),
  Switch: ({ children }: { children: ReactNode }) => (
    <div data-testid="switch">{children}</div>
  ),
  Dropdown: ({ children }: { children: ReactNode }) => (
    <div data-testid="dropdown">{children}</div>
  ),
  Icon: () => <div date-testid="icon" />
}))

describe('test TopBar component', () => {
  it('should render TopBar component', () => {
    const { wrapper } = getReduxWrapper()
    render(<TopBar />, {
      wrapper
    })
    expect(screen.getByTestId('app-header')).toBeInTheDocument()
  })

  it('should not render summary bar if isSummaryBarVisible is false', () => {
    const { wrapper, store } = getReduxWrapper({
      users: {
        userPreferences: {
          componentVisibility: {
            isSummaryBarVisible: false
          }
        }
      }
    })
    const { queryByTestId } = render(<TopBar />, {
      wrapper
    })

    expect(
      store.getState().users.userPreferences.componentVisibility
        .isSummaryBarVisible
    ).toBe(false)
    expect(queryByTestId('topheader-summary')).toBeInTheDocument()
  })

  it('should not render summary bar if isAppHeaderVisible is false', () => {
    const { wrapper, store } = getReduxWrapper({
      users: {
        userPreferences: {
          componentVisibility: {
            isAppHeaderVisible: false
          }
        }
      }
    })
    const { queryByTestId } = render(<TopBar />, {
      wrapper
    })

    expect(
      store.getState().users.userPreferences.componentVisibility
        .isAppHeaderVisible
    ).toBe(false)
    expect(queryByTestId('topheader-summary')).toBeInTheDocument()
  })

  it('should not render summary bar if isAppHeaderVisible is true', () => {
    const { wrapper, store } = getReduxWrapper({
      users: {
        userPreferences: {
          componentVisibility: {
            isAppHeaderVisible: true
          }
        }
      }
    })
    const { queryByTestId } = render(<TopBar />, {
      wrapper
    })

    expect(
      store.getState().users.userPreferences.componentVisibility
        .isAppHeaderVisible
    ).toBe(true)
    expect(queryByTestId('topheader-summary')).toBeInTheDocument()
  })
})
