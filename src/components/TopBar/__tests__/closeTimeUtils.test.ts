import {
  getCloseDateForRegion,
  optionByName
} from '../CloseTimeButton/closeTimeUtils'

describe('regionByName', () => {
  it('should return the correct region object for a valid region name', () => {
    // When
    const result = optionByName('apac')

    // Then
    expect(result.name).toEqual('apac')
    expect(result.label).toEqual('APAC')
  })

  it('should return the default region object if the region name is invalid', () => {
    // Given
    const invalidRegionName = 'invalid'

    // When
    const result = optionByName(invalidRegionName)

    // Then
    expect(result.name).toEqual('EOD')
  })
})

describe('getCloseDateForRegion', () => {
  it('should return the correct close date for Monday before regionCloseMoment', () => {
    // Given
    const region = optionByName('emea')
    const currentUTCDate = new Date('2025-03-31T14:00:00Z') // Monday before close time

    // When
    const result = getCloseDateForRegion(region, currentUTCDate)

    // Then
    expect(result.toISOString()).toEqual('2025-03-28T16:20:00.000Z') // winter 16:20 UTC
  })

  it('should return the correct close date for Sunday', () => {
    // Given
    const region = optionByName('us')
    const currentUTCDate = new Date('2025-03-30T20:00:00Z') // Sunday before close time

    // When
    const result = getCloseDateForRegion(region, currentUTCDate)

    // Then
    expect(result.toUTCString()).toEqual('Fri, 28 Mar 2025 21:59:00 GMT')
  })

  it('should return the correct close date for Saturday', () => {
    // Given
    const region = optionByName('apac')
    const currentUTCDate = new Date('2025-03-29T10:00:00Z') // Saturday before close time

    // When
    const result = getCloseDateForRegion(region, currentUTCDate)

    // Then
    expect(result.toUTCString()).toEqual('Fri, 28 Mar 2025 09:45:00 GMT')
  })

  it('should return today’s close date if current time is after regionCloseMoment', () => {
    // Given
    const region = optionByName('emea')
    const currentUTCDate = new Date('2025-03-31T16:00:00Z') // Monday after close time
    // When
    const result = getCloseDateForRegion(region, currentUTCDate)

    // Then
    expect(result.toUTCString()).toEqual('Mon, 31 Mar 2025 15:20:00 GMT')
  })

  it('should return the correct US close date before close time (Winter)', () => {
    // Given
    const region = optionByName('us')
    const currentUTCDate = new Date('2025-02-20T20:00:00Z') // Thu before close time

    // When
    const result = getCloseDateForRegion(region, currentUTCDate)

    // Then
    expect(result.toUTCString()).toEqual('Wed, 19 Feb 2025 22:59:00 GMT')
  })

  it('should return the correct US close date after close time (Winter)', () => {
    // Given
    const region = optionByName('us')
    const currentUTCDate = new Date('2025-02-20T23:00:00Z') // Sunday after close time

    // When
    const result = getCloseDateForRegion(region, currentUTCDate)

    // Then
    expect(result.toUTCString()).toEqual('Thu, 20 Feb 2025 22:59:00 GMT') // winter 22:59 UTC
  })

  it('should return the correct US close date before close time (Summer)', () => {
    // Given
    const region = optionByName('us')
    const currentUTCDate = new Date('2025-04-10T20:00:00Z') // Thu before close time

    // When
    const result = getCloseDateForRegion(region, currentUTCDate)

    // Then
    expect(result.toUTCString()).toEqual('Wed, 09 Apr 2025 21:59:00 GMT') // summer 21:59 UTC
  })
})
