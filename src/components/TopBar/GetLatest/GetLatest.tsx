import {
  setLatestAsOfUtcResponse,
  setRegionExpand
} from '@/core/redux/features/report'
import {
  useAppDispatch,
  useAppSelect,
  useNotifications
} from '@/core/redux/hooks'
import { getLatestAsOfUtc } from '@/core/services/riskReport'
import { Button } from '@core-clib/react'

export function GetLatest() {
  const reportName = useAppSelect((state) => state.report.reportName)
  const isLiveModeEnabled = useAppSelect(
    (state) => state.report.isLiveModeEnabled
  )
  const dispatch = useAppDispatch()
  const { addDangerNotification } = useNotifications()

  const handleClick = async () => {
    try {
      const latestAsOfUtcResponse = await getLatestAsOfUtc({
        isLiveModeEnabled: true,
        reportName
      })

      dispatch(setLatestAsOfUtcResponse(latestAsOfUtcResponse))
      dispatch(setRegionExpand(false))
    } catch (error) {
      addDangerNotification(
        'Failed to update latest as of UTC. Please, try again later.'
      )
    }
  }

  return (
    <Button
      text=""
      className={`secondary minimal sync-button get-latest`}
      disabled={isLiveModeEnabled}
      onClick={handleClick}
      icon="ci-loop3"
    />
  )
}

export default GetLatest
