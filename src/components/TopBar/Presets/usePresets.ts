import {
  setExpandedGroupsIds,
  setPivotResultsColumnsOrdering
} from '@/core/redux/features/grid'
import { onExecutionTimeFilterRestoredFromPresetThunk } from '@/core/redux/features/quickFilters/executionTimeFilter/thunks/onExecutionTImeFilterRestoredFromPresetThunk'
import { setColumnsHeaderNames } from '@/core/redux/features/report'
import {
  setLastUsedPresetId,
  setUserPreferencesChanged
} from '@/core/redux/features/users'
import { useAppDispatch } from '@/core/redux/hooks'
import { ClibPreset } from '@/core/services/epic'
import { logger } from '@/core/utils/logger'
import {
  ColDef,
  ColGroupDef,
  ColumnApi,
  GridApi
} from '@ag-grid-community/core'
import { PresetsConfig } from '@core-clib/web-components'
import { isString, keyBy, mapValues } from 'lodash'

interface UsePresetsProps {
  gridApi: GridApi
  columnApi: Column<PERSON>pi
  lastUsedPresetIdRef: React.MutableRefObject<string | undefined>
}

export const usePresets = ({
  gridApi,
  columnApi,
  lastUsedPresetIdRef
}: UsePresetsProps) => {
  const dispatch = useAppDispatch()

  const config: PresetsConfig = {
    defaultPresetId: lastUsedPresetIdRef.current
  }

  const onBeforePresetSelect = async (preset: ClibPreset) => {
    const { id, state } = preset

    if (state.expandedGroupIds) {
      dispatch(setExpandedGroupsIds(state.expandedGroupIds))
    }

    if (state.executionTimeFilter) {
      dispatch(
        onExecutionTimeFilterRestoredFromPresetThunk(state.executionTimeFilter)
      )
    }

    if (state.headerNames) {
      const dictionary = mapValues(keyBy(state.headerNames, 'colId'), 'name') // { [colId]: name }

      dispatch(setColumnsHeaderNames(dictionary))
    }

    columnApi.setPivotMode(Boolean(state.isPivotMode))

    if (state.isPivotMode) {
      // If not set in a macro-task, the pivot columns will not be set.
      setTimeout(() => {
        columnApi.setPivotColumns(state.pivotColumns ?? [])
        columnApi.setPivotResultColumns(state.pivotResultsColumns ?? [])
        columnApi.setValueColumns(state.valueColumns ?? [])
        dispatch(
          setPivotResultsColumnsOrdering(
            (preset.state.pivotResultsColumns
              ?.map((c) => (c as ColDef).colId ?? (c as ColGroupDef).groupId)
              .filter(Boolean) as string[]) ?? null
          )
        )
        // Apply column state including pinned columns
        columnApi.applyColumnState({
          state: state.columnState,
          applyOrder: true
        })
      })
    } else {
      columnApi.resetColumnState()
      // If we switch between pivot and non-pivot, we should clear the pivot columns synchronously
      columnApi.setPivotColumns([])
      columnApi.setPivotResultColumns([])
      const valueColumns =
        columnApi
          .getColumns()
          ?.map((column) => column.getColDef().field)
          .filter(isString) ?? []
      columnApi.setValueColumns(valueColumns)
    }

    // Need to use refresh to force applying grid options.
    // Especially, we want to retrigger the `isServerSideGroupOpenByDefault` function that will open expanded groups according to the newly-selected preset state.
    logger.debug(`RefreshServerSide Purge: true`)
    gridApi.refreshServerSide({ purge: true })

    dispatch(setLastUsedPresetId(id))
    if (id !== lastUsedPresetIdRef.current) {
      lastUsedPresetIdRef.current = id
      dispatch(setUserPreferencesChanged(true))
    }

    // Force tool panel refresh with delay to ensure proper synchronization
    // This addresses issue where column names in sidebar don't update
    setTimeout(() => {
      gridApi.refreshToolPanel()
    }, 50)
  }

  return {
    config,
    onBeforePresetSelect
  }
}
