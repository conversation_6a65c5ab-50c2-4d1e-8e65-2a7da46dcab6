import { usePresetsStorage } from '@/components/TopBar/Presets/usePresetsStorage'
import {
  ClibPreset,
  ClibPresetToCreate,
  IPresetShareAccessType,
  IPresetShareType,
  IPresetState,
  IPresetsGroupId,
  IPresetsGroupName,
  IUserRole
} from '@/core/services/epic'
import {
  apiPaths,
  combineWrappers,
  createHandler,
  deepObjectContaining,
  getGridProviderWrapper,
  getInitializeDataWrapper,
  getReduxWrapper,
  handlers,
  setupMockServer,
  waitForCurrent,
  wrapWithUrl
} from '@/core/testing'
import {
  getClibPresetDummy,
  getClibPresetToCreateDummy,
  getColumnInfoDummy,
  getPresetDummy,
  getPresetShareDummy,
  getPresetStateDummy,
  getReportDetailsResponseDummy,
  getUserDummy,
  getUserProfileDummy,
  getUsersByRolesDummy
} from '@/core/testing/dummies'
import { PresetsStorage } from '@core-clib/web-components'
import { renderHook, waitFor } from '@testing-library/react'
import { rest } from 'msw'
import { vi } from 'vitest'

type HookResult =
  | { current: Required<PresetsStorage<IPresetState>> }
  | { error: Error }

describe('usePresetsStorage', () => {
  const server = setupMockServer(
    handlers.getUserPreference,
    handlers.getUserProfile,
    handlers.getUsersByRoles,
    handlers.latestAsOf,
    handlers.getReportDetails,
    handlers.getAllPresets,
    handlers.getMetadata,
    handlers.getDistinctColumnValues,
    handlers.getSummary,
    handlers.savePreset,
    handlers.saveUserPreference,
    handlers.sharePreset
  )

  const { wrapper: reduxWrapper, resetStore, store } = getReduxWrapper()
  const { wrapper: initializeDataWrapper } = getInitializeDataWrapper()
  const { wrapper: gridProviderWrapper } = getGridProviderWrapper()

  const url = '/reports/pnl-live'
  const wrapper = combineWrappers(
    wrapWithUrl(url),
    reduxWrapper,
    initializeDataWrapper,
    gridProviderWrapper
  )

  afterEach(() => {
    resetStore()
    vi.resetAllMocks()
  })

  describe('asserting a user id', () => {
    it('should throw an error when the user is has not been fetched', async () => {
      // given
      server.use(
        createHandler({
          method: 'get',
          path: apiPaths.getUserProfile,
          response: getUserProfileDummy({ UserId: '' }),
          status: 200
        })
      )

      const { result } = renderHook(usePresetsStorage, {
        wrapper
      }) as unknown as { result: HookResult }

      // then
      waitFor(() => {
        if ('error' in result) {
          expect(result.error).toEqual(
            Error(
              'The current user ID and grid schema should be fetched before!'
            )
          )
        }
      })
    })
  })

  describe('loading presets and groups', () => {
    it('should filter out removed presets', async () => {
      // given
      server.use(
        createHandler({
          method: 'get',
          path: apiPaths.getAllPresets,
          response: [
            getPresetDummy({ Id: 'id1', IsDeleted: true }),
            getPresetDummy({ Id: 'id2', IsDeleted: false })
          ]
        })
      )

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      const data = await result.current.presetsStorage?.loadPresetsAndGroups()

      // then
      expect(data?.presets).toHaveLength(1)
      expect(data?.presets).toMatchObject([
        {
          id: 'id2'
        }
      ])
    })

    // this test is flaky and should be fixed
    // Pipeline: src/components/TopBar/Presets/usePresetsStorage.test.ts:172:50
    // - Expected:
    // undefined
    // + Received:
    // false
    it('should filter out system columns from a preset state', async () => {
      // given
      const state = getPresetStateDummy({
        columnState: [
          { colId: 'Team' },
          { colId: 'TradingArea' },
          { colId: 'ContextActions' }
        ]
      })

      server.use(
        createHandler({
          method: 'get',
          path: apiPaths.getAllPresets,
          response: [getPresetDummy({ Data: getClibPresetDummy({ state }) })]
        })
      )

      server.use(
        createHandler({
          method: 'get',
          path: apiPaths.getReportDetails,
          response: getReportDetailsResponseDummy({
            columnInfo: [
              getColumnInfoDummy({ column: 'Team', isSystemData: false }),
              getColumnInfoDummy({
                column: 'TradingArea',
                isSystemData: false
              }),
              // The following column should be removed from the state in the returned data.
              getColumnInfoDummy({
                column: 'ContextActions',
                isSystemData: true
              })
            ]
          })
        })
      )

      const wrapper = combineWrappers(
        wrapWithUrl(url),
        reduxWrapper,
        initializeDataWrapper,
        gridProviderWrapper
      )

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      const data = await result.current.presetsStorage?.loadPresetsAndGroups()

      // then
      const columnState = data?.presets[0].state.columnState

      expect(columnState).toHaveLength(2)
      expect(columnState).toMatchObject([
        { colId: 'Team' },
        { colId: 'TradingArea' }
      ])
    })

    it('should map API data to clib personal and shared presets', async () => {
      // given
      const personalPresetDummy = getPresetDummy({
        Id: '123',
        PresetName: 'personal preset',
        SharedWith: null
      })
      const sharedPresetDummy = getPresetDummy({
        Id: '456',
        PresetName: 'shared preset',
        SharedWith: [getPresetShareDummy({ Type: IPresetShareType.Public })]
      })

      server.use(
        createHandler({
          method: 'get',
          path: apiPaths.getAllPresets,
          response: [personalPresetDummy, sharedPresetDummy]
        })
      )

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      const data = await result.current.presetsStorage?.loadPresetsAndGroups()

      // then
      expect(data?.presets).toMatchObject([
        { id: '123', name: 'personal preset', state: {} },
        {
          id: '456',
          name: 'shared preset',
          groupId: IPresetsGroupId.Shared,
          state: {}
        }
      ])
    })

    it('should return preset groups with read only set to false when a user is an admin', async () => {
      // given
      server.use(
        createHandler({
          method: 'get',
          path: apiPaths.getUserProfile,
          response: getUserProfileDummy({ UserId: 'AB1234' })
        }),
        createHandler({
          method: 'post',
          path: apiPaths.getUsersByRoles,
          response: getUsersByRolesDummy({
            [IUserRole.PresetAdmin]: [getUserDummy({ ADUserID: 'AB1234' })]
          })
        })
      )

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      const data = await result.current.presetsStorage?.loadPresetsAndGroups()

      // then
      await waitFor(() =>
        expect(data?.groups).toMatchObject([
          {
            id: IPresetsGroupId.Shared,
            name: IPresetsGroupName.SharedPresets,
            readOnly: false,
            showIfEmpty: true
          },
          {
            id: IPresetsGroupId.Personal,
            name: IPresetsGroupName.MyPresets,
            readOnly: false,
            showIfEmpty: true,
            allowSharing: true
          }
        ])
      )
    })

    it('should return preset groups with read only set to true when a user is not an admin', async () => {
      // given
      server.use(
        createHandler({
          method: 'get',
          path: apiPaths.getUserProfile,
          response: getUserProfileDummy({ UserId: 'AB1234' })
        }),
        createHandler({
          method: 'post',
          path: apiPaths.getUsersByRoles,
          response: getUsersByRolesDummy({
            [IUserRole.PresetAdmin]: [getUserDummy({ ADUserID: 'CD5678' })]
          })
        })
      )

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      const data = await result.current.presetsStorage?.loadPresetsAndGroups()

      // then
      expect(data?.groups).toMatchObject([
        {
          id: IPresetsGroupId.Shared,
          name: IPresetsGroupName.SharedPresets,
          readOnly: true,
          showIfEmpty: true
        },
        {
          id: IPresetsGroupId.Personal,
          name: IPresetsGroupName.MyPresets,
          readOnly: false,
          showIfEmpty: true,
          allowSharing: true
        }
      ])
    })
  })

  describe('creating a preset', () => {
    it('should filter out the payload from the system columns included in a preset state', async () => {
      // given
      const savePresetReqSpy = vi.fn()

      server.use(
        createHandler({
          method: 'post',
          path: apiPaths.savePreset,
          reqSpy: savePresetReqSpy
        })
      )

      server.use(
        createHandler({
          method: 'get',
          path: apiPaths.getReportDetails,
          response: getReportDetailsResponseDummy({
            columnInfo: [
              getColumnInfoDummy({ column: 'Team', isSystemData: false }),
              getColumnInfoDummy({
                column: 'TradingArea',
                isSystemData: false
              }),
              // The following column should be removed from the state in the `savePreset` payload.
              getColumnInfoDummy({
                column: 'ContextActions',
                isSystemData: true
              })
            ]
          })
        })
      )

      const presetToCreate: ClibPresetToCreate = getClibPresetToCreateDummy({
        state: getPresetStateDummy({
          columnState: [
            { colId: 'Team' },
            { colId: 'TradingArea' },
            { colId: 'ContextActions' }
          ]
        })
      })

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      await result.current.presetsStorage?.createPreset(presetToCreate)

      // then
      expect(savePresetReqSpy).toHaveBeenCalledTimes(1)
      expect(savePresetReqSpy).toHaveBeenCalledWith(
        deepObjectContaining({
          body: {
            PresetData: {
              state: {
                columnState: [{ colId: 'Team' }, { colId: 'TradingArea' }]
              }
            }
          }
        })
      )
    })

    it('should attach the app name, report type, preset name, and expanded groups ids to the payload', async () => {
      // given

      const savePresetReqSpy = vi.fn()

      server.use(
        createHandler({
          method: 'post',
          path: apiPaths.savePreset,
          reqSpy: savePresetReqSpy
        })
      )

      const presetToCreate = getClibPresetToCreateDummy({ name: 'New preset' })

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      await result.current.presetsStorage?.createPreset(presetToCreate)

      // then
      expect(savePresetReqSpy).toHaveBeenCalledTimes(1)
      expect(savePresetReqSpy).toHaveBeenCalledWith(
        deepObjectContaining({
          body: {
            AppName: 'MacroPortal',
            GridName: 'PnlLive',
            PresetName: 'New preset',
            PresetData: {
              state: {
                expandedGroupIds: []
              }
            }
          }
        })
      )
    })

    it('should attach the `PnlLive` report type to the payload when the report name is `PnlLiveBbgOverlay`', async () => {
      // given
      const savePresetReqSpy = vi.fn()

      server.use(
        createHandler({
          method: 'post',
          path: apiPaths.savePreset,
          reqSpy: savePresetReqSpy
        })
      )

      const bbgUrl = '/reports/pnl-live-bbg-overlay'
      const wrapper = combineWrappers(
        wrapWithUrl(bbgUrl),
        reduxWrapper,
        initializeDataWrapper,
        gridProviderWrapper
      )

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      await result.current.presetsStorage?.createPreset(
        getClibPresetToCreateDummy()
      )

      // then
      expect(savePresetReqSpy).toHaveBeenCalledTimes(1)
      expect(savePresetReqSpy).toHaveBeenCalledWith(
        deepObjectContaining({
          body: {
            GridName: 'PnlLive'
          }
        })
      )
    })

    it('should attach the app name, report type should be PnlLive, preset name, and expanded groups ids to the payload', async () => {
      // given

      const savePresetReqSpy = vi.fn()

      server.use(
        createHandler({
          method: 'post',
          path: apiPaths.savePreset,
          reqSpy: savePresetReqSpy
        })
      )

      const presetToCreate = getClibPresetToCreateDummy({ name: 'New preset' })

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      await result.current.presetsStorage?.createPreset(presetToCreate)

      // then
      expect(savePresetReqSpy).toHaveBeenCalledTimes(1)
      expect(savePresetReqSpy).toHaveBeenCalledWith(
        deepObjectContaining({
          body: {
            AppName: 'MacroPortal',
            GridName: 'PnlLive',
            PresetName: 'New preset',
            PresetData: {
              state: {
                expandedGroupIds: []
              }
            }
          }
        })
      )
    })

    it('should save the newly-created preset as the default one in user preferences', async () => {
      // given

      let reqObjSpy: any = {}
      // vi.resetAllMocks()
      const saveUserPreferenceReqSpy = vi.fn((obj) => {
        reqObjSpy = obj
      })

      server.use(
        createHandler({
          method: 'post',
          path: apiPaths.savePreset,
          response: getPresetDummy({
            Id: 'createdPresetId'
          })
        }),
        createHandler({
          method: 'post',
          path: apiPaths.saveUserPreference,
          reqSpy: saveUserPreferenceReqSpy
        })
      )

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      await result.current.presetsStorage?.createPreset(
        getClibPresetToCreateDummy()
      )

      // then
      await waitFor(() =>
        // wait for redux to process the action
        expect(
          store.getState().users.userPreferences.lastUsedPresetId?.length
        ).toBeGreaterThan(0)
      )
      expect(saveUserPreferenceReqSpy).toHaveBeenCalledTimes(1)
      expect(reqObjSpy.body.Key).toBe('pnl-live')
      expect(reqObjSpy.body.Data.lastUsedPresetId).toBe('createdPresetId')
    })

    it('should share the newly-created preset when the group id is shared', async () => {
      // given
      const sharePresetReqSpy = vi.fn()

      server.use(
        createHandler({
          method: 'post',
          path: apiPaths.savePreset,
          response: getPresetDummy({
            Id: 'createdPresetId'
          })
        }),
        createHandler({
          method: 'post',
          path: apiPaths.sharePreset,
          reqSpy: sharePresetReqSpy
        })
      )

      const presetToCreate = getClibPresetToCreateDummy({
        groupId: IPresetsGroupId.Shared
      })

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      await result.current.presetsStorage?.createPreset(presetToCreate)

      // then
      expect(sharePresetReqSpy).toHaveBeenCalledTimes(1)
      expect(sharePresetReqSpy).toHaveBeenCalledWith(
        deepObjectContaining({
          body: {
            Id: 'createdPresetId',
            Shares: [
              {
                AccessType: IPresetShareAccessType.Read,
                Type: IPresetShareType.Public,
                Names: []
              },
              {
                AccessType: IPresetShareAccessType.Write,
                Type: IPresetShareType.AZManRole,
                Names: [IUserRole.PresetAdmin]
              }
            ]
          }
        })
      )
    })

    it('should return the newly-created preset', async () => {
      // given
      const presetId = 'createdPresetId'

      const clibPreset = getClibPresetDummy({
        name: 'New preset',
        groupId: IPresetsGroupId.Personal,
        state: getPresetStateDummy({
          expandedGroupIds: ['All']
        }),
        order: 5
      })

      server.use(
        createHandler({
          method: 'post',
          path: apiPaths.savePreset,
          response: getPresetDummy({
            Id: presetId,
            Data: clibPreset
          })
        })
      )

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      const data = await result.current.presetsStorage?.createPreset(
        getClibPresetToCreateDummy()
      )

      // then
      expect(data).toMatchObject({
        id: 'createdPresetId',
        name: 'New preset',
        groupId: IPresetsGroupId.Personal,
        state: {
          expandedGroupIds: ['All']
        },
        order: 5
      })
    })

    describe('handling errors', () => {
      it('should throw an error when saving a preset fails', async () => {
        // given
        server.use(
          createHandler({
            method: 'post',
            path: apiPaths.savePreset,
            status: 500
          })
        )

        const { result } = renderHook(usePresetsStorage, { wrapper })

        // then
        await waitForCurrent(result)
        await expect(
          result.current.presetsStorage?.createPreset(
            getClibPresetToCreateDummy()
          )
        ).rejects.toThrow('Error while saving preset')
      })

      it('should throw an error when the sharing of newly-created preset fails', async () => {
        // given
        server.use(
          createHandler({
            method: 'post',
            path: apiPaths.sharePreset,
            status: 500
          })
        )

        const presetToCrate = getClibPresetToCreateDummy({
          groupId: IPresetsGroupId.Shared
        })

        const { result } = renderHook(usePresetsStorage, { wrapper })

        // then
        await waitForCurrent(result)
        await expect(
          result.current.presetsStorage?.createPreset(presetToCrate)
        ).rejects.toThrow('Error while saving preset')
      })
    })
  })

  describe('updating a preset', () => {
    it('should filter out the payload from the system columns included in the preset state', async () => {
      // given
      const savePresetReqSpy = vi.fn()

      server.use(
        createHandler({
          method: 'post',
          path: apiPaths.savePreset,
          reqSpy: savePresetReqSpy
        })
      )

      server.use(
        createHandler({
          method: 'get',
          path: apiPaths.getReportDetails,
          response: getReportDetailsResponseDummy({
            columnInfo: [
              getColumnInfoDummy({ column: 'Team', isSystemData: false }),
              getColumnInfoDummy({
                column: 'TradingArea',
                isSystemData: false
              }),
              // The following column should be removed from the state in the `savePreset` payload.
              getColumnInfoDummy({
                column: 'ContextActions',
                isSystemData: true
              })
            ]
          })
        })
      )

      const presetToUpdate = getClibPresetDummy({
        state: getPresetStateDummy({
          columnState: [
            { colId: 'Team' },
            { colId: 'TradingArea' },
            { colId: 'ContextActions' }
          ]
        })
      })

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      await result.current.presetsStorage?.updatePreset(
        'presetId',
        presetToUpdate,
        getClibPresetDummy()
      )

      // then
      await waitFor(() => {
        expect(savePresetReqSpy).toHaveBeenCalledTimes(1)
        expect(savePresetReqSpy).toHaveBeenCalledWith(
          deepObjectContaining({
            body: {
              PresetData: {
                state: {
                  columnState: [{ colId: 'Team' }, { colId: 'TradingArea' }]
                }
              }
            }
          })
        )
      })
    })

    it('should save a preset with the updated values', async () => {
      // given
      const savePresetReqSpy = vi.fn()

      server.use(
        createHandler({
          method: 'post',
          path: apiPaths.savePreset,
          reqSpy: savePresetReqSpy
        })
      )

      const originalPreset = getClibPresetDummy({
        name: 'Original name',
        groupId: IPresetsGroupId.Personal,
        order: 1
      })

      const updatedValues: Partial<ClibPreset> = {
        name: 'Updated name',
        groupId: IPresetsGroupId.Shared
      }

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      await result.current.presetsStorage?.updatePreset(
        'presetId',
        updatedValues,
        originalPreset
      )

      await waitFor(() => {
        // then
        expect(savePresetReqSpy).toHaveBeenCalledTimes(1)
        expect(savePresetReqSpy).toHaveBeenCalledWith(
          deepObjectContaining({
            body: {
              PresetData: {
                name: 'Updated name',
                groupId: IPresetsGroupId.Shared,
                order: 1
              }
            }
          })
        )
      })
    })

    it('should attach the app name, report type, preset id, preset name, and expanded groups ids to the payload', async () => {
      // given
      const savePresetReqSpy = vi.fn()

      server.use(
        createHandler({
          method: 'post',
          path: apiPaths.savePreset,
          reqSpy: savePresetReqSpy
        })
      )

      const presetId = 'presetId'
      const originalPreset = getClibPresetDummy({ name: 'My preset' })

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      await result.current.presetsStorage?.updatePreset(
        presetId,
        {},
        originalPreset
      )

      // then
      await waitFor(() => {
        expect(savePresetReqSpy).toHaveBeenCalledTimes(1)
        expect(savePresetReqSpy).toHaveBeenCalledWith(
          deepObjectContaining({
            body: {
              Id: 'presetId',
              AppName: 'MacroPortal',
              GridName: 'PnlLive',
              PresetName: 'My preset',
              PresetData: {
                state: {
                  expandedGroupIds: []
                }
              }
            }
          })
        )
      })
    })

    it('should attach the `PnlLive` report type to the payload when the report name is `PnlLiveBbgOverlay`', async () => {
      // given
      const savePresetReqSpy = vi.fn()

      server.use(
        createHandler({
          method: 'post',
          path: apiPaths.savePreset,
          reqSpy: savePresetReqSpy
        })
      )

      const bbgUrl = '/reports/pnl-live-bbg-overlay'
      const wrapper = combineWrappers(
        wrapWithUrl(bbgUrl),
        reduxWrapper,
        initializeDataWrapper,
        gridProviderWrapper
      )

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      await result.current.presetsStorage?.updatePreset(
        '',
        {},
        getClibPresetDummy()
      )

      await waitFor(() => {
        // then
        expect(savePresetReqSpy).toHaveBeenCalledTimes(1)
        expect(savePresetReqSpy).toHaveBeenCalledWith(
          deepObjectContaining({
            body: {
              GridName: 'PnlLive'
            }
          })
        )
      })
    })

    describe('handling errors', () => {
      it('should throw an error when saving a preset fails', async () => {
        // given
        server.use(
          createHandler({
            method: 'post',
            path: apiPaths.savePreset,
            status: 500
          })
        )

        const { result } = renderHook(usePresetsStorage, { wrapper })

        // then
        await waitForCurrent(result)
        await waitFor(async () => {
          await expect(
            result.current.presetsStorage?.updatePreset(
              'presetId',
              {},
              getClibPresetDummy()
            )
          ).rejects.toThrow('Error while updating preset')
        })
      })
    })
  })

  describe('deleting a preset', () => {
    it('should delete a preset by adding the preset id do the URL search params', async () => {
      // given
      const deletePresetReqSpy = vi.fn()

      server.use(
        createHandler({
          method: 'delete',
          path: apiPaths.deletePreset,
          reqSpy: deletePresetReqSpy
        })
      )

      const targetPresetId = '12345'

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      await result.current.presetsStorage?.deletePreset(targetPresetId)

      // then
      expect(deletePresetReqSpy).toHaveBeenCalledTimes(1)
      expect(deletePresetReqSpy).toHaveBeenCalledWith(
        deepObjectContaining({
          searchParams: {
            presetId: '12345'
          }
        })
      )
    })

    describe('handling errors', () => {
      it('should throw an error when deleting a preset fails', async () => {
        // given
        server.use(
          createHandler({
            method: 'delete',
            path: apiPaths.deletePreset,
            status: 500
          })
        )

        const { result } = renderHook(usePresetsStorage, { wrapper })

        // then
        await waitForCurrent(result)
        await expect(
          result.current.presetsStorage?.deletePreset('presetId')
        ).rejects.toThrow('Error while deleting preset')
      })
    })
  })

  describe('getting users', () => {
    it('should return preset users alphabetically without duplications', async () => {
      // given
      server.use(
        createHandler({
          method: 'post',
          path: apiPaths.getUsersByRoles,
          response: getUsersByRolesDummy({
            [IUserRole.PreviewAccess]: [
              getUserDummy({
                ADUserID: 'AB12345',
                EmployeeFirstName: 'John',
                EmployeeLastName: 'Smith'
              }),
              getUserDummy({
                ADUserID: 'EF23456',
                EmployeeFirstName: 'Alice',
                EmployeeLastName: 'Johnson'
              }),
              getUserDummy({
                ADUserID: 'CD67891',
                EmployeeFirstName: 'John',
                EmployeeLastName: 'Doe'
              }),
              getUserDummy({ ADUserID: 'AB12345' })
            ]
          })
        })
      )

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      const data = await result.current.presetsStorage?.getUsers?.()

      expect(data).toHaveLength(3)
      expect(data?.[0]).toStrictEqual({
        name: 'Alice Johnson',
        id: 'EF23456'
      })
      expect(data?.[1]).toStrictEqual({
        name: 'John Doe',
        id: 'CD67891'
      })
      expect(data?.[2]).toStrictEqual({
        name: 'John Smith',
        id: 'AB12345'
      })
    })
  })

  describe('sharing a preset', () => {
    it('should share a preset with specified user ids by adding the preset id do the URL search params', async () => {
      // given
      const sendPresetCopyReqSpy = vi.fn()

      server.use(
        rest.post(`${apiPaths.sendPresetCopy}`, (req, res, ctx) => {
          const { searchParams } = req.url

          const { body } = req
          sendPresetCopyReqSpy({
            searchParams: Object.fromEntries(searchParams.entries()),
            // searchParams: { presetId: searchParams.get('presetId') },
            body
          })
          return res(ctx.status(200))
        })
      )

      const targetPresetId = '6789'
      const targetUserIds = ['user1', 'user2', 'user3']

      const { result } = renderHook(usePresetsStorage, { wrapper })

      // then
      await waitForCurrent(result)

      // when
      await result.current.presetsStorage?.sharePreset?.(
        getClibPresetDummy({ id: targetPresetId }),
        targetUserIds
      )

      // then
      expect(sendPresetCopyReqSpy).toHaveBeenCalledTimes(1)
      expect(sendPresetCopyReqSpy).toHaveBeenCalledWith(
        deepObjectContaining({
          searchParams: {
            presetId: '6789'
          },
          body: ['user1', 'user2', 'user3']
        })
      )
    })

    describe('handling errors', () => {
      it('should throw an error when sharing a preset fails', async () => {
        server.use(
          rest.post(`${apiPaths.sendPresetCopy}`, (_, res, ctx) => {
            return res(ctx.status(500))
          })
        )

        const { result } = renderHook(usePresetsStorage, { wrapper })

        // then
        await waitForCurrent(result)
        await expect(
          result.current.presetsStorage?.sharePreset?.(getClibPresetDummy(), [])
        ).rejects.toThrow('Error while sharing preset')
      })
    })
  })
})
