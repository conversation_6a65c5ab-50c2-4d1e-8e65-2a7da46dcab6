import { Column<PERSON>pi, <PERSON>rid<PERSON><PERSON> } from '@ag-grid-community/core'
import { Presets as CoreClibPresets } from '@core-clib/react'
import { memo, useEffect, useState } from 'react'
import { usePresets } from './usePresets'
import { usePresetsStorage } from './usePresetsStorage'
import { useAgGridStateManager } from '@/components/TopBar/Presets/useAgGridStateManager'

interface PresetsProps {
  gridApi: GridApi
  columnApi: ColumnApi
  lastUsedPresetIdRef: React.MutableRefObject<string | undefined>
}

// Avoid rerender of this component.
// If it rerenders on create preset, you might not see new preset in the list
// the problem is that Presets component rerenders (on react side) with new config (defaultPresetId) and list of all presets that does not have this ID,
// its triggered by selector
// useAppSelect(selectLastUsedPresetId) in usePreset
// everything works without rerender because Presets are in fact web component behind shadow DOM and it manages its own render cycle there
//
// But, in the April 2024 we tested it and it appeared to also work without memo - inconclusive
// Leaving memo in place for not. To be re-evaluated in the future. Do not remove memo without thorough re-testing of MACROUIUX-1256
export const Presets: React.FC<PresetsProps> = memo(
  ({ gridApi, columnApi, lastUsedPresetIdRef }) => {
    const [isInitialized, setIsInitialized] = useState(false)
    const { config, onBeforePresetSelect } = usePresets({
      gridApi,
      columnApi,
      lastUsedPresetIdRef
    })

    const agGridStateManager = useAgGridStateManager(gridApi, columnApi)

    const { presetsStorage } = usePresetsStorage()
    // We need to put rendering of the component into macro task because it has undefined behavior
    // when it is rendered in the same microtask as the agGridStateManager is created.
    // Guess the problem is in SDK component agGridStateManager.getState().filterModel is null
    // Initialization of user preferences first is not working for this case.
    useEffect(() => {
      // wait for agGridStateManager to be initialized
      setTimeout(() => setIsInitialized(true), 500)
    }, [agGridStateManager])

    if (!isInitialized || !presetsStorage) {
      return null
    }

    return (
      <CoreClibPresets
        data-testid="presets-component"
        presetsStorage={presetsStorage}
        stateManager={agGridStateManager}
        onBeforePresetSelect={(event) => onBeforePresetSelect(event.preset)}
        config={config}
      />
    )
  }
)
