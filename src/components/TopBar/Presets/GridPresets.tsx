import { useGridApi } from '@/components/Grid/hooks'
import { Presets } from './Presets'
import { useAppSelect } from '@/core/redux/hooks'
import { useLastUsedPresetId } from '@/core/redux/features/users/selectors/useLastUsedPresetId'
import { useEffect, useRef, useState } from 'react'
import { GridApi, ColumnApi } from '@ag-grid-community/core'

export const GridPresets: React.FC = () => {
  // Since gridApiRef is reference, it does not trigger rerender and Preset component
  // doesn't load unless we keep these reactive state variables
  // and update them via the effect. Revisit for a later cleanup.
  const [gridApi, setGridApi] = useState<GridApi | null>(null)
  const [columnApi, setColumnApi] = useState<ColumnApi | null>(null)

  const { gridApiRef } = useGridApi()
  useEffect(() => {
    if (gridApiRef.current && gridApiRef.current.api) {
      setGridApi(gridApiRef.current.api)
      setColumnApi(gridApiRef.current.columnApi)
    }
  }, [gridApiRef.current])

  const lastUsedPresetId = useLastUsedPresetId()
  // We need to keep lastUsedPresetId in ref to avoid new Preset (created) invisible in UI
  const lastUsedPresetIdRef = useRef(lastUsedPresetId)
  const isReady = useAppSelect((state) => state.presets.isReady)

  if (!isReady || !gridApi || !columnApi) {
    return null
  } else {
    lastUsedPresetIdRef.current = lastUsedPresetId
  }
  return (
    <Presets
      gridApi={gridApi}
      columnApi={columnApi}
      lastUsedPresetIdRef={lastUsedPresetIdRef}
    />
  )
}
