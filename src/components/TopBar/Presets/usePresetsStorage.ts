import { useGridApi, useGridUtils } from '@/components/Grid/hooks'
import { setPivotResultsColumnsOrdering } from '@/core/redux/features/grid'
import {
  addPreset,
  removePreset,
  updatePreset
} from '@/core/redux/features/presets'
import {
  setLastUsedPresetId,
  setUserPreferencesChanged
} from '@/core/redux/features/users'
import { useAppDispatch, useAppSelect } from '@/core/redux/hooks'
import { RootState, store } from '@/core/redux/store'
import {
  IPreset,
  IPresetToUpdate,
  getPresetsStorage
} from '@/core/services/epic'
import { useCallback, useMemo } from 'react'

export const usePresetsStorage = () => {
  const reportType = useAppSelect((state) => state.report.reportType)
  const columnInfo = useAppSelect((state) => state.report.columnInfo)
  const userPresets = useAppSelect((state) => state.presets.userPresets)
  const sharedPresets = useAppSelect((state) => state.presets.sharedPresets)
  const currentUserId = useAppSelect((state) => state.users.profile?.UserId)
  const usersByRoles = useAppSelect((state) => state.users.usersByRoles)
  const { gridApi } = useGridApi()

  // To prevent re-rendering because of changing the state
  // we should use useCallback to pick up executionTimeFilter as a callback function
  const getExecutionTimeFilter = useCallback(
    () => (store.getState() as RootState).executionTimeFilter,
    []
  )

  const {
    getExpandedGroupsIds,
    getPivotColumns,
    getPivotResultsColumns,
    getValueColumns,
    isPivotMode,
    getPivotColumnsOrdering
  } = useGridUtils()

  const dispatch = useAppDispatch()

  const onPresetCreated = useCallback(
    (preset: IPreset) => {
      dispatch(addPreset(preset))
      dispatch(setLastUsedPresetId(preset.Id))
      dispatch(setUserPreferencesChanged(true))
    },
    [dispatch]
  )

  const onPresetDeleted = useCallback(
    (presetId: string) => {
      dispatch(removePreset(presetId))
    },
    [dispatch]
  )

  const onPresetUpdated = useCallback(
    (preset: IPresetToUpdate) => {
      const currentOrdering = getPivotColumnsOrdering()

      if (currentOrdering) {
        dispatch(setPivotResultsColumnsOrdering(currentOrdering))
      }

      dispatch(updatePreset(preset))
    },
    [dispatch]
  )

  const presetsStorage = useMemo(
    () =>
      currentUserId && reportType
        ? getPresetsStorage({
            gridApi,
            userId: currentUserId,
            reportType,
            onPresetCreated,
            onPresetDeleted,
            onPresetUpdated,
            columnInfo,
            getExpandedGroupsIds,
            userPresets,
            sharedPresets,
            usersByRoles,
            getExecutionTimeFilter,
            getPivotColumns,
            getPivotResultsColumns,
            getValueColumns,
            isPivotMode
          })
        : undefined,
    [
      currentUserId,
      reportType,
      columnInfo,
      onPresetCreated,
      getExpandedGroupsIds,
      userPresets,
      sharedPresets,
      usersByRoles,
      getExecutionTimeFilter
    ]
  )

  return {
    presetsStorage
  }
}
