import { IUserPreferenceKey } from '@/core/services/epic'
import {
  apiPaths,
  columnApiMock,
  combineWrappers,
  createHandler,
  deepObjectContaining,
  getGridApiMock,
  getInitializeAppWrapper,
  getInitializeDataWrapper,
  getReduxWrapper,
  gridApiMock,
  handlers,
  setupMockServer,
  waitForCurrent,
  wrapWithUrl
} from '@/core/testing'
import {
  getClibPresetDummy,
  getPresetStateDummy,
  getUserPreferenceResponseDummy
} from '@/core/testing/dummies'
import { act, renderHook, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { usePresets } from './usePresets'

describe('usePresets', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  const url = '/reports/pnl-live'
  const server = setupMockServer(
    handlers.latestAsOf,
    handlers.getReportDetails,
    handlers.getSummary,
    handlers.getMetadata,
    handlers.getDistinctColumnValues,
    handlers.getUserPreference,
    handlers.saveUserPreference,
    handlers.getUserProfile,
    handlers.getAllPresets,
    handlers.getUsersByRoles,
    handlers.getClientDetails,
    handlers.connectAggregatorWebSocket
  )

  const gridApi = getGridApiMock()
  const refMock = {
    current: 'defaultPresetId'
  } as any

  it('should return config with the default preset id fetched from the API', async () => {
    // given
    server.use(
      createHandler({
        method: 'get',
        path: apiPaths.getUserPreference,
        response: getUserPreferenceResponseDummy({
          Data: {
            [IUserPreferenceKey.LastUsedPresetId]: 'defaultPresetId',
            [IUserPreferenceKey.IsSummaryBarCollapsed]: false
          }
        })
      })
    )

    // when
    const { result } = renderHook(
      () =>
        usePresets({
          gridApi,
          lastUsedPresetIdRef: refMock,
          columnApi: columnApiMock
        }),

      getInitializeAppWrapper(url)
    )

    // then
    await waitForCurrent(result)
    expect(result.current.config).toStrictEqual({
      defaultPresetId: 'defaultPresetId'
    })
  })

  describe('before preset selecting', () => {
    describe('applying custom preset state', () => {
      it('should expand groups when they are present in the preset state', async () => {
        // given
        const { wrapper: reduxWrapper, store } = getReduxWrapper()
        const { wrapper: initializeDataWrapper } = getInitializeDataWrapper()

        const wrapper = combineWrappers(
          wrapWithUrl(url),
          reduxWrapper,
          initializeDataWrapper
        )

        const { result } = renderHook(
          () =>
            usePresets({
              gridApi,
              lastUsedPresetIdRef: refMock,
              columnApi: columnApiMock
            }),
          {
            wrapper
          }
        )

        // then
        await waitForCurrent(result)

        // when
        await waitFor(() =>
          result.current.onBeforePresetSelect(
            getClibPresetDummy({
              state: getPresetStateDummy({ expandedGroupIds: ['MELL', 'DECA'] })
            })
          )
        )

        // then
        expect(store.getState().grid.expandedGroupsIds).toStrictEqual([
          'MELL',
          'DECA'
        ])

        // when
        await waitFor(() =>
          result.current.onBeforePresetSelect(
            getClibPresetDummy({
              state: getPresetStateDummy({ expandedGroupIds: undefined })
            })
          )
        )

        // then
        // The `expandedGroupIds` should remain the same because the recent preset didn't contain it in its custom state.
        expect(store.getState().grid.expandedGroupsIds).toStrictEqual([
          'MELL',
          'DECA'
        ])

        // when
        await waitFor(() =>
          result.current.onBeforePresetSelect(
            getClibPresetDummy({
              state: getPresetStateDummy({ expandedGroupIds: [] })
            })
          )
        )

        // then
        expect(store.getState().grid.expandedGroupsIds).toStrictEqual([])
      })

      it('should save column header names when they are present in the preset state', async () => {
        // given
        const { wrapper: reduxWrapper, store } = getReduxWrapper()
        const { wrapper: initializeDataWrapper } = getInitializeDataWrapper()

        const wrapper = combineWrappers(
          wrapWithUrl(url),
          reduxWrapper,
          initializeDataWrapper
        )

        const { result } = renderHook(
          () =>
            usePresets({
              gridApi,
              columnApi: columnApiMock,
              lastUsedPresetIdRef: refMock
            }),
          {
            wrapper
          }
        )

        // when
        await waitForCurrent(result)
        await result.current.onBeforePresetSelect(
          getClibPresetDummy({
            state: getPresetStateDummy({
              headerNames: [
                { colId: 'Team', name: 'Edited Team' },
                { colId: 'TradingArea', name: 'Trading Area' }
              ]
            })
          })
        )

        // then
        expect(store.getState().report.columnsHeaderNames).toStrictEqual({
          Team: 'Edited Team',
          TradingArea: 'Trading Area'
        })
      })
    })

    // This test is flaky. It fails randomly.
    // src/components/TopBar/Presets/usePresets.test.tsx:135:50
    // on await waitForCurrent(result)
    it('should refresh server side rows', async () => {
      // when
      const { result } = renderHook(
        () =>
          usePresets({
            gridApi,
            lastUsedPresetIdRef: refMock,
            columnApi: columnApiMock
          }),

        getInitializeAppWrapper(url)
      )

      // then
      await waitForCurrent(result)

      // when
      act(() => {
        waitFor(() => result.current.onBeforePresetSelect(getClibPresetDummy()))
      })
      // then
      expect(gridApiMock.refreshServerSide).toBeCalledTimes(1)
      expect(gridApiMock.refreshServerSide).toHaveBeenCalledWith({
        purge: true
      })
    })

    it('should save newly-created preset as the default one in user preferences', async () => {
      // given
      const saveUserPreferenceReqSpy = vi.fn()

      server.use(
        createHandler({
          method: 'post',
          path: apiPaths.saveUserPreference,
          reqSpy: saveUserPreferenceReqSpy
        })
      )
      const { wrapper, store } = getInitializeAppWrapper(url)
      const presetId = 'newDefaultPresetId'

      // when
      const { result } = renderHook(
        () =>
          usePresets({
            gridApi,
            lastUsedPresetIdRef: refMock,
            columnApi: columnApiMock
          }),
        {
          wrapper
        }
      )

      // then
      await waitForCurrent(result)
      // when
      act(() => {
        result.current.onBeforePresetSelect(
          getClibPresetDummy({ id: presetId })
        )
      })

      // then
      await waitFor(() =>
        expect(store.getState().users.userPreferences.lastUsedPresetId).toBe(
          presetId
        )
      )
      expect(saveUserPreferenceReqSpy).toHaveBeenCalledTimes(1)
      expect(saveUserPreferenceReqSpy).toHaveBeenCalledWith(
        deepObjectContaining({
          body: {
            Data: {
              [IUserPreferenceKey.LastUsedPresetId]: presetId
            },
            Key: 'pnl-live'
          }
        })
      )
    })
  })
})
