import { GridPresets } from '@/components/TopBar/Presets'
import { setIsSummaryBarCollapsed } from '@/core/redux/features/users'
import { usePresetNameForTitle } from '@/core/redux/features/users/selectors/usePresetNameForTitle'
import { componentVisibilitySelector } from '@/core/redux/features/users/selectors/componentVisibilitySelector'
import { useAppDispatch, useAppSelect } from '@/core/redux/hooks'
import {
  App<PERSON>ead<PERSON>,
  Collapse,
  PageHeader,
  PageHeaderItem,
  PageHeaderItems
} from '@core-clib/react'
import { useCallback } from 'react'
import { HeaderScrollPersist } from '../Shared/HeaderScrollPersist'
import { CloseTimeButton } from './CloseTimeButton'
import style from './CloseTimeButton/CloseTimeButton.module.scss'
import { CurrentAsOfLocal } from './CurrentAsOfLocal'
import { LiveUpdate } from './LiveUpdate'
import { Metadata } from './Metadata'
import { ReposDropdown } from './ReposDropdown'
import { RiskReportSummaryHeader } from './RiskReportSummaryHeader'
import './topBar.scss'
import { TradingArea } from './TradingArea/TradingArea'
import { isConnectedSubscriberSelector } from '@/core/redux/selectors'
import { KnowledgeAsOf } from './KnowledgeDates'

export const TopBar: React.FC = () => {
  // Set Application Title with Preset Name
  usePresetNameForTitle()
  const isSummaryBarCollapsed = useAppSelect((state) =>
    Boolean(state.users.userPreferences?.isSummaryBarCollapsed)
  )
  const isConnectedSubscriber = useAppSelect(isConnectedSubscriberSelector)
  const dispatch = useAppDispatch()

  const handleCollapseChanged = useCallback(
    (isBarCollapsed: boolean) => {
      dispatch(setIsSummaryBarCollapsed(isBarCollapsed))
    },
    [dispatch]
  )

  const componentVisibilityState = useAppSelect(componentVisibilitySelector)

  return (
    <>
      <div
        data-testid="topheader-summary"
        id="top-app-header-summary"
        className={`top-app-header-summary ${
          componentVisibilityState?.isAppHeaderVisible ? '' : 'hidden'
        }`}
      >
        <HeaderScrollPersist storageKey="TopBar">
          <AppHeader isHideShortcutEnabled={true} isVisible={true}>
            <PageHeaderItems>
              <div className="item-wrapper">
                <PageHeaderItem>
                  <GridPresets />
                </PageHeaderItem>
              </div>
              <div className="item-wrapper">
                <PageHeaderItem>
                  <TradingArea />
                </PageHeaderItem>
              </div>
              {!isConnectedSubscriber && (
                <div className={`item-wrapper ${style.currentAsOfWrapper}`}>
                  <CurrentAsOfLocal />
                </div>
              )}
              {!isConnectedSubscriber && (
                <>
                  <KnowledgeAsOf />
                  <div className={`item-wrapper ${style.closeToRegionWrapper}`}>
                    <PageHeaderItem>
                      <CloseTimeButton />
                    </PageHeaderItem>
                  </div>
                </>
              )}
              <div className={`item-wrapper ${style.liveUpdateWrapper}`}>
                <PageHeaderItem>
                  <LiveUpdate />
                </PageHeaderItem>
              </div>
              <Metadata />
            </PageHeaderItems>
          </AppHeader>
        </HeaderScrollPersist>
      </div>
      {componentVisibilityState?.isSummaryBarVisible && (
        <Collapse
          direction="top"
          onCollapseChanged={handleCollapseChanged}
          isCollapse={isSummaryBarCollapsed}
          data-testid="collapsebar"
        >
          <HeaderScrollPersist storageKey="SummaryBar">
            <PageHeader>
              <RiskReportSummaryHeader />
              <ReposDropdown />
            </PageHeader>
          </HeaderScrollPersist>
        </Collapse>
      )}
    </>
  )
}
