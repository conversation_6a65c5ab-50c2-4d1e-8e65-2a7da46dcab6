// Generated by CodiumAI

import { ReduxWrapper } from '@/core/testing'
import { renderHook, waitFor } from '@testing-library/react'
import { act } from 'react-dom/test-utils'
import { useLiveUpdate } from './useLiveUpdate'

describe('useLiveUpdate', () => {
  it('should return an object with isLiveModeEnabled and onCheckLiveUpdate properties', () => {
    const { result } = renderHook(() => useLiveUpdate(), {
      wrapper: ReduxWrapper({
        report: {
          isLiveModeEnabled: false,
          lastAsOfUtcMetadata: {
            receivedTime: null,
            error: null
          }
        }
      })
    })

    expect(result.current.isLiveModeEnabled).toBeFalsy()
    expect(result.current).toHaveProperty('onCheckLiveUpdate')
  })

  it('should set isLiveModeEnabled state when onCheckLiveUpdate is called', async () => {
    const { result } = renderHook(() => useLiveUpdate(), {
      wrapper: ReduxWrapper({
        report: {
          isLiveModeEnabled: true,
          lastAsOfUtcMetadata: {
            receivedTime: null,
            error: null
          }
        }
      })
    })
    act(() => {
      result.current.onCheckLiveUpdate(true)
    })
    expect(result.current.isLiveModeEnabled).toBe(true)

    act(() => {
      result.current.onCheckLiveUpdate(false)
    })

    await waitFor(() => {
      expect(result.current.isLiveModeEnabled).toBe(false)
    })
  })

  it('should detect delay', () => {
    const { result } = renderHook(() => useLiveUpdate(), {
      wrapper: ReduxWrapper({
        report: {
          isLiveModeEnabled: true,
          lastAsOfUtcMetadata: {
            receivedTime: null,
            error: {
              retryAfterSeconds: 3
            }
          }
        }
      })
    })

    expect(result.current.isDelay).toBeTruthy()
    expect(result.current.liveModeStatus).toBeTruthy()
  })

  it('should detect status Off', () => {
    const { result } = renderHook(() => useLiveUpdate(), {
      wrapper: ReduxWrapper({
        report: {
          isLiveModeEnabled: false,
          lastAsOfUtcMetadata: {
            receivedTime: null,
            error: null
          }
        }
      })
    })

    expect(result.current.liveModeStatus).toBe('Off')
  })

  it('should detect status On', () => {
    const { result } = renderHook(() => useLiveUpdate(), {
      wrapper: ReduxWrapper({
        report: {
          isLiveModeEnabled: true,
          lastAsOfUtcMetadata: {
            receivedTime: null,
            error: null
          }
        }
      })
    })

    expect(result.current.liveModeStatus).toBe('On')
    expect(result.current.liveUpdateDelayType).toBeUndefined()
  })

  it('should detect status disconnected', () => {
    const { result } = renderHook(() => useLiveUpdate(), {
      wrapper: ReduxWrapper({
        report: {
          isLiveModeEnabled: true,
          lastAsOfUtcMetadata: {
            receivedTime: null,
            error: {
              retryAfterSeconds: 33
            }
          }
        }
      })
    })

    expect(result.current.liveModeStatus).toBe('disconnected')
  })

  it('should detect status >5 min older than current system time', () => {
    const { result } = renderHook(() => useLiveUpdate(), {
      wrapper: ReduxWrapper({
        report: {
          isLiveModeEnabled: true,
          lastAsOfUtcMetadata: {
            receivedTime: new Date(Date.now() - 1000 * 60 * 6).toISOString(),
            error: null
          }
        }
      })
    })

    expect(result.current.liveUpdateDelayMessage).toBe(
      'Data is >5 min older than current system time.'
    )
  })

  it('should detect status >15 min older than current system time', () => {
    const { result } = renderHook(() => useLiveUpdate(), {
      wrapper: ReduxWrapper({
        report: {
          isLiveModeEnabled: true,
          lastAsOfUtcMetadata: {
            receivedTime: new Date(Date.now() - 1000 * 60 * 16).toISOString(),
            error: null
          }
        }
      })
    })

    expect(result.current.liveUpdateDelayMessage).toBe(
      'Data is >15 min older than current system time.'
    )
  })
})
