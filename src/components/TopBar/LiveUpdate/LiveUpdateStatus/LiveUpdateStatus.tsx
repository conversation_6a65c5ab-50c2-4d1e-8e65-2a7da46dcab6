import { WarningIcon } from '@/components/Shared/Icons/Warning'
import GetLatest from '@/components/TopBar/GetLatest/GetLatest'
import { useLiveUpdate } from '../useLiveUpdate'
import { Spinner } from '@core-clib/react'
import { TooltipWrapper } from '@core-clib/react'
import { TooltipVariant } from '@core-clib/web-components'
import { useEffect, useState } from 'react'
import { useAppSelect } from '@/core/redux/hooks'
import { useAfterRefreshEffect } from '@/core/hooks/useAfterRefreshEffect'
import { isConnectedSubscriberSelector } from '@/core/redux/selectors'

export const LiveUpdateStatus = () => {
  const isConnectedSubscriber = useAppSelect(isConnectedSubscriberSelector)
  const {
    isLiveModeEnabled,
    isLoading,
    isDelay,
    liveUpdateDelayType,
    liveUpdateDelayMessage,
    liveModeStatus
  } = useLiveUpdate()

  const [isRefreshing, setIsRefreshing] = useState(isLoading)

  useEffect(() => {
    if (isLoading) {
      setIsRefreshing(true)
    }
  }, [isLoading])

  useAfterRefreshEffect(() => {
    setIsRefreshing(false)
  })

  if (!isDelay && isRefreshing) {
    return (
      <>
        <span>{liveModeStatus}</span>
        <Spinner />
      </>
    )
  }

  if ((isLiveModeEnabled && liveUpdateDelayType) || isDelay) {
    return (
      <>
        <span>{liveModeStatus}</span>
        <TooltipWrapper
          text={liveUpdateDelayMessage}
          variant={TooltipVariant.ERROR}
        >
          <WarningIcon variant={liveUpdateDelayType} />
        </TooltipWrapper>
      </>
    )
  } else if (!isLiveModeEnabled) {
    return (
      <>
        <span>{liveModeStatus}</span>
        {!isConnectedSubscriber && <GetLatest />}
      </>
    )
  }

  return <span>{liveModeStatus}</span>
}
