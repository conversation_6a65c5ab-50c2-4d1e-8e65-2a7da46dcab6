import { render, screen } from '@testing-library/react'
import { LiveUpdateStatus } from './LiveUpdateStatus'
import { vi } from 'vitest'
// @ts-ignore
import { mockUseLiveUpdate } from '../useLiveUpdate'
import { renderWithReduxInitialState } from '@/core/testing'

vi.mock('../useLiveUpdate', () => {
  const mockUseLiveUpdate = vi.fn()
  return {
    mockUseLiveUpdate,
    useLiveUpdate: mockUseLiveUpdate
  }
})

vi.mock('@core-clib/react', async () => {
  const mockTooltipWrapper = vi.fn(({ text }) => <div>{text}</div>)
  const actual = await vi.importActual('@core-clib/react')
  return {
    ...actual,
    TooltipWrapper: mockTooltipWrapper,
    Spinner: () => <div>spinner</div>
  }
})

describe('LiveUpdateStatus', () => {
  it('renders live mode status when not delayed and loading', () => {
    mockUseLiveUpdate.mockReturnValue({
      isLiveModeEnabled: true,
      isLoading: true,
      isDelay: false,
      liveUpdateDelayType: 'warning',
      liveUpdateDelayMessage: 'Delay message',
      liveModeStatus: 'Live Mode Status'
    })
    renderWithReduxInitialState(<LiveUpdateStatus />)

    expect(screen.getByText('Live Mode Status')).toBeInTheDocument()
    expect(screen.getByText('spinner')).toBeInTheDocument()
  })

  it('renders live mode status with warning icon and tooltip when delayed', () => {
    mockUseLiveUpdate.mockReturnValue({
      isLiveModeEnabled: true,
      isLoading: false,
      isDelay: true,
      liveUpdateDelayType: 'warning',
      liveUpdateDelayMessage: 'Delay message',
      liveModeStatus: 'Live Mode Status'
    })
    renderWithReduxInitialState(<LiveUpdateStatus />)

    expect(screen.getByText('Live Mode Status')).toBeInTheDocument()
    expect(screen.getByText('Delay message')).toBeInTheDocument()
  })

  it('renders live mode status without warning icon and tooltip when not delayed', () => {
    mockUseLiveUpdate.mockReturnValue({
      isLiveModeEnabled: true,
      isLoading: false,
      isDelay: false,
      liveModeStatus: 'Live Mode Status'
    })
    renderWithReduxInitialState(<LiveUpdateStatus />)

    expect(screen.getByText('Live Mode Status')).toBeInTheDocument()
    expect(screen.queryByText('spinner')).not.toBeInTheDocument()
  })
})
