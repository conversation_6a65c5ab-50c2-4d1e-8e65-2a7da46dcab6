.core-sdk-styles core-clib-switch.switch-warning input:checked + .slider {
  background-color: var(--core-yellow);
}

.core-sdk-styles core-clib-switch.switch-error input:checked + .slider {
  background-color: var(--core-red);
}

.core-sdk-styles core-clib-switch.switch-ok input:checked + .slider {
  background-color: var(--core-green);
}
.core-sdk-styles core-clib-switch.switch-disconnected input:checked + .slider {
  background-color: var(--core-green);
  filter: brightness(75%);
}

.switch-disconnected + span {
  color: hsl(0, 0%, 50%);
}
