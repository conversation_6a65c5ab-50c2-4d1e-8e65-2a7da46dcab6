import {
  startLatestAsOfUtcListener,
  stopLatestAsOfUtcListener
} from '@/core/redux/features/report'
import { useAppDispatch, useAppSelect } from '@/core/redux/hooks'
import {
  isLoadingsSelector,
  isLiveModeOrParentLiveMode
} from '@/core/redux/selectors'
import {
  isDateOlderThanFifteenMinutes,
  isDateOlderThanFiveMinutes
} from '@/core/utils/date'

export const useLiveUpdate = () => {
  const isLiveModeEnabled = useAppSelect(isLiveModeOrParentLiveMode)

  const isLoading = useAppSelect(isLoadingsSelector)
  const receivedTime = useAppSelect(
    (state) => state.report.lastAsOfUtcMetadata.receivedTime
  )
  const error = useAppSelect((state) => state.report.lastAsOfUtcMetadata.error)
  const liveUpdateDelayType = useAppSelect(
    (state) => state.report.liveUpdateDelayType
  )
  const dispatch = useAppDispatch()
  let liveModeStatus = isLiveModeEnabled ? 'On' : 'Off'
  let liveUpdateDelayMessage = ''

  const isDelay = Boolean(
    error?.retryAfterSeconds && error?.retryAfterSeconds > 2
  )

  const onCheckLiveUpdate = (checked: boolean) => {
    if (checked) {
      dispatch(startLatestAsOfUtcListener())
    } else {
      dispatch(stopLatestAsOfUtcListener())
    }
  }

  if (isDelay) {
    liveUpdateDelayMessage =
      'There are issues connecting to the backend server.'

    if (error?.retryAfterSeconds && error?.retryAfterSeconds > 32) {
      liveModeStatus = 'disconnected'
    }
  }

  if (
    receivedTime &&
    isDateOlderThanFiveMinutes(receivedTime) &&
    !liveUpdateDelayType
  ) {
    liveUpdateDelayMessage = 'Data is >5 min older than current system time.'
  }

  if (receivedTime && isDateOlderThanFifteenMinutes(receivedTime)) {
    liveUpdateDelayMessage = 'Data is >15 min older than current system time.'
  }

  return {
    isLoading,
    isDelay,
    liveUpdateDelayType,
    liveUpdateDelayMessage,
    isLiveModeEnabled,
    liveModeStatus,
    onCheckLiveUpdate
  }
}
