import { PageHeaderItem, Switch } from '@core-clib/react'
import { useLiveUpdate } from './useLiveUpdate'
import { FC } from 'react'
import { LiveUpdateStatus } from './LiveUpdateStatus'
import './styles.css'
import { useAppSelect } from '@/core/redux/hooks'
import { isConnectedSubscriberSelector } from '@/core/redux/selectors'

export const LiveUpdate: FC = () => {
  const isConnectedSubscriber = useAppSelect(isConnectedSubscriberSelector)

  const {
    isLiveModeEnabled,
    liveUpdateDelayType,
    onCheckLiveUpdate,
    liveModeStatus
  } = useLiveUpdate()
  let switchClassName = 'switch-ok'

  if (liveUpdateDelayType) {
    switchClassName = `switch-${liveUpdateDelayType}`
  }

  if (liveModeStatus === 'disconnected') {
    switchClassName = 'switch-disconnected'
  }

  return (
    <div className="item-wrapper">
      <PageHeaderItem label="Live Update">
        <div className="toggler">
          {!isConnectedSubscriber && (
            <Switch
              value={isLiveModeEnabled}
              onChecked={onCheckLiveUpdate}
              className={switchClassName}
              disabled={isConnectedSubscriber}
            />
          )}
          <LiveUpdateStatus />
        </div>
      </PageHeaderItem>
    </div>
  )
}
