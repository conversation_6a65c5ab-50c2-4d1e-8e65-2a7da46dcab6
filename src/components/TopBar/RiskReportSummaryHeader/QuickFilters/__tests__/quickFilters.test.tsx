import { setupMockBackend, getInitializeAppWrapper } from '@/core/testing'
import { setUrlParam } from '@/core/testing/mocks'
import { cleanup, render, screen, waitFor } from '@testing-library/react'

import { App } from '@/components/App'
import { vi } from 'vitest'

describe('Quick filters', () => {
  beforeEach(() => {
    setupMockBackend()
  })

  afterEach(() => {
    vi.clearAllMocks()
    cleanup()
    setUrlParam('reportName', 'trades')
  })

  it('should render buttons', async () => {
    const { wrapper } = getInitializeAppWrapper()
    render(<App />, {
      wrapper
    })

    let TodayBtn

    await waitFor(() => {
      TodayBtn = screen.getByText(/Today/)
      expect(TodayBtn).toBeInTheDocument()
    })

    const T1Btn = screen.getByText(/T-1/)
    const AllBtn = screen.getByText(/All/)

    expect(T1Btn).toBeInTheDocument()
    expect(AllBtn).toBeInTheDocument()
  })
})
