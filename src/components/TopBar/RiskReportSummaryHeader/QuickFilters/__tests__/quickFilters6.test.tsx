import {
  combineWrappers,
  getReduxWrapper,
  wrapWithUrl,
  setupMockBackend
} from '@/core/testing'
import { setUrlParam } from '@/core/testing/mocks'
import { InitializeApp } from '@/components/App/InitializeApp'
import {
  cleanup,
  getByPlaceholderText,
  render,
  waitFor
} from '@testing-library/react'

import { App } from '@/components/App'
import { vi } from 'vitest'
import { act } from 'react-dom/test-utils'

describe.skip('Quick filters', () => {
  const url = '/reports/trades'

  beforeEach(() => {
    setupMockBackend()
  })

  afterEach(() => {
    vi.clearAllMocks()
    cleanup()
    setUrlParam('reportName', 'trades')
  })

  const getByDataToggleButtonId = (container: HTMLElement, id: string) =>
    container.querySelector(`[data-toggle-button-id="${id}"]`)

  it('should render date picker with initial date', async () => {
    setUrlParam('reportName', 'trades')
    const { wrapper } = getReduxWrapper()
    const { container } = render(<App />, {
      wrapper: combineWrappers(wrapWithUrl(url), wrapper, InitializeApp)
    })

    let fromLabel

    await waitFor(() => {
      fromLabel = document.querySelector(
        'core-clib-page-header-item[label="From"]'
      )
      expect(fromLabel).toBeInTheDocument()
    })

    const toLabel = document.querySelector(
      'core-clib-page-header-item[label="To"]'
    )

    const AllBtn = getByDataToggleButtonId(container, 'all')
    // const TodayBtn = screen.getByText(/Today/)
    const TodayBtn = getByDataToggleButtonId(container, 'today')
    const T1Btn = getByDataToggleButtonId(container, 't1')
    const datePickerFrom = getByPlaceholderText(fromLabel, /field_input_date/)
    const datePickerTo = getByPlaceholderText(toLabel, /field_input_date/)

    // get by data-toggle-button-id attribute

    /**
     * Click All button
     */
    act(() => {
      AllBtn.click()
    })

    await waitFor(() => {
      expect(datePickerFrom).toHaveValue('2000-01-01T00:00')
    })

    expect(datePickerTo).toHaveValue('2099-12-31T23:59:59.000')
    expect(AllBtn).toHaveClass('selected')
    expect(TodayBtn).not.toHaveClass('selected')
    expect(T1Btn).not.toHaveClass('selected')

    /**
     * Click T -1 button
     */
    act(() => {
      T1Btn.click()
    })

    await waitFor(() => {
      expect(datePickerFrom).toHaveValue('2024-08-09T00:00')
    })

    expect(datePickerTo).toHaveValue('2024-08-09T23:59:59.000')
    expect(AllBtn).not.toHaveClass('selected')
    expect(TodayBtn).not.toHaveClass('selected')
    expect(T1Btn).toHaveClass('selected')

    /**
     * Click Today button
     */
    act(() => {
      TodayBtn.click()
    })

    await waitFor(() => {
      expect(datePickerFrom).toHaveValue('2024-08-12T00:00')
    })

    expect(datePickerTo).toHaveValue('2099-12-31T23:59:59.000')
    // expect Today button to be selected
    expect(AllBtn).not.toHaveClass('selected')
    expect(TodayBtn).toHaveClass('selected')
    expect(T1Btn).not.toHaveClass('selected')

    /**
     * Click All button again
     */
    act(() => {
      AllBtn.click()
    })

    await waitFor(() => {
      expect(datePickerFrom).toHaveValue('2000-01-01T00:00')
    })

    expect(datePickerTo).toHaveValue('2099-12-31T23:59:59.000')
    expect(AllBtn).toHaveClass('selected')
    expect(TodayBtn).not.toHaveClass('selected')
    expect(T1Btn).not.toHaveClass('selected')
  })
})
