import {
  combineWrappers,
  getReduxWrapper,
  wrapWithUrl,
  setupMockBackend,
  getExecutionTimeFilterButton,
  waitForQuickFilters
} from '@/core/testing'
import { setUrlParam } from '@/core/testing/mocks'
import { InitializeApp } from '@/components/App/InitializeApp'
import {
  cleanup,
  fireEvent,
  render,
  screen,
  waitFor
} from '@testing-library/react'

import { App } from '@/components/App'
import { vi } from 'vitest'
import { act } from 'react-dom/test-utils'
vi.mock(
  '@/components/App/InitializeSimultaneousAccess/useSimultaneousAccess',
  () => ({
    useSimultaneousAccess: () => ({
      isMultiInstance: false,
      closeOtherInstances: vi.fn()
    })
  })
)

vi.mock('@/components/TopBar/Presets/useAgGridStateManager', () => ({
  useAgGridStateManager: () => ({
    restoreState: vi.fn(),
    getState: vi.fn(() => ({
      columnState: [],
      filterModel: {}
    })),
    addEventListener: vi.fn(),
    getColumnState: vi.fn(() => [])
  })
}))

// test is flaky
describe.skip('Quick filters', () => {
  const url = '/reports/trades'

  const { getSubsetReqSpy } = setupMockBackend()

  beforeEach(() => {
    setUrlParam('reportName', 'trades')
  })

  afterAll(() => {
    vi.clearAllMocks()
    cleanup()
  })

  it('should call getSubset with selected value', async () => {
    const { wrapper } = getReduxWrapper()

    const { container } = render(<App />, {
      wrapper: combineWrappers(wrapWithUrl(url), wrapper, InitializeApp)
    })

    const { datePickerFrom, AllBtn } = await waitForQuickFilters(container)

    const executionTimeFilterButton = await getExecutionTimeFilterButton()
    /**
     * Click All button
     */
    act(() => {
      AllBtn.click()
    })

    await waitFor(
      () => {
        expect(datePickerFrom).toHaveValue('2000-01-01T00:00')
      },
      { timeout: 4000 }
    )

    /**
     * Click execution time filter button
     */

    act(() => {
      executionTimeFilterButton.click()
    })
    const dateFilterInputs = screen.getAllByTestId('datefilter')
    const timeFilterInputs = screen.getAllByTestId('timefilter')

    getSubsetReqSpy.mockClear()

    /**
     * Change execution time filter from to : 2005-05-05T11:12:13
     */
    fireEvent.change(dateFilterInputs[0], { target: { value: '2005-05-05' } })
    fireEvent.change(timeFilterInputs[0], { target: { value: '11:12:13' } })

    await waitFor(
      () => {
        expect(datePickerFrom).toHaveDisplayValue(/^2005-05-05T11:12:13/)
      },
      { timeout: 4000 }
    )

    fireEvent.blur(dateFilterInputs[0])

    /**
     * expect getSubset to be called with selected value
     */
    await waitFor(
      () => {
        expect(getSubsetReqSpy).toHaveBeenCalledWith(
          expect.objectContaining({
            body: expect.objectContaining({
              tableSubsetRequest: expect.objectContaining({
                filters: [
                  {
                    column: 'ExecDateTimeUtc',
                    filters: [
                      {
                        operator: 'InRange',
                        value: '2005-05-05T11:12:13,2099-12-31T23:59:59'
                      }
                    ],
                    logicalOperator: 'Or'
                  }
                ]
              })
            })
          })
        )
      },
      { timeout: 4000 }
    )
  }, 10000)
})
