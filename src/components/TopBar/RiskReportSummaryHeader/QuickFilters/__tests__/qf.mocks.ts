import { vi } from 'vitest'

export const mockForQuickFilters = () => {
  vi.mock(
    '@/components/App/InitializeSimultaneousAccess/useSimultaneousAccess',
    () => ({
      useSimultaneousAccess: () => ({
        isMultiInstance: false,
        closeOtherInstances: vi.fn()
      })
    })
  )

  vi.mock('@/components/TopBar/Presets/useAgGridStateManager', () => ({
    useAgGridStateManager: () => ({
      restoreState: vi.fn(),
      getState: vi.fn(() => ({
        columnState: [],
        filterModel: {}
      })),
      addEventListener: vi.fn(),
      getColumnState: vi.fn(() => [])
    })
  }))
}
