import {
  setupMockBackend,
  waitForQuickFilters,
  getExecutionTimeFilterButton,
  getInitializeAppWrapper
} from '@/core/testing'
import { setUrlParam } from '@/core/testing/mocks'
import {
  cleanup,
  fireEvent,
  render,
  screen,
  waitFor
} from '@testing-library/react'

import { App } from '@/components/App'
import { vi } from 'vitest'
import { act } from 'react-dom/test-utils'
vi.mock(
  '@/components/App/InitializeSimultaneousAccess/useSimultaneousAccess',
  () => ({
    useSimultaneousAccess: () => ({
      isMultiInstance: false,
      closeOtherInstances: vi.fn()
    })
  })
)

vi.mock('@/components/TopBar/Presets/useAgGridStateManager', () => ({
  useAgGridStateManager: () => ({
    restoreState: vi.fn(),
    getState: vi.fn(() => ({
      columnState: [],
      filterModel: {}
    })),
    addEventListener: vi.fn(),
    getColumnState: vi.fn(() => [])
  })
}))

// test is flaky
describe.skip('Quick filters', () => {
  const url = '/reports/trades'

  beforeAll(() => {
    setUrlParam('reportName', 'trades')
    setupMockBackend()
  })

  afterAll(() => {
    vi.clearAllMocks()
    cleanup()
  })

  it('should unselect QF when execution time - date change', async () => {
    const { container } = render(<App />, getInitializeAppWrapper())

    const { datePickerFrom, AllBtn, TodayBtn, T1Btn, datePickerTo } =
      await waitForQuickFilters(container)

    const executionTimeFilterButton = await getExecutionTimeFilterButton()

    /**
     * Click All button
     */
    act(() => {
      AllBtn.click()
    })

    await waitFor(() => {
      expect(datePickerFrom).toHaveValue('2000-01-01T00:00'), { timeout: 4000 }
    })

    /**
     * Click execution time filter button
     */

    act(() => {
      executionTimeFilterButton.click()
    })

    const dateFilterInputs = screen.getAllByTestId('datefilter')

    /**
     * Change execution time filter date
     */
    fireEvent.change(dateFilterInputs[0], { target: { value: '2005-05-05' } })

    await waitFor(() => {
      expect(datePickerFrom).toHaveDisplayValue(/^2005-05-05/),
        { timeout: 4000 }
    })

    await waitFor(
      () => {
        expect(AllBtn).not.toHaveClass('selected')
      },
      { timeout: 4000 }
    )

    expect(TodayBtn).not.toHaveClass('selected')
    expect(T1Btn).not.toHaveClass('selected')

    /**
     * Click Today button
     */
    act(() => {
      TodayBtn.click()
    })

    await waitFor(
      () => {
        expect(datePickerFrom).toHaveValue('2024-08-12T00:00')
      },
      { timeout: 4000 }
    )

    expect(datePickerTo).toHaveValue('2099-12-31T23:59:59.000')
    // expect Today button to be selected
    expect(AllBtn).not.toHaveClass('selected')
    expect(TodayBtn).toHaveClass('selected')
    expect(T1Btn).not.toHaveClass('selected')
  }, 10000)
})
