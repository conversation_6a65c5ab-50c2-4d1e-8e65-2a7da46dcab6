import {
  setupMockBackend,
  waitForQuickFilters,
  getExecutionTimeFilterButton,
  getInitializeAppWrapper
} from '@/core/testing'
import { setUrlParam } from '@/core/testing/mocks'
import {
  cleanup,
  fireEvent,
  render,
  screen,
  waitFor
} from '@testing-library/react'

import { App } from '@/components/App'
import { vi } from 'vitest'
import { act } from 'react-dom/test-utils'
vi.mock(
  '@/components/App/InitializeSimultaneousAccess/useSimultaneousAccess',
  () => ({
    useSimultaneousAccess: () => ({
      isMultiInstance: false,
      closeOtherInstances: vi.fn()
    })
  })
)

vi.mock('@/components/TopBar/Presets/useAgGridStateManager', () => ({
  useAgGridStateManager: () => ({
    restoreState: vi.fn(),
    getState: vi.fn(() => ({
      columnState: [],
      filterModel: {}
    })),
    addEventListener: vi.fn(),
    getColumnState: vi.fn(() => [])
  })
}))

// test is flaky
describe.skip('Quick filters', () => {
  const url = '/reports/trades'

  beforeAll(() => {
    setUrlParam('reportName', 'trades')
    setupMockBackend()
  })

  afterAll(() => {
    vi.clearAllMocks()
    cleanup()
  })

  it('should unselect QF when execution time - time change', async () => {
    const { container, rerender } = render(<App />, getInitializeAppWrapper())
    const { datePickerFrom, AllBtn, TodayBtn, T1Btn, datePickerTo } =
      await waitForQuickFilters(container)

    const executionTimeFilterButton = await getExecutionTimeFilterButton()

    /**
     * Click All button
     */
    act(() => {
      AllBtn.click()
    })

    await waitFor(() => {
      expect(datePickerFrom).toHaveValue('2000-01-01T00:00')
    })

    await waitFor(
      () => {
        expect(AllBtn).toHaveClass('selected')
      },
      { timeout: 5000 }
    )

    /**
     * Click execution time filter button
     */

    act(() => {
      executionTimeFilterButton.click()
    })

    const timeFilterInputs = screen.getAllByTestId('timefilter')

    /**
     * Change execution time filter time
     */
    fireEvent.change(timeFilterInputs[0], {
      target: { value: '11:22:33' }
    })

    await waitFor(
      () => {
        expect(datePickerFrom).toHaveDisplayValue(/11:22:33/)
      },
      { timeout: 4000 }
    )

    rerender(<App />)

    await waitFor(
      () => {
        expect(AllBtn).not.toHaveClass('selected')
      },
      { timeout: 5000 }
    )

    expect(TodayBtn).not.toHaveClass('selected')
    expect(T1Btn).not.toHaveClass('selected')

    /**
     * Click Today button
     */
    act(() => {
      T1Btn.click()
    })

    await waitFor(() => {
      expect(datePickerFrom).toHaveValue('2024-08-09T00:00')
    })

    expect(datePickerTo).toHaveValue('2024-08-09T23:59:59.000')
    // expect Today button to be selected
    expect(AllBtn).not.toHaveClass('selected')
    expect(TodayBtn).not.toHaveClass('selected')
    expect(T1Btn).toHaveClass('selected')
  }, 10000)
})
