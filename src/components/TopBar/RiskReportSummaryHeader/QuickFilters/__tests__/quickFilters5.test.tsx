import {
  combineWrappers,
  getReduxWrapper,
  wrapWithUrl,
  setupMockBackend
} from '@/core/testing'
import { setUrlParam } from '@/core/testing/mocks'
import { InitializeApp } from '@/components/App/InitializeApp'
import {
  cleanup,
  getByPlaceholderText,
  render,
  waitFor
} from '@testing-library/react'

import { App } from '@/components/App'
import { vi } from 'vitest'
vi.mock(
  '@/components/App/InitializeSimultaneousAccess/useSimultaneousAccess',
  () => ({
    useSimultaneousAccess: () => ({
      isMultiInstance: false,
      closeOtherInstances: vi.fn()
    })
  })
)

vi.mock('@/components/TopBar/Presets/useAgGridStateManager', () => ({
  useAgGridStateManager: () => ({
    restoreState: vi.fn(),
    getState: vi.fn(() => ({
      columnState: [],
      filterModel: {}
    })),
    addEventListener: vi.fn(),
    getColumnState: vi.fn(() => [])
  })
}))

describe('Quick filters', () => {
  const url = '/reports/trades'

  beforeEach(() => {
    setupMockBackend()
  })

  afterEach(() => {
    vi.clearAllMocks()
    cleanup()
    setUrlParam('reportName', 'trades')
  })

  it('should render date picker', async () => {
    const { wrapper } = getReduxWrapper()
    setUrlParam('reportName', 'trades')

    render(<App />, {
      wrapper: combineWrappers(wrapWithUrl(url), wrapper, InitializeApp)
    })

    let fromLabel

    await waitFor(() => {
      fromLabel = document.querySelector(
        'core-clib-page-header-item[label="From"]'
      )
      expect(fromLabel).toBeInTheDocument()
    })

    const toLabel = document.querySelector(
      'core-clib-page-header-item[label="To"]'
    )

    const datePickerFrom = getByPlaceholderText(fromLabel, /field_input_date/)
    const datePickerTo = getByPlaceholderText(toLabel, /field_input_date/)
  })
})
