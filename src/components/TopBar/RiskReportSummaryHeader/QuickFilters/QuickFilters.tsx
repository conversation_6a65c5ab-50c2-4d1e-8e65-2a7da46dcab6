import { DatePicker } from '@/components/Shared/DatePicker'
import { executionTimeFilterSelector } from '@/core/redux/features/quickFilters/executionTimeFilter/selectors/executionTimeFilterSelector'
import { onExecutionTimeDatesChangesThunk } from '@/core/redux/features/quickFilters/executionTimeFilter/thunks/onExecutionTimeDatesChangedThunk'
import { onExecutionTimeFilterSelectedThunk } from '@/core/redux/features/quickFilters/executionTimeFilter/thunks/onExecutionTimeFilterSelectedThunk'
import { FilterOptions } from '@/core/redux/features/quickFilters/executionTimeFilter/types'
import { useAppDispatch, useAppSelect } from '@/core/redux/hooks'
import { formatToLocalDateAndTime } from '@/core/utils/grid'
import { PageHeaderItem, PageHeaderItems, ToggleButton } from '@core-clib/react'
import { FC } from 'react'
import styles from './QuickFilters.module.scss'
import { useApplyFiltersEffect } from './hooks/useApplyFiltersEffect'
import { useCurrentAsOfListener } from './hooks/useCurrentAsOfEffect'

const filterActions = [
  { id: FilterOptions.ALL, label: 'All' },
  { id: FilterOptions.TODAY, label: 'Today' },
  { id: FilterOptions.T1, label: 'T-1' }
]

export const QuickFilters: FC = () => {
  const executionTimeFilter = useAppSelect(executionTimeFilterSelector)
  const currentFilterDates = executionTimeFilter.filter
  const currentFilterSelectedValue = executionTimeFilter.selectedFilterOption
  const dispatch = useAppDispatch()

  const dateFrom = currentFilterDates?.dateFrom
    ? formatToLocalDateAndTime(currentFilterDates.dateFrom)
    : undefined

  const dateTo = currentFilterDates?.dateTo
    ? formatToLocalDateAndTime(currentFilterDates.dateTo)
    : undefined

  useApplyFiltersEffect()
  useCurrentAsOfListener()

  return (
    <PageHeaderItems className={styles.QuickFilters}>
      <PageHeaderItem>
        <ToggleButton
          className="form"
          actions={filterActions}
          selectedActionId={currentFilterSelectedValue}
          onSelected={(action) => {
            dispatch(
              onExecutionTimeFilterSelectedThunk(action.id as FilterOptions)
            )
          }}
        ></ToggleButton>
      </PageHeaderItem>
      <PageHeaderItem label="From">
        <DatePicker
          onDateChange={(date) => {
            dispatch(
              onExecutionTimeDatesChangesThunk({
                dateFrom: date && new Date(date).toISOString(),
                selectedFilter: FilterOptions.DATES
              })
            )
          }}
          initialDate={dateFrom}
        />
      </PageHeaderItem>
      <PageHeaderItem label="To">
        <DatePicker
          onDateChange={(date) => {
            dispatch(
              onExecutionTimeDatesChangesThunk({
                dateTo: date && new Date(date).toISOString(),
                selectedFilter: FilterOptions.DATES
              })
            )
          }}
          initialDate={dateTo}
        />
      </PageHeaderItem>
    </PageHeaderItems>
  )
}
