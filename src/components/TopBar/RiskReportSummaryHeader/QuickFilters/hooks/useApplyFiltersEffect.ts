import { useGridApi } from '@/components/Grid/hooks'
import { executionTimeFilterSelector } from '@/core/redux/features/quickFilters/executionTimeFilter/selectors/executionTimeFilterSelector'
import { useAppSelect } from '@/core/redux/hooks'
import { useEffect } from 'react'
import { EXECUTION_TIME } from '@/core/redux/features/quickFilters/executionTimeFilter/constants'

export const useApplyFiltersEffect = () => {
  const gridApi = useGridApi()?.gridApi
  const filter = useAppSelect(executionTimeFilterSelector).filter
  const selectedFilterOption = useAppSelect(
    executionTimeFilterSelector
  ).selectedFilterOption

  const utcDateFrom = filter?.dateFrom
  const utcDateTo = filter?.dateTo

  useEffect(() => {
    if (!gridApi) {
      return
    }

    const currentAgGridFilterModel = gridApi?.getFilterModel() ?? {}

    currentAgGridFilterModel[EXECUTION_TIME] = {
      type: 'inRange',
      dateFrom: utcDateFrom,
      dateTo: utcDateTo
    }

    gridApi?.setFilterModel(currentAgGridFilterModel)
    gridApi?.onFilterChanged()
  }, [gridApi, selectedFilterOption, utcDateFrom, utcDateTo])
}
