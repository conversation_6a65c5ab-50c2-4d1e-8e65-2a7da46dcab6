import * as gridHooks from '@/components/Grid/hooks'
import { EXECUTION_TIME } from '@/core/redux/features/quickFilters/executionTimeFilter/constants'
import { onExecutionTimeDatesChangesThunk } from '@/core/redux/features/quickFilters/executionTimeFilter/thunks/onExecutionTimeDatesChangedThunk'
import { onExecutionTimeFilterSelectedThunk } from '@/core/redux/features/quickFilters/executionTimeFilter/thunks/onExecutionTimeFilterSelectedThunk'
import { FilterOptions } from '@/core/redux/features/quickFilters/executionTimeFilter/types'
import { getReduxWrapper } from '@/core/testing'
import { GridApi } from '@ag-grid-community/core'
import { act, renderHook, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { useApplyFiltersEffect } from '../useApplyFiltersEffect'

describe('useApplyFiltersEffect', () => {
  const gridApiMock = {
    getFilterModel: vi.fn(),
    setFilterModel: vi.fn(),
    onFilterChanged: vi.fn()
  } as unknown as GridApi

  vi.spyOn(gridHooks, 'useGridApi').mockReturnValue({
    gridApi: gridApiMock,
    columnApi: undefined,
    setGridApi: vi.fn(),
    setColumnApi: vi.fn()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })
  const stateMock = {
    report: {
      currentAsOfUtc: '2022-01-01T00:00:00.000Z',
      lastAsOfUtcMetadata: {}
    },
    executionTimeFilter: {
      currentLocalMidnight: '2022-01-01T00:00:00.000Z',
      filter: {
        dateFrom: '2022-01-01T00:00:00.000Z',
        dateTo: '2099-12-31T23:59:59.999Z'
      },
      selectedFilterOption: FilterOptions.ALL
    }
  }

  const { store, wrapper } = getReduxWrapper(stateMock)

  test('should update EXECUTION_TIME filter when selectedValue is not "ALL"', async () => {
    const { rerender } = renderHook(() => useApplyFiltersEffect(), { wrapper })

    act(() => {
      rerender()
    })

    store.dispatch(onExecutionTimeFilterSelectedThunk(FilterOptions.TODAY))

    await waitFor(() => {
      expect(gridApiMock.setFilterModel).toHaveBeenCalledWith({
        [EXECUTION_TIME]: {
          type: 'inRange',
          dateFrom: '2021-12-31T23:00:00.000Z',
          dateTo: '2099-12-31T22:59:59.999Z'
        }
      })
    })
  })

  test('should update EXECUTION_TIME filter when filter value is changed', async () => {
    const dateFrom = '2022-01-01T23:00:00.000Z'
    const dateTo = '2099-12-31T22:59:59.999Z'

    const { rerender } = renderHook(() => useApplyFiltersEffect(), { wrapper })

    act(() => {
      rerender()
    })

    store.dispatch(
      onExecutionTimeDatesChangesThunk({
        selectedFilterOption: FilterOptions.DATES,
        dateFrom: new Date(dateFrom).toISOString(),
        dateTo: new Date(dateTo).toISOString()
      })
    )

    await waitFor(() => {
      expect(gridApiMock.setFilterModel).toHaveBeenCalledWith({
        [EXECUTION_TIME]: {
          type: 'inRange',
          dateFrom: dateFrom,
          dateTo: dateTo
        }
      })
    })
  })
})
