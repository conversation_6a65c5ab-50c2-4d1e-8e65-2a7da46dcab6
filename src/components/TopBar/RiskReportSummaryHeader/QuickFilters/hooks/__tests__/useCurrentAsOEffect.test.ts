import { defaultDateTo } from '@/core/redux/features/quickFilters/executionTimeFilter/constants'
import { FilterOptions } from '@/core/redux/features/quickFilters/executionTimeFilter/types'
import { setLatestAsOfUtcResponse } from '@/core/redux/features/report'
import { AgGridFilterOperator } from '@/core/services/grid'
import { getReduxWrapper } from '@/core/testing'
import { act, renderHook, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { useCurrentAsOfListener } from '../useCurrentAsOfEffect'
import { getLocalMidnight } from '@/core/utils/date'

describe('useCurrentAsOfEffect', () => {
  const currentAsOfUtc = new Date().toISOString()

  const { wrapper, store } = getReduxWrapper({
    report: { currentAsOfUtc, lastAsOfUtcMetadata: {} },
    executionTimeFilter: {
      currentLocalMidnight: currentAsOfUtc,
      selectedFilterOption: FilterOptions.TODAY,
      filter: {
        dateFrom: currentAsOfUtc,
        dateTo: new Date(defaultDateTo).toISOString(),
        type: AgGridFilterOperator.InRange
      }
    }
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  test('should not change filter dateFrom if currentAsOfUtc the same day', async () => {
    renderHook(() => useCurrentAsOfListener(), { wrapper })

    await waitFor(() => {
      const expectedCurrentLocalMidnight = getLocalMidnight(currentAsOfUtc)

      expect(store.getState().executionTimeFilter).toStrictEqual({
        currentLocalMidnight: expectedCurrentLocalMidnight,
        selectedFilterOption: FilterOptions.TODAY,
        filter: {
          dateFrom: expectedCurrentLocalMidnight,
          dateTo: new Date(defaultDateTo).toISOString(),
          type: AgGridFilterOperator.InRange
        }
      })
    })
  })

  test('should update filter when currentAsOfUtc has changed', async () => {
    const refDate = new Date('2022-01-02T00:00:00.000Z')
    const { rerender } = renderHook(() => useCurrentAsOfListener(), { wrapper })

    act(() => rerender())

    store.dispatch(
      setLatestAsOfUtcResponse({ latestAsOfUtc: refDate.toISOString() })
    )

    await waitFor(() => {
      expect(store.getState().executionTimeFilter).toStrictEqual({
        currentLocalMidnight: getLocalMidnight(refDate.toISOString()),
        selectedFilterOption: FilterOptions.TODAY,
        filter: {
          dateFrom: getLocalMidnight(refDate.toISOString()),
          dateTo: new Date(defaultDateTo).toISOString(),
          type: AgGridFilterOperator.InRange
        }
      })
    })
  })
})
