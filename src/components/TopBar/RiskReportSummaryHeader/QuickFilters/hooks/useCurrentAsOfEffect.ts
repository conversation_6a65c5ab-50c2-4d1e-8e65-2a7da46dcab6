import { executionTimeFilterSelector } from '@/core/redux/features/quickFilters/executionTimeFilter/selectors/executionTimeFilterSelector'
import { onExecutionTimeFilterSelectedThunk } from '@/core/redux/features/quickFilters/executionTimeFilter/thunks/onExecutionTimeFilterSelectedThunk'
import { FilterOptions } from '@/core/redux/features/quickFilters/executionTimeFilter/types'
import { useAppDispatch, useAppSelect } from '@/core/redux/hooks'
import { getLocalMidnight } from '@/core/utils/date'
import { useEffect } from 'react'

export const useCurrentAsOfListener = () => {
  const dispatch = useAppDispatch()
  const currentAsOfUtc = useAppSelect((state) => state.report.currentAsOfUtc)
  const currentLocalMidnight = getLocalMidnight(currentAsOfUtc)

  const {
    selectedFilterOption,
    currentLocalMidnight: executionTimeFilterCurrentLocalMidnight
  } = useAppSelect(executionTimeFilterSelector)

  useEffect(() => {
    if (
      currentLocalMidnight !== executionTimeFilterCurrentLocalMidnight &&
      selectedFilterOption &&
      [FilterOptions.TODAY, FilterOptions.T1].includes(selectedFilterOption)
    ) {
      dispatch(onExecutionTimeFilterSelectedThunk(selectedFilterOption))
    }
  }, [
    currentAsOfUtc,
    selectedFilterOption,
    executionTimeFilterCurrentLocalMidnight,
    dispatch
  ])
}
