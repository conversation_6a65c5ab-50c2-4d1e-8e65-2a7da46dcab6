import { FormattedValue } from '@/components/TopBar/FormattedNumber'
import { useAppSelect } from '@/core/redux/hooks'
import { PageHeaderItem, PageHeaderItems } from '@core-clib/react'
import { QuickFilters } from './QuickFilters/QuickFilters'
import { isTradesReportSelector } from '@/core/redux/selectors'

export const RiskReportSummaryHeader = () => {
  const riskReportSummary = useAppSelect(
    (state) => state.report.riskReportSummary
  )

  const isTrades = useAppSelect(isTradesReportSelector)

  return (
    <>
      <PageHeaderItems>
        {riskReportSummary.map((riskMetric) => (
          <PageHeaderItem key={riskMetric.title} label={riskMetric.title}>
            <FormattedValue value={riskMetric.value} />
          </PageHeaderItem>
        ))}
      </PageHeaderItems>
      {isTrades && <QuickFilters />}
    </>
  )
}
