import { setCurrentKnowledgeAsOf } from '@/core/redux/features/report/reportMetadata'
import {
  currentKnowledgeAsOfSelector,
  knowledgeAsOfValuesSelector,
  isKnowledgeAsOfUIVisibleSelector
} from '@/core/redux/features/report/reportMetadata/selectors'
import { useAppDispatch, useAppSelect } from '@/core/redux/hooks'
import { formatDateToLocal } from '@/core/utils/date'
import { Dropdown, PageHeaderItem } from '@core-clib/react'
import { IAction } from '@core-clib/web-components'

export const KnowledgeAsOf = () => {
  const dispatch = useAppDispatch()
  const isKnowledgeAsOfUIVisible = useAppSelect(
    isKnowledgeAsOfUIVisibleSelector
  )
  const currentKnowledgeAsOf = useAppSelect(currentKnowledgeAsOfSelector)
  const knowledgeAsOfValues = useAppSelect(knowledgeAsOfValuesSelector)

  // Convert knowledgeAsOfValues to format expected by Dropdown
  const dropdownOptions = Array.isArray(knowledgeAsOfValues)
    ? knowledgeAsOfValues.map((value) => ({
        id: value,
        label: formatDateToLocal(new Date(value))
      }))
    : []

  const handleKnowledgeAsOfChange = (action: IAction) => {
    dispatch(setCurrentKnowledgeAsOf(action.id))
  }

  if (!isKnowledgeAsOfUIVisible) {
    return null
  }
  return (
    <div data-testid="knowledge-as-of">
      <div className="item-wrapper">
        <PageHeaderItem label="Knowledge As Of">
          <div>
            <Dropdown
              actions={dropdownOptions}
              selectedActionId={currentKnowledgeAsOf}
              onSelected={handleKnowledgeAsOfChange}
              className="dropdown"
            />
          </div>
        </PageHeaderItem>
      </div>
    </div>
  )
}
