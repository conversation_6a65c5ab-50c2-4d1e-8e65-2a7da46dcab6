import { Dropdown, TooltipWrapper } from '@core-clib/react'
import { useReposDropdown } from '@/components/TopBar/ReposDropdown/useReposDropdown'
import { useAppSelect } from '@/core/redux/hooks'
import './ReposDropdown.scss'
import { renderToString } from 'react-dom/server'

export const ReposDropdown = () => {
  const theme = useAppSelect((state) => state.users.theme)
  const { displayLabel, actions, selectedAction, selectAction } =
    useReposDropdown()

  if (!actions?.length) return null

  return (
    // TODO: The wrapper should be replaced with the HeaderActions component to align with the Angular implementation.
    //  However, for some reason, the HeaderActions component has not been exported for the React version.
    //  Due to time pressure, I've decided to leave just div as it seems to look and work the same, instead of waiting for the next release of the core SDK library.
    <div>
      <div className="preProcessingParamsSelector">
        <div>{displayLabel}</div>
        <TooltipWrapper
          tippyOptions={{
            content: `${renderToString(
              <div className={`tippy-tooltip descriptionTooltip ${theme}`}>
                {actions?.map((action) => (
                  <div key={action.label}>
                    {action.label} - {action.shortDescription}
                  </div>
                ))}
              </div>
            )}
            </div>`,
            allowHTML: true
          }}
        >
          <Dropdown
            selectedActionId={selectedAction?.id}
            actions={actions}
            onSelected={selectAction}
          />
        </TooltipWrapper>
      </div>
    </div>
  )
}
