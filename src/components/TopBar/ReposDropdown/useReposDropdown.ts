import { useRepoNettingParamEffect } from '@/core/redux/features/report/hooks'
import { IAction, IActionBase } from '@core-clib/web-components'
import { PreProcessingParamValueSpec } from '@/core/services/riskReport'

interface ReposDropdownAction extends IAction {
  id: string
  shortDescription?: string
  label: string
}

const mapParamValueSpecToReposDropdownAction = (
  value: PreProcessingParamValueSpec
): ReposDropdownAction => ({
  id: value.value,
  shortDescription: value.shortDescription,
  label: value.displayValue
})

export const useReposDropdown = () => {
  const { displayLabel, valuesSpec, currentValueSpec, changeParamValue } =
    useRepoNettingParamEffect()

  const actions = valuesSpec?.map(mapParamValueSpecToReposDropdownAction)

  const selectedAction =
    currentValueSpec && mapParamValueSpecToReposDropdownAction(currentValueSpec)

  const selectAction = (value: IActionBase<never, string>) => {
    changeParamValue(value.id)
  }

  return { displayLabel, actions, selectedAction, selectAction }
}
