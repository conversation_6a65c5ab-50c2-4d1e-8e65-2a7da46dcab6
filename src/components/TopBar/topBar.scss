/* Light Theme Specific Css */
.core-light-theme {
  /* Top Header and Summary Css */
  #top-app-header-summary {
    --page-header-item-content-color: var(--black);
    --light-mode-non-header-text-opacity: 1;

    .last-update {
      opacity: var(--light-mode-non-header-text-opacity);
    }

    .datepicker-container {
      .datepicker,
      .datetime-plug span {
        opacity: var(--light-mode-non-header-text-opacity);
      }
    }

    .get-latest {
      button {
        margin-left: 2px;

        i,
        span {
          color: var(--black);
        }
      }
    }
  }

  div.wrapper .preset-group span.preset-group-name {
    color: var(--blue-gray-4);
  }

  /* Top Header and Summary Css */
}

/* Light Theme Specific Css */

@mixin font-style {
  font-weight: normal;
  color: var(--page-header-item-content-color);
  opacity: 0.5;
}

.tippy-tooltip {
  padding: 10px 10px;
  border: 1px solid var(--page-header-border-color);
}

.item-wrapper {
  border-right: 1px solid var(--white-30);
  margin-right: 10px;
  position: relative;

  &:last-child {
    border-right: none;
    margin-right: 0;
  }
}

.last-update {
  @include font-style;
}

core-clib-multiselect-dropdown.secondary {
  core-clib-button {
    button {
      background: transparent;
      padding-left: 0;
      padding-right: 0;
    }
  }
}

.typeahead__menu {
  min-width: 175px;
}

.toggler {
  display: flex;
  gap: 5px;
  align-items: center;
}

.sync-button {
  &.get-latest {
    button {
      margin-left: 2px;

      i.ci-loop3,
      span {
        color: var(--blue-2);

        &.button--text {
          color: var(--blue-2);
        }
      }
    }
  }
}

.datepicker-container {
  margin-left: 3px;

  .datepicker {
    color: white;
    opacity: 0.5;
    font-weight: 600;
    width: auto;
    height: 14px;
  }

  .datetime-plug-wrapper {
    display: flex;
    align-self: flex-end;

    .datetime-plug {
      display: flex;
      align-items: center;
      gap: 5px;

      span {
        @include font-style;
      }

      .calendarIcon {
        opacity: 0.5;
        cursor: pointer;
      }
    }
  }
}

input[type='datetime-local']::-webkit-datetime-edit {
  display: none;
}

.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
}

.hidden {
  visibility: hidden;
  height: 0px;
}

core-clib-field {
  input {
    --border-color: var(--input-default-border-color);
    border-radius: var(--input-border-radius);
    --background-color: var(--input-primary-background-color);
    outline: none;
    color: var(--input-primary-color);
    padding: 0 var(--input-padding);
    font-size: var(--font-size-xs);
    width: 100%;
    height: var(--input-height);
    border: 1px solid var(--border-color);
    background-color: var(--background-color);
  }
}
