import { useOptionState } from './useOptionState'
import { Button } from '@core-clib/react'
import { useLiveUpdate } from '@/components/TopBar/LiveUpdate/useLiveUpdate'
import { FC, useEffect } from 'react'
import style from './CloseTimeButton.module.scss'

export const CloseTimeButton: FC = () => {
  const {
    isExpanded,
    selectedOption,
    lastSelectedOption,
    setSelectedOption,
    setExpanded,
    handleOptionLogic,
    closeTimeOptions
  } = useOptionState()
  const { isLiveModeEnabled } = useLiveUpdate()

  useEffect(() => {
    if (isLiveModeEnabled) {
      setExpanded(false)
      setSelectedOption(undefined)
    }
  }, [isLiveModeEnabled])

  const toggleRegionsExpandedWithTimeout = () => {
    setTimeout(() => {
      setExpanded(true)
    }, 200)
  }

  return (
    <div className={`item-wrapper`} data-testid="closeregionbuttons">
      <h5 className="label">Close</h5>
      <div className="content">
        <div className={style.closeSelector}>
          {isExpanded ? (
            closeTimeOptions.map((option) => (
              <Button
                key={`region-${option.name}`}
                data-testid={`region-${option.name}`}
                onClick={() => handleOptionLogic(option)}
                className={`${
                  option.name === selectedOption?.name
                    ? style.regionButtonSelected
                    : style.regionButton
                }`}
                text={option.label}
              ></Button>
            ))
          ) : (
            <Button
              key={`region-default`}
              data-testid={`region-default`}
              onDoubleClick={() => {
                handleOptionLogic(lastSelectedOption)
                setExpanded(true)
              }}
              onClick={toggleRegionsExpandedWithTimeout}
              className={`${
                selectedOption?.name == 'EOD'
                  ? style.regionButtonSelected
                  : style.regionButton
              }`}
              text={lastSelectedOption?.label}
            ></Button>
          )}
        </div>
      </div>
    </div>
  )
}
