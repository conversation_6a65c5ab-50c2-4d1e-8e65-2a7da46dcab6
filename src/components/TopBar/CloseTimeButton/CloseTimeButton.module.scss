.closeRegionsDisabled {
  opacity: 0.5;
  cursor: default;
}

:global(.core-light-theme) {
  .closeSelector {
    border: 1px solid var(--label-color);
    .regionButton {
      button {
        span {
          color: var(--black);
        }
      }
    }
    .regionButtonSelected {
      button {
        background-color: var(--th-ms-value-background-color);
      }
    }
  }
}

/* Fixing the Width to make sure that fields are not jumping while adjusting widths */
.currentAsOfWrapper {
  core-clib-page-header-item {
    width: max-content;
  }
}
.liveUpdateWrapper {
  width: 80px;
}

.closeToRegionWrapper {
  core-clib-page-header-item {
    min-width: 68px;
  }
}
/* Fixing the Width to make sure that fields are not jumping while adjusting widths */

.closeSelector {
  border: 1px solid #a4d7f6;
  border-radius: 7.71px;
  padding: 1px;
  display: flex;
  margin-top: 5px;

  core-clib-button {
    button {
      height: 15px;
      border-radius: 8.57px;

      span {
        font-weight: bold;
      }
    }
  }

  .regionButton {
    button {
      span {
        color: var(--blue-2);
      }
    }
  }

  .regionButtonSelected {
    button {
      background: #a4d7f6;
      span {
        color: var(--black);
      }
    }
  }
}
