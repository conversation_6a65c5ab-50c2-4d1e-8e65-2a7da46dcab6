import { useState } from 'react'
import {
  useAppDispatch,
  useAppSelect,
  useCurrentAsOfLocal
} from '@/core/redux/hooks'
import {
  eodReportTypeSelector,
  setRegionExpand
} from '@/core/redux/features/report'
import {
  optionByName,
  CloseTimeOption,
  options,
  CloseTimeOptionName,
  getEODCloseDateTime,
  getCloseDateForRegion
} from './closeTimeUtils'
import { setKnowledgeAsOfUIVisible } from '@/core/redux/features/report/reportMetadata'
import { getKnowledgeAsOfValuesThunk } from '@/core/redux/features/report/reportMetadata/thunk/getKnowledgeAsOfValuesThunk'

export const useOptionState = () => {
  const [selectedRegionName, setSelectedRegionName] = useState<
    CloseTimeOptionName | undefined
  >(undefined)
  const eodReportType = useAppSelect(eodReportTypeSelector)

  const closeTimeOptions = options.filter(
    (option) => option.group !== 'eod' || eodReportType
  )
  const [lastSelectedOption, setLastSelectedOption] = useState<CloseTimeOption>(
    closeTimeOptions[0]
  )
  const dispatch = useAppDispatch()
  const isExpanded = useAppSelect((state) => state.report.isRegionExpanded)
  const { changeCurrentAsOfDate, currentAsOfUtc } = useCurrentAsOfLocal()

  const setExpanded = (isExpanded: boolean) => {
    dispatch(setRegionExpand(isExpanded))
  }

  const setSelectedOption = (option?: CloseTimeOption) => {
    setSelectedRegionName(option?.name)
    if (option) {
      setLastSelectedOption(option)
    }
  }

  const selectedOption = selectedRegionName
    ? optionByName(selectedRegionName)
    : undefined

  const handleOptionLogic = async (option: CloseTimeOption) => {
    setSelectedOption(option)

    if (option.group === 'region') {
      dispatch(setKnowledgeAsOfUIVisible(false))
    }

    if (option.group === 'eod') {
      console.log('EOD option selected')

      await dispatch(setKnowledgeAsOfUIVisible(true))
      await dispatch(getKnowledgeAsOfValuesThunk())
      await changeCurrentAsOfDate(getEODCloseDateTime(currentAsOfUtc))
    } else {
      await changeCurrentAsOfDate(getCloseDateForRegion(option))
    }
  }

  return {
    lastSelectedOption,
    isExpanded,
    selectedOption,
    closeTimeOptions,
    setExpanded,
    setSelectedOption,
    handleOptionLogic
  }
}
