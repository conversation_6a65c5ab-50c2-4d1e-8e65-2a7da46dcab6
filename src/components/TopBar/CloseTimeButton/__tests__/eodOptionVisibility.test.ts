import { renderHookWithRedux } from '@/core/testing'
import { useOptionState } from '../useOptionState'

it('should include EOD option when eodReportType is defined', () => {
  const { closeTimeOptions } = renderHookWithRedux(() => useOptionState(), {
    report: {
      eodReportType: 'EOD',
      isRegionExpanded: false
    }
  })

  expect(
    closeTimeOptions.filter((option) => option.group === 'eod').length
  ).toBeGreaterThan(0)
})

it('should not include EOD option when eodReportType is undefined', () => {
  const { closeTimeOptions } = renderHookWithRedux(() => useOptionState(), {
    report: {
      eodReportType: undefined,
      isRegionExpanded: false
    }
  })

  expect(
    closeTimeOptions.filter((option) => option.group === 'eod').length
  ).toBe(0)
})
