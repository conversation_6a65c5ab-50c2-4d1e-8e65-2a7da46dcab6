import { screen, fireEvent } from '@testing-library/react'
import { setCurrentAsOfLocal } from '@/core/redux/features/report'
import { act } from '@testing-library/react'
import { CloseTimeButton } from '../CloseTimeButton'
import {
  renderWithReduxInitialState,
  renderWithMockedState
} from '@/core/testing'
import { vi } from 'vitest'
// @ts-ignore
import { mockUseLiveUpdate } from '../../LiveUpdate/useLiveUpdate'
import { stateMock } from '@/core/testing/index'
const mocks = vi.hoisted(() => {
  return {
    useRegionExpansion: vi.fn(),
    mockUseLiveUpdate: vi.fn()
  }
})
vi.mock('./useRegionExpansion', () => {
  return {
    useRegionExpansion: mocks.useRegionExpansion
  }
})

const regions = [
  { name: 'us', label: 'US', time: '2023-11-15T21:59:00.000Z' },
  { name: 'emea', label: 'EMEA', time: '2023-11-15T15:20:00.000Z' },
  { name: 'apac', label: 'APAC', time: '2023-11-15T10:45:00.000Z' }
]

// TODO: Update the test suite to test current CloseTimeOfRegion component implementation
describe.skip('CloseTimeOfRegion', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.clearAllMocks()
    vi.clearAllTimers()
  })

  it('Live Mode Should be true', () => {
    mocks.mockUseLiveUpdate.mockReturnValue({
      isLiveModeEnabled: true
    })
    mocks.useRegionExpansion.mockReturnValue({
      isRegionExpanded: false,
      selectedRegion: 'US',
      toggleRegionExpanded: vi.fn(),
      changeSelectedRegion: vi.fn()
    })
    const { store } = renderWithReduxInitialState(<CloseTimeButton />)

    expect(store.getState().report.isLiveModeEnabled).toBe(true)
  })
  it('Check Rendering of Region Buttons on LiveMode is On', () => {
    const initialState = {
      ...stateMock,
      report: {
        isLiveModeEnabled: true,
        currentAsOfUtc: '2023-11-15T10:48:22.2649764Z',
        lastAsOfUtcMetadata: {
          lastDataUpdateTime: '2023-08-18T07:55:00.000Z'
        }
      }
    }
    mocks.useRegionExpansion.mockReturnValue({
      regions,
      isRegionExpanded: false,
      selectedRegion: 'EMEA',
      toggleRegionExpanded: vi.fn(),
      changeSelectedRegion: vi.fn()
    })
    const { store } = renderWithMockedState(<CloseTimeButton />, initialState)
    expect(screen.getByTestId('closeregionbuttons')).toBeInTheDocument()
    expect(store.getState().report.isLiveModeEnabled).toBe(true)
  })
  it('Check Rendering of Region Buttons on LiveMode is Off', async () => {
    const initialState = {
      ...stateMock,
      report: {
        isLiveModeEnabled: false,
        currentAsOfUtc: '2023-11-15T10:48:22.2649764Z',
        lastAsOfUtcMetadata: {
          lastDataUpdateTime: '2023-08-18T07:55:00.000Z'
        }
      }
    }
    mocks.useRegionExpansion.mockReturnValue({
      isRegionExpanded: false,
      selectedRegion: 'APAC',
      toggleRegionExpanded: vi.fn(),
      changeSelectedRegion: vi.fn()
    })
    const { store } = renderWithMockedState(<CloseTimeButton />, initialState)

    expect(screen.getByTestId('closeregionbuttons')).toBeInTheDocument()
    expect(store.getState().report.isLiveModeEnabled).toBe(false)

    // Finding the button
    const button = screen.getByTestId('region-default')

    expect(button).toBeInTheDocument()

    // Click the button
    fireEvent.click(button)

    // when
    act(() =>
      store.dispatch(setCurrentAsOfLocal('2023-11-15T10:48:22.2649764Z'))
    )

    vi.advanceTimersToNextTimer()

    // then
    expect(store.getState().report.currentAsOfUtc).toBe(
      '2023-11-15T10:48:22.264Z'
    )
  })

  it('Check Rendering of Region Buttons on LiveMode is Off and Expanded is on', async () => {
    mocks.mockUseLiveUpdate.mockReturnValue({
      isLiveModeEnabled: false
    })
    mocks.useRegionExpansion.mockReturnValue({
      regions,
      isRegionExpanded: true,
      selectedRegion: 'US',
      toggleRegionExpanded: vi.fn(),
      changeSelectedRegion: vi.fn()
    })

    const initialState = {
      ...stateMock,
      report: {
        isLiveModeEnabled: false,
        currentAsOfUtc: '2023-11-16T10:48:22.2649764Z',
        lastAsOfUtcMetadata: {
          lastDataUpdateTime: '2023-08-19T07:55:00.000Z'
        }
      }
    }
    const { store } = renderWithMockedState(<CloseTimeButton />, initialState)

    expect(screen.getByTestId('closeregionbuttons')).toBeInTheDocument()
    expect(store.getState().report.isLiveModeEnabled).toBe(false)

    // // Finding US button
    const buttonUS = screen.getByTestId('region-us')

    expect(buttonUS).toBeInTheDocument()

    // Finding EMEA button
    const buttonEMEA = screen.getByTestId('region-emea')

    expect(buttonEMEA).toBeInTheDocument()

    // Finding APAC button
    const buttonAPAC = screen.getByTestId('region-apac')

    expect(buttonAPAC).toBeInTheDocument()

    // Click the button
    fireEvent.click(buttonUS)

    // when
    act(() =>
      store.dispatch(setCurrentAsOfLocal('2023-11-16T10:48:22.2649764Z'))
    )

    vi.advanceTimersToNextTimer()

    // then
    expect(store.getState().report.currentAsOfUtc).toBe(
      '2023-11-16T10:48:22.264Z'
    )
  })

  it('should set AsOf to current day 5:45 PM if the time is after 5:45 but before 5:50 HK time', async () => {
    vi.useFakeTimers()
    mocks.useRegionExpansion.mockReturnValue({
      regions,
      isRegionExpanded: true,
      selectedRegion: 'APAC',
      toggleRegionExpanded: vi.fn(),
      changeSelectedRegion: vi.fn()
    })
    mocks.mockUseLiveUpdate.mockReturnValue({
      isLiveModeEnabled: true
    })
    // Mock state with isLiveModeEnabled = false
    const initialState = {
      ...stateMock,
      report: {
        ...stateMock.report,
        currentAsOfUtc: '2025-01-13T08:00:00.000Z'
      }
    }

    const { store } = renderWithMockedState(<CloseTimeButton />, initialState)

    // Mocking the current time to be 9:47 AM HK time
    const mockNow = new Date('2025-01-13T09:47:00.000Z')
    vi.setSystemTime(mockNow)

    // Finding APAC button
    const apacButton = screen.getByTestId('region-apac')
    // Simulate user clicking APAC
    fireEvent.click(apacButton)

    // Advance timers to trigger any time-based logic
    vi.advanceTimersByTime(5000)

    // Expect the AsOf timestamp to be updated to current day's 5:45 PM
    expect(store.getState().report.currentAsOfUtc).toBe(
      '2025-01-13T08:45:00.000Z'
    )
  })
})
