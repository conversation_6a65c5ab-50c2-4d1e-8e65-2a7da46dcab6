import { render } from '@testing-library/react'
import { act } from 'react-dom/test-utils'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import * as closeTimeUtils from '../closeTimeUtils'
import { setKnowledgeAsOfUIVisible } from '@/core/redux/features/report/reportMetadata'
import { getKnowledgeAsOfValuesThunk } from '@/core/redux/features/report/reportMetadata/thunk/getKnowledgeAsOfValuesThunk'
import { useOptionState } from '../useOptionState'

// Mocks for redux hooks and thunk
const dispatchMock = vi.fn(() => Promise.resolve())
const changeCurrentAsOfDateMock = vi.fn(() => Promise.resolve())

vi.mock('@/core/redux/hooks', () => ({
  useAppDispatch: () => dispatchMock,
  useAppSelect: vi.fn().mockReturnValue(false),
  useCurrentAsOfLocal: () => ({
    changeCurrentAsOfDate: changeCurrentAsOfDateMock,
    currentAsOfUtc: '2025-05-20T12:00:00Z'
  })
}))

vi.mock(
  '@/core/redux/features/report/reportMetadata/thunk/getKnowledgeAsOfValuesThunk',
  () => ({
    getKnowledgeAsOfValuesThunk: vi.fn().mockReturnValue('thunkAction')
  })
)

describe('useOptionState handleOptionLogic', () => {
  beforeEach(() => {
    dispatchMock.mockClear()
    changeCurrentAsOfDateMock.mockClear()
  })

  // Helper to expose hook handlers
  const TestComponent = ({ onReady }: { onReady: (handlers: any) => void }) => {
    const handlers = useOptionState()
    onReady(handlers)
    return null
  }

  it('should handle region option logic', async () => {
    const regionOption = closeTimeUtils.optionByName('us')
    const expectedDate = new Date('2025-05-10T00:00:00Z')
    vi.spyOn(closeTimeUtils, 'getCloseDateForRegion').mockReturnValue(
      expectedDate
    )

    let handlers: any
    render(
      <TestComponent
        onReady={(h) => {
          handlers = h
        }}
      />
    )

    await act(async () => {
      await handlers.handleOptionLogic(regionOption)
    })

    expect(dispatchMock).toHaveBeenCalledWith(setKnowledgeAsOfUIVisible(false))
    expect(changeCurrentAsOfDateMock).toHaveBeenCalledWith(expectedDate)
  })

  it('should handle EOD option logic', async () => {
    const eodOption = closeTimeUtils.optionByName('EOD')
    const expectedEodDate = new Date('2025-05-15T00:00:00Z')
    vi.spyOn(closeTimeUtils, 'getEODCloseDateTime').mockReturnValue(
      expectedEodDate
    )

    let handlers: any
    render(
      <TestComponent
        onReady={(h) => {
          handlers = h
        }}
      />
    )

    await act(async () => {
      await handlers.handleOptionLogic(eodOption)
    })

    expect(dispatchMock).toHaveBeenCalledWith(setKnowledgeAsOfUIVisible(true))
    expect(dispatchMock).toHaveBeenCalledWith(getKnowledgeAsOfValuesThunk())
    expect(changeCurrentAsOfDateMock).toHaveBeenCalledWith(expectedEodDate)
  })
})
