import { getEODCloseDateTime } from '../closeTimeUtils'

describe('getEODCloseDateTime', () => {
  const EOD_TIME_UTC = '21:59:59.000Z'

  it('should return the correct EOD time for an input time before EOD', () => {
    // Given
    const inputDateTimeStr = '2025-03-31T14:00:00Z' // Time before EOD

    // When
    const result = getEODCloseDateTime(inputDateTimeStr)

    // Then
    expect(result.toISOString()).toEqual(`2025-03-31T${EOD_TIME_UTC}`)
  })

  it('should return a Date object', () => {
    // Given
    const inputDateTimeStr = '2025-03-31T14:00:00Z'

    // When
    const result = getEODCloseDateTime(inputDateTimeStr)

    // Then
    expect(result).toBeInstanceOf(Date)
  })

  it('should preserve the year, month, and day of the input date string', () => {
    // Given
    const inputDateStr = '2024-07-15T10:30:00Z'
    const inputDate = new Date(inputDateStr)

    // When
    const result = getEODCloseDateTime(inputDateStr)

    // Then
    expect(result.getUTCFullYear()).toEqual(inputDate.getUTCFullYear())
    expect(result.getUTCMonth()).toEqual(inputDate.getUTCMonth())
    expect(result.getUTCDate()).toEqual(inputDate.getUTCDate())
    // July 15, 2024 is during DST, so EOD_TIME_UTC should be the DST EOD time.
    expect(result.toISOString().endsWith(`T${EOD_TIME_UTC}`)).toBe(true)
  })

  it('should handle dates around daylight saving time transitions correctly', () => {
    // Given
    // US DST 2024: Starts Sunday, March 10; Ends Sunday, November 3.
    // These tests use UTC dates, so DST in local zones should not affect the outcome.
    const dateDuringDSTTransitionStart = '2024-03-10T10:00:00Z' // Day DST starts in US
    const dateDuringDSTTransitionEnd = '2024-11-03T10:00:00Z' // Day DST ends in US

    // When
    const resultDSTStart = getEODCloseDateTime(dateDuringDSTTransitionStart)
    const resultDSTEnd = getEODCloseDateTime(dateDuringDSTTransitionEnd)

    // Then
    // Assuming EOD_TIME_UTC is "21:59:59.000Z" (e.g., 5 PM EDT)
    // For March 10, 2024 (DST in effect), EOD is 21:59:59 UTC.
    expect(resultDSTStart.toISOString()).toEqual(`2024-03-10T${EOD_TIME_UTC}`)
    // For November 3, 2024 (Standard Time in effect), EOD is 22:59:59 UTC (e.g., 5 PM EST).
    expect(resultDSTEnd.toISOString()).toEqual('2024-11-03T22:59:59.000Z')
  })

  it('should return correct EOD time when input time is after EOD on the same day', () => {
    // Given
    const inputDateTimeStr = '2025-03-31T23:00:00Z' // Time after EOD

    // When
    const result = getEODCloseDateTime(inputDateTimeStr)

    // Then
    expect(result.toISOString()).toEqual(`2025-03-31T${EOD_TIME_UTC}`)
  })

  it('should return correct EOD time when input time is exactly EOD', () => {
    // Given
    const inputDateTimeStr = `2025-03-31T${EOD_TIME_UTC}` // Exactly EOD time

    // When
    const result = getEODCloseDateTime(inputDateTimeStr)

    // Then
    expect(result.toISOString()).toEqual(`2025-03-31T${EOD_TIME_UTC}`)
  })

  it('should return correct EOD time for an input at the beginning of the day (00:00:00Z)', () => {
    // Given
    const inputDateTimeStr = '2025-04-01T00:00:00Z'

    // When
    const result = getEODCloseDateTime(inputDateTimeStr)

    // Then
    expect(result.toISOString()).toEqual(`2025-04-01T${EOD_TIME_UTC}`)
  })

  it('should return correct EOD time for an input at the end of the day (23:59:59.999Z)', () => {
    // Given
    const inputDateTimeStr = '2025-04-01T23:59:59.999Z'

    // When
    const result = getEODCloseDateTime(inputDateTimeStr)

    // Then
    expect(result.toISOString()).toEqual(`2025-04-01T${EOD_TIME_UTC}`)
  })
})
