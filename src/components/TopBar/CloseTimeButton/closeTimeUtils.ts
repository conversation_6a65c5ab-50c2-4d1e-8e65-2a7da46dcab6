import moment from 'moment-timezone'

export type CloseTimeOptionName = 'EOD' | 'emea' | 'us' | 'apac'

export interface CloseTimeOption {
  name: CloseTimeOptionName
  label: string
  timezone: string
  closeTimeUTCsummer: string
  closeTimeUTCwinter: string
  group: 'region' | 'eod'
}

export const options: CloseTimeOption[] = [
  {
    name: 'EOD',
    label: 'EOD',
    closeTimeUTCsummer: '21:59:59',
    closeTimeUTCwinter: '22:59:59',
    timezone: 'America/New_York',
    group: 'eod'
  },
  {
    name: 'us',
    label: 'US',
    timezone: 'America/New_York',
    closeTimeUTCsummer: '21:59:00',
    closeTimeUTCwinter: '22:59:00',
    group: 'region'
  },
  {
    name: 'emea',
    label: 'EMEA',
    timezone: 'Europe/London',
    closeTimeUTCsummer: '15:20:00',
    closeTimeUTCwinter: '16:20:00',
    group: 'region'
  },
  {
    name: 'apac',
    label: 'APAC',
    timezone: 'Asia/Hong_Kong',
    closeTimeUTCsummer: '09:45:00',
    closeTimeUTCwinter: '09:45:00',
    group: 'region'
  }
]

export const isSummerTime = (
  currentUTCDate: Date,
  timezone: string
): boolean => {
  const momentDate = moment.tz(currentUTCDate, timezone)
  return momentDate.isDST() // Returns true if it's summer time (DST), false otherwise
}

export const getCloseDateForRegion = (
  region: CloseTimeOption,
  currentUTCDate = new Date()
): Date => {
  const closeTime = isSummerTime(currentUTCDate, region.timezone)
    ? region.closeTimeUTCsummer
    : region.closeTimeUTCwinter

  const regionCloseTimeUTC = new Date(
    currentUTCDate.toISOString().split('T')[0] + `T${closeTime}Z`
  )

  const regionCurrentMoment = moment.tz(currentUTCDate, region.timezone)
  const regionCloseMoment = moment.tz(regionCloseTimeUTC, region.timezone)

  //if it's Sunday substract 2 days = Friday
  if (regionCurrentMoment.day() === 0) {
    return regionCloseMoment.subtract(2, 'day').toDate()
  }

  //if it's Saturday substract 1 day = Friday
  if (regionCurrentMoment.day() === 6) {
    return regionCloseMoment.subtract(1, 'day').toDate()
  }

  // if currentMoment is after closeMoment, we dont need to subtract days
  if (regionCurrentMoment.isBefore(regionCloseMoment)) {
    // Use the previous day's close time
    // unless it is Monday, then substract 3 day = Friday
    const daysToMinus = regionCurrentMoment.day() === 1 ? 3 : 1
    return regionCloseMoment.subtract(daysToMinus, 'day').toDate()
  }

  return regionCloseMoment.toDate()
}

export const getEODCloseDateTime = (selectedAsOfUtc?: string): Date => {
  const eodOption = optionByName('EOD')
  const eodSelectedMoment = moment.utc(selectedAsOfUtc)

  const usCloseTime = getCloseDateForRegion(optionByName('us'))
  const nyCloseMoment = moment.utc(usCloseTime)

  if (nyCloseMoment.isSameOrBefore(eodSelectedMoment)) {
    return nyCloseMoment.toDate()
  } else {
    const closeTime = isSummerTime(
      eodSelectedMoment.toDate(),
      eodOption.timezone
    )
      ? eodOption.closeTimeUTCsummer
      : eodOption.closeTimeUTCwinter
    const datePart = eodSelectedMoment.toDate().toISOString().split('T')[0]

    return new Date(`${datePart}T${closeTime}Z`)
  }
}

export const optionByName = (name?: CloseTimeOptionName): CloseTimeOption =>
  options.find((region) => region.name === name) ?? options[0]
