import { DateTimeCalendar } from '@/components/Shared/DateTimeCalendar'
import { userThemeSelector } from '@/core/redux/features/users/selectors/userThemeSelector'
import { useAppSelect, useCurrentAsOfLocal } from '@/core/redux/hooks'
import { Icon, PageHeaderItem, TooltipWrapper } from '@core-clib/react'
import { FC, memo, useState } from 'react'
import styles from './CurrentAsOfLocal.module.scss'
import { isKnowledgeAsOfUIVisibleSelector } from '@/core/redux/features/report/reportMetadata/selectors'

const CurrentAsOfLocalComponent: FC = () => {
  const theme = useAppSelect(userThemeSelector)
  const isKnowledgeAsOfUIVisible = useAppSelect(
    isKnowledgeAsOfUIVisibleSelector
  )
  const {
    currentAsOfLocal,
    currentAsOfLocalPrecise,
    currentAsOfUtcPrecise,
    handleDateChange
  } = useCurrentAsOfLocal()

  const [isCalendarOpen, changeCalendarVisibility] = useState(false)
  const toggleCalendar = () => changeCalendarVisibility((prev) => !prev)

  const onTodaySelected = (date: Date) => {
    handleDateChange(date)
  }

  return (
    <div className="item-wrapper datepicker-container">
      <PageHeaderItem label="As of">
        <div className="datetime-plug-wrapper">
          <div className="datetime-plug">
            <TooltipWrapper
              tippyOptions={{
                content: `<div class="tippy-tooltip ${theme}">${currentAsOfLocalPrecise} (local)<br>${currentAsOfUtcPrecise} (UTC)</div>`,
                allowHTML: true
              }}
            >
              <span>{currentAsOfLocal}</span>
            </TooltipWrapper>
            <Icon
              className="calendarIcon"
              name="ci-calendar"
              onClick={toggleCalendar}
              aria-label="Toggle calendar"
            />
          </div>
        </div>
      </PageHeaderItem>
      <div className={styles.calendar}>
        <DateTimeCalendar
          isOpen={isCalendarOpen}
          onCancel={toggleCalendar}
          onApply={handleDateChange}
          dateTime={currentAsOfLocal}
          onSetToday={onTodaySelected}
          time={!isKnowledgeAsOfUIVisible}
        />
      </div>
    </div>
  )
}

export const CurrentAsOfLocal = memo(CurrentAsOfLocalComponent)
