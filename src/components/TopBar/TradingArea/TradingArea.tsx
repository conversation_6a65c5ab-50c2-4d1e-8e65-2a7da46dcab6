import { HeaderTypeahead } from '@core-clib/react'
import { FC, useEffect, useState } from 'react'
import { useTradingAreaSelect } from './useTradingAreaSelect'
import './tradingArea.scss'
import { SDKOptionType } from './types'

// react component Books
export const TradingArea: FC = () => {
  const [render, doRender] = useState(false)
  const { allItems, selectedItems, handleChange } = useTradingAreaSelect()

  // To fix https://prdjira.saccap.int/browse/MACROUIUX-1508 we need to render the component to the bottom of the stack
  // It becomes a problem when we have updated core sdk to 1.98.8
  useEffect(() => {
    setTimeout(() => doRender(true), 0)
  }, [])

  return render ? (
    <HeaderTypeahead
      options={allItems}
      isMulti={true}
      checkboxMode={true}
      showAllOption={true}
      value={selectedItems}
      onChange={(items) => handleChange(items as SDKOptionType[])}
      className="tradingAreaContainer"
    />
  ) : null
}
