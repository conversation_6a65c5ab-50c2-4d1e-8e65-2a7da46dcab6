import { setSelectedTradingAreas } from '@/core/redux/features/report'
import { useAppDispatch, useAppSelect } from '@/core/redux/hooks'
import { SDKOptionType } from './types'

export const useTradingAreaSelect = () => {
  const selectedTradingAreas = useAppSelect(
    (state) => state.report.selectedTradingAreas
  )
  const tradingAreas = useAppSelect((state) => state.report.tradingAreas)

  const dispatch = useAppDispatch()

  const handleItemsChange = (newValue: SDKOptionType[]) => {
    const newSelectedTradingAreas = newValue.map((item) => item.id)
    dispatch(setSelectedTradingAreas(newSelectedTradingAreas))
  }

  return {
    allItems: tradingAreas.map(toSDKOptionType),
    selectedItems: selectedTradingAreas?.map(toSDKOptionType),
    handleChange: handleItemsChange
  }
}

const toSDKOptionType = (item: string): SDKOptionType => ({
  id: item,
  label: item
})
