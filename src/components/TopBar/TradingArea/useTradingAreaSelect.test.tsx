import { ReduxWrapper } from '@/core/testing'
import { cleanup, renderHook, waitFor } from '@testing-library/react'
import { describe, it, vi } from 'vitest'
import { useTradingAreaSelect } from './useTradingAreaSelect'

vi.mock('@/core/redux/features/report/thunk/riskReportSummaryThunk')

describe('useTradingAreaSelect', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    cleanup()
  })

  const storeMock = {
    report: {
      reportName: 'pnl-live',
      tradingAreas: ['BHIR', 'SOFI', 'ARNO', 'BALT', 'BLUM', 'CLEV'].sort(),
      selectedTradingAreas: ['BHIR', 'SOFI'].sort(),
      currentAsOfUtc: '2021-08-10T00:00:00Z',
      lastAsOfUtcMetadata: {},
      isInitialized: true
    },
    grid: {},
    notifications: {},
    presets: {
      userPresets: [],
      sharedPresets: []
    },
    users: {},
    epicApi: {}
  }

  it('should set the initial `selectedTradingAreas` according to those fetched from the API and sort it', async () => {
    const { result } = renderHook(useTradingAreaSelect, {
      wrapper: ReduxWrapper(storeMock)
    })

    // then
    await waitFor(() => {
      expect(result.current.selectedItems).toStrictEqual([
        {
          id: 'BHIR',
          label: 'BHIR'
        },
        {
          id: 'SOFI',
          label: 'SOFI'
        }
      ])

      expect(result.current.allItems).toStrictEqual([
        {
          id: 'ARNO',
          label: 'ARNO'
        },
        {
          id: 'BALT',
          label: 'BALT'
        },
        {
          id: 'BHIR',
          label: 'BHIR'
        },
        {
          id: 'BLUM',
          label: 'BLUM'
        },
        {
          id: 'CLEV',
          label: 'CLEV'
        },
        {
          id: 'SOFI',
          label: 'SOFI'
        }
      ])
    })
  })
})
