import { AgGridReact } from '@ag-grid-community/react'
import { FC, PropsWithChildren, createContext, useRef } from 'react'

interface GridContextType {
  gridApiRef: React.RefObject<AgGridReact>
}

// gridApi are not serializable, so we can't store them in redux
export const GridContext = createContext<GridContextType>({
  gridApiRef: { current: null }
})

export interface GridProviderProps {
  gridApiRef?: React.RefObject<AgGridReact>
}

// GridProvider component to wrap around components that need to use the useGrid hook
export const GridProvider: FC<PropsWithChildren<GridProviderProps>> = (
  props
) => {
  const gridApiRef = useRef<AgGridReact>(props.gridApiRef?.current || null)

  return (
    <GridContext.Provider
      value={{
        gridApiRef
      }}
    >
      {props.children}
    </GridContext.Provider>
  )
}
