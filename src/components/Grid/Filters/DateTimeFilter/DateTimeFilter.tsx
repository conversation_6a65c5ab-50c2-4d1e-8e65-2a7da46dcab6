import { forwardRef, useImperativeHandle, useState } from 'react'

import { IAction } from '@core-clib/web-components/types'
import { Dropdown } from '@/components/Shared/Dropdown'
import { IFilterParams } from '@ag-grid-community/core'
import { useDateTimeFilter } from './useDateTimeFilter'
import { DateTimeInput } from '@/components/Shared/DateTimeInput'
import {
  formatDateToISOString,
  formatTimestampToLocalWithMilliseconds,
  formatDateToISOStringWithZeroMilliseconds
} from '@/core/utils/date'
import { IFilterReactComp } from '@ag-grid-community/react'
import {
  FilterAction,
  FilterActions,
  getAgGridFilterOperator,
  getFilterAction
} from './consts'
import { AgGridFilterOperator } from '@/core/services/grid'
import { useFilterOptions } from './useFilterOptions'
import { useApplyFilterValueEffect } from './useApplyFilterValueEffect'
import { useAppDispatch } from '@/core/redux/hooks'
import { onExecutionTimeFilterSelectedThunk } from '@/core/redux/features/quickFilters/executionTimeFilter/thunks/onExecutionTimeFilterSelectedThunk'
import { FilterOptions } from '@/core/redux/features/quickFilters/executionTimeFilter/types'
import { EXECUTION_TIME } from '@/core/redux/features/quickFilters/executionTimeFilter/constants'

export const DateTimeFilter = forwardRef((props: IFilterParams, ref) => {
  const { options, defaultFilterAction } = useFilterOptions(props)
  const dispatch = useAppDispatch()

  const [selectedAction, setSelectedAction] =
    useState<FilterAction>(defaultFilterAction)
  const { onChangeDate, filterValue, setFilterValue } = useDateTimeFilter()
  const {
    onChangeDate: onChangeDateTo,
    filterValue: filterValueTo,
    setFilterValue: setFilterValueTo
  } = useDateTimeFilter()

  const { isActiveRef } = useApplyFilterValueEffect({
    filterValue,
    filterValueTo,
    selectedAction,
    props
  })

  // TODO : refactor to useDateTimeFilterModel
  useImperativeHandle(ref, (): IFilterReactComp => {
    return {
      doesFilterPass() {
        throw new Error('Method not implemented.')
      },

      isFilterActive() {
        return isActiveRef.current
      },

      getModel() {
        let model = null
        let dateFrom = undefined
        let dateTo = undefined

        if (filterValue !== undefined) {
          if (
            selectedAction === FilterActions.IN_RANGE &&
            filterValueTo === undefined
          ) {
            // If the selected action is BETWEEN and the second date is not set,
            // we should not return the model yet
            return null
          }

          try {
            if (props?.colDef?.refData?.type === 'Date') {
              dateFrom = formatDateToISOStringWithZeroMilliseconds(filterValue)
            } else {
              dateFrom = new Date(
                formatTimestampToLocalWithMilliseconds(filterValue)
              ).toISOString()
            }
          } catch (e) {
            return null
          }

          model = {
            filterType: 'date',
            type: getAgGridFilterOperator(selectedAction),
            dateFrom
          }

          if (selectedAction === FilterActions.IN_RANGE && filterValueTo) {
            try {
              dateTo = new Date(filterValueTo).toISOString()
            } catch (e) {
              return null
            }

            model = {
              ...model,
              dateTo
            }
          }
        }
        return model
      },

      setModel(model) {
        if (model?.dateFrom) {
          setFilterValue(formatDateToISOString(model.dateFrom))
          setSelectedAction(getFilterAction(model.type))
          if (model.type === AgGridFilterOperator.InRange && model.dateTo) {
            setFilterValueTo(formatDateToISOString(model.dateTo))
          }
        }

        if (model === null || model === undefined) {
          clearFilters(setFilterValue)()
          clearFilters(setFilterValueTo)()
        }
      }
    }
  })

  const handleExecutionTimeFilterUpdate = () => {
    props.colDef?.field === EXECUTION_TIME &&
      dispatch(onExecutionTimeFilterSelectedThunk(FilterOptions.DATES))
  }

  const clearFilters =
    (setValueCallback: (x: string | undefined) => void) => () => {
      setValueCallback(undefined)
      setSelectedAction(defaultFilterAction)
      handleExecutionTimeFilterUpdate()

      /* Without waiting for state variable update, if we call filterChangedCallback, it is resetting the Filter value to selected one for the first time. So,added SetTimeout here */
      setTimeout(() => {
        props.filterChangedCallback()
      }, 0)
    }

  return (
    <div className="ag-custom-component-popup">
      <Dropdown
        options={options}
        selectedOptionId={selectedAction}
        onSelected={(action: IAction) => {
          setSelectedAction(action.id as FilterAction)
        }}
      />
      {selectedAction !== FilterActions.BLANK &&
        selectedAction !== FilterActions.NOT_BLANK && (
          <DateTimeInput
            dateChangeHandler={(date: Date) => {
              onChangeDate(date)
              handleExecutionTimeFilterUpdate()
            }}
            onClear={clearFilters(setFilterValue)}
            value={filterValue}
            time={props.colDef.refData?.type === 'DateTime'}
          />
        )}
      {selectedAction === FilterActions.IN_RANGE && (
        <DateTimeInput
          dateChangeHandler={(date: Date) => {
            onChangeDateTo(date)
            handleExecutionTimeFilterUpdate()
          }}
          onClear={clearFilters(setFilterValueTo)}
          value={filterValueTo}
          time={props.colDef.refData?.type === 'DateTime'}
        />
      )}
    </div>
  )
})
