import { useEffect, useRef } from 'react'
import { IFilterParams } from '@ag-grid-community/core'
import { EXECUTION_TIME } from '@/core/redux/features/quickFilters/executionTimeFilter/constants'
import { useAppDispatch, useAppSelect } from '@/core/redux/hooks'
import { isTradesReportSelector } from '@/core/redux/selectors'
import { onExecutionTimeDatesChangesThunk } from '@/core/redux/features/quickFilters/executionTimeFilter/thunks/onExecutionTimeDatesChangedThunk'
import { FilterAction, FilterActions } from './consts'

export const useApplyFilterValueEffect = ({
  filterValue,
  filterValueTo,
  selectedAction,
  props
}: {
  filterValue: string | undefined
  filterValueTo: string | undefined
  selectedAction: FilterAction
  props: IFilterParams
}) => {
  const dispatch = useAppDispatch()
  const isTrades = useAppSelect(isTradesReportSelector)
  const isQuickFilterRelated =
    props.colDef?.field === EXECUTION_TIME && isTrades

  const isActiveRef = useRef(false)

  useEffect(() => {
    isActiveRef.current = isFilterActive(
      filterValue,
      filterValueTo,
      selectedAction
    )
  }, [filterValue, selectedAction])

  useEffect(() => {
    const isActive = isFilterActive(filterValue, filterValueTo, selectedAction)

    if (isActive || isActiveRef.current !== isActive) {
      props?.filterChangedCallback() // call this to update filter Icon in agGrid (and apply filter)
    }

    if (selectedAction === FilterActions.IN_RANGE && !isActive) {
      props?.filterChangedCallback() // call this to update filter Icon in agGrid (clear inactive)
    }

    isActiveRef.current = isActive

    if (isActive && isQuickFilterRelated) {
      // TODO: decouple from quick filter, dispatch generic onFilterValueChangedThunk(field, value) and handle isQuickFilterRelated logic there
      dispatch(
        onExecutionTimeDatesChangesThunk({
          dateFrom: filterValue
            ? new Date(filterValue).toISOString()
            : undefined,
          dateTo: filterValueTo
            ? new Date(filterValueTo).toISOString()
            : undefined
        })
      )
    }
  }, [filterValue, filterValueTo, selectedAction])

  return {
    isActiveRef
  }
}

const isFilterActive = (
  filterValue: string | undefined,
  filterValueTo: string | undefined,
  selectedAction: FilterAction
) => {
  const filtersWithoutValue = [
    FilterActions.BLANK,
    FilterActions.NOT_BLANK
  ] as FilterAction[]

  const isFilterWithoutValue = filtersWithoutValue.includes(selectedAction)

  const isInRangeFilter =
    selectedAction === FilterActions.IN_RANGE && filterValue && filterValueTo

  const isOtherFilter = filterValue && selectedAction !== FilterActions.IN_RANGE

  return Boolean(isFilterWithoutValue || isInRangeFilter || isOtherFilter)
}
