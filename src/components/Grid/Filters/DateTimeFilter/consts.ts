import { AgGridFilterOperator } from '@/core/services/grid'

export const FilterActions = {
  EQUALS: 'Equals',
  DOES_NOT_EQUAL: 'Not Equal',
  BEFORE: 'Less than',
  AFTER: 'Greater than',
  IN_RANGE: 'In Range',
  BLANK: 'Blank',
  NOT_BLANK: 'Not Blank'
} as const

export type FilterAction = (typeof FilterActions)[keyof typeof FilterActions]

export const ActionOperatorMap = [
  { action: FilterActions.EQUALS, operator: AgGridFilterOperator.Equals },
  {
    action: FilterActions.DOES_NOT_EQUAL,
    operator: AgGridFilterOperator.NotEqual
  },
  { action: FilterActions.BEFORE, operator: AgGridFilterOperator.LessThan },
  { action: FilterActions.AFTER, operator: AgGridFilterOperator.GreaterThan },
  { action: FilterActions.IN_RANGE, operator: AgGridFilterOperator.InRange },
  { action: FilterActions.BLANK, operator: AgGridFilterOperator.Blank },
  { action: FilterActions.NOT_BLANK, operator: AgGridFilterOperator.NotBlank }
]

export const getAgGridFilterOperator = (action: FilterAction) => {
  const mapping = ActionOperatorMap.find((map) => map.action === action)
  return mapping ? mapping.operator : AgGridFilterOperator.Equals
}

export const getFilterAction = (operator: AgGridFilterOperator) => {
  const mapping = ActionOperatorMap.find((map) => map.operator === operator)
  return mapping ? mapping.action : FilterActions.EQUALS
}
