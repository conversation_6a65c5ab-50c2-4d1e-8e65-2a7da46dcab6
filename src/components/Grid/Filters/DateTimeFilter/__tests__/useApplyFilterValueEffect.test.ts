import { useApplyFilterValueEffect } from '../useApplyFilterValueEffect'
import { FilterActions } from '../consts'
import { IFilterParams } from '@ag-grid-community/core'
import { renderHookWithRedux } from '@/core/testing'
import { vi } from 'vitest'
import { be } from 'date-fns/locale'

describe('useApplyFilterValueEffect', () => {
  const filterParamsMock = {
    filterChangedCallback: vi.fn()
  } as unknown as IFilterParams

  beforeEach(() => {
    vi.resetAllMocks()
  })

  it('should return true if selectedAction is not BLANK or NOT_BLANK and  filterValue is truthy', () => {
    const { isActiveRef } = renderHookWithRedux(() =>
      useApplyFilterValueEffect({
        filterValue: 'filterValue',
        filterValueTo: 'filterValueTo',
        selectedAction: FilterActions.EQUALS,
        props: filterParamsMock
      })
    )

    expect(isActiveRef.current).toBe(true)
  })

  it('should return false if selectedAction is not BLANK or NOT_BLANK and  filterValue is falsy', () => {
    const { isActiveRef } = renderHookWithRedux(() =>
      useApplyFilterValueEffect({
        filterValue: undefined,
        filterValueTo: undefined,
        selectedAction: FilterActions.EQUALS,
        props: filterParamsMock
      })
    )

    expect(isActiveRef.current).toBe(false)
  })

  it('should return true if selectedAction is BLANK or NOT_BLANK', () => {
    const { isActiveRef } = renderHookWithRedux(() =>
      useApplyFilterValueEffect({
        filterValue: undefined,
        filterValueTo: undefined,
        selectedAction: FilterActions.BLANK,
        props: filterParamsMock
      })
    )

    expect(isActiveRef.current).toBe(true)
  })

  it('should return true if selectedAction is IN_RANGE and filterValueTo is truthy', () => {
    const { isActiveRef } = renderHookWithRedux(() =>
      useApplyFilterValueEffect({
        filterValue: 'filterValue',
        filterValueTo: 'filterValueTo',
        selectedAction: FilterActions.IN_RANGE,
        props: filterParamsMock
      })
    )

    expect(isActiveRef.current).toBe(true)
  })

  it('should return false if selectedAction is IN_RANGE and filterValueTo is falsy', () => {
    const { isActiveRef } = renderHookWithRedux(() =>
      useApplyFilterValueEffect({
        filterValue: 'filterValue',
        filterValueTo: undefined,
        selectedAction: FilterActions.IN_RANGE,
        props: filterParamsMock
      })
    )

    expect(isActiveRef.current).toBe(false)
  })

  it('should call filterChangedCallback if value change', () => {
    renderHookWithRedux(() =>
      useApplyFilterValueEffect({
        filterValue: 'filterValue',
        filterValueTo: undefined,
        selectedAction: FilterActions.EQUALS,
        props: filterParamsMock
      })
    )

    // change value
    renderHookWithRedux(() =>
      useApplyFilterValueEffect({
        filterValue: 'filterValue2',
        filterValueTo: 'filterValueTo2',
        selectedAction: FilterActions.IN_RANGE,
        props: filterParamsMock
      })
    )

    expect(filterParamsMock.filterChangedCallback).toHaveBeenCalledTimes(2)
  })
})
