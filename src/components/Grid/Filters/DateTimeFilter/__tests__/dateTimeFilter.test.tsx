import { AgGridFilterOperator } from '@/core/services/grid'
import { renderWithReduxInitialState } from '@/core/testing'
import { IFilterParams } from '@ag-grid-community/core'
import { IFilterReactComp } from '@ag-grid-community/react'
import { fireEvent } from '@testing-library/react'
import { createRef } from 'react'
import { vi } from 'vitest'
import { FilterActions } from '../consts'
import { DateTimeFilter } from '../DateTimeFilter'

describe('DateTimeFilter', () => {
  let filterParams: IFilterParams

  beforeEach(() => {
    filterParams = {
      context: null,
      filterChangedCallback: vi.fn(),
      colDef: {
        refData: { type: 'date' }
      }
    } as any
  })

  it('should render correctly', () => {
    const { getByText } = renderWithReduxInitialState(
      <DateTimeFilter {...filterParams} />
    )
    expect(getByText(FilterActions.EQUALS)).toBeInTheDocument()
  })

  it('should handle action selection', () => {
    const { getByText, getByRole } = renderWithReduxInitialState(
      <DateTimeFilter {...filterParams} />
    )
    const dropdown = getByRole('combobox', { name: /filter options/i })
    fireEvent.change(dropdown, { target: { value: FilterActions.AFTER } })
    expect(getByText(FilterActions.AFTER)).toBeInTheDocument()
  })

  it('should return the correct model when getModel is called', async () => {
    const ref = createRef<IFilterReactComp>()
    const filterParams = {
      filterChangedCallback: vi.fn(),
      colDef: {
        refData: { type: 'date' }
      }
    } as any

    const Component = <DateTimeFilter ref={ref} {...filterParams} />

    const { rerender } = renderWithReduxInitialState(Component)

    await ref.current?.setModel({
      filterType: 'date',
      type: 'equals',
      dateFrom: new Date('2024-04-26T14:03:00.000Z').toISOString()
    })

    rerender(Component)

    const model = ref.current?.getModel()

    expect(model).toEqual({
      filterType: 'date',
      type: AgGridFilterOperator.Equals,
      dateFrom: expect.stringMatching(/2024-04-26T14:03:00.\d{3}Z/)
    })
  })

  it('getModel should return null for selectedAction BETWEEN when filterValueTo is undefined', async () => {
    const ref = createRef<IFilterReactComp>()
    const filterParams = {
      filterChangedCallback: vi.fn(),
      colDef: {
        refData: { type: 'date' }
      }
    } as any

    const Component = <DateTimeFilter ref={ref} {...filterParams} />

    const { rerender } = renderWithReduxInitialState(Component)

    await ref.current?.setModel({
      filterType: 'date',
      type: AgGridFilterOperator.InRange,
      dateFrom: new Date('2024-04-26T14:03:00.000Z').toISOString(),
      dateTo: undefined
    })

    rerender(Component)

    const model = ref.current?.getModel()

    expect(model).toBeNull()
  })
})
