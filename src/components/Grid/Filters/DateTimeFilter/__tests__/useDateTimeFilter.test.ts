import { useDateTimeFilter } from '@/components/Grid/Filters/DateTimeFilter/useDateTimeFilter'
import { getReduxWrapper } from '@/core/testing'
import { act, renderHook, waitFor } from '@testing-library/react'

// skip as it's flaky and this functionality is tested by quick filters integration test
describe('useDateTimeFilter', () => {
  const { wrapper } = getReduxWrapper({
    users: {
      userPreferences: {
        lastUsedPresetId: 'testPresetId'
      }
    }
  })

  it('should handle date and time change correctly', async () => {
    const { result } = renderHook(() => useDateTimeFilter(), { wrapper })
    const date = new Date('2023-10-10T15:30:00')

    act(() => {
      // Change date and time
      result.current.onChangeDate(date)
    })

    // Verify the filter value has been updated correctly
    await waitFor(() =>
      expect(result.current.filterValue).toBe('2023-10-10 15:30:00.0000000')
    )
  })

  it('should handle invalid date correctly', async () => {
    const { result } = renderHook(() => useDateTimeFilter(), { wrapper })

    // Change date with invalid value
    result.current.onChangeDate('invalid-date' as any)

    // Verify the filter value has not been updated
    await waitFor(() => expect(result.current.filterValue).toBeUndefined())
  })
})
