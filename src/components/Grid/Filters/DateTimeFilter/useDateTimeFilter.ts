import { useLastUsedPresetId } from '@/core/redux/features/users/selectors/useLastUsedPresetId'
import { formatTimestampToLocalWithMilliseconds } from '@/core/utils/date'
import { useEffect, useState } from 'react'

interface DateTimeFilter {
  onChangeDate: (date: Date) => void
  filterValue: string | undefined
  setFilterValue: (value: string | undefined) => void
}

export const useDateTimeFilter = (): DateTimeFilter => {
  const presetId = useLastUsedPresetId() ?? 'default'
  const [filterValues, setFilterValues] = useState<
    Record<string, string | undefined>
  >({})

  useEffect(() => {
    if (!filterValues[presetId]) {
      // Reset the filter value when the preset changes
      // otherwise the filter value will be kept between presets
      setFilterValues((prevValues) => ({
        ...prevValues,
        [presetId]: undefined
      }))
    }
  }, [presetId])

  const setFilterValue = (value: string | undefined) => {
    // Update the filter value for the current preset
    setFilterValues((prevValues) => ({ ...prevValues, [presetId]: value }))
  }

  const filterValue = filterValues[presetId]

  const onChangeDate = (date: Date) => {
    try {
      setFilterValue(formatTimestampToLocalWithMilliseconds(date.toISOString()))
    } catch (e) {
      setFilterValue(undefined)
    }
  }

  return { onChangeDate, filterValue, setFilterValue }
}
