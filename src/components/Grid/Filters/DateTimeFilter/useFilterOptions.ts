import { EXECUTION_TIME } from '@/core/redux/features/quickFilters/executionTimeFilter/constants'
import { IAction } from '@core-clib/web-components/types'
import { IFilterParams } from '@ag-grid-community/core'
import { FilterAction, FilterActions } from './consts'
import { useAppSelect } from '@/core/redux/hooks'
import { isTradesReportSelector } from '@/core/redux/selectors'

const options: IAction[] = Object.values(FilterActions).map((action) => ({
  id: action,
  label: action
}))

export const useFilterOptions = (props: IFilterParams) => {
  const { colDef } = props
  const isTrades = useAppSelect(isTradesReportSelector)
  const isQuickFilterRelated = colDef?.field === EXECUTION_TIME && isTrades

  let filterOptions = [...options]
  let defaultFilterAction: FilterAction = FilterActions.EQUALS

  if (isQuickFilterRelated) {
    defaultFilterAction = FilterActions.IN_RANGE
    filterOptions = options.filter(
      (option) => option.id === FilterActions.IN_RANGE
    )
  }

  return {
    options: filterOptions,
    defaultFilterAction
  }
}
