import { useMemo } from 'react'
import { add, format, parseISO } from 'date-fns'
import { enUS } from 'date-fns/locale'
import { useAppSelect } from '@/core/redux/hooks'
import { isDateOlderThanFiveMinutes } from '@/core/utils/date'

const formatDateForStatusMessage = (isoString: string | null): string => {
  if (!isoString) {
    return '-'
  }

  const isoDate = parseISO(isoString)
  return format(isoDate, 'hh:mm:ss a', { locale: enUS })
}

export const getStatusMessage = {
  healthy: (
    lastDataUpdateTime: string | null,
    lastStatusPollTime: string | null
  ) =>
    `Live updates healthy (last data update received at ${formatDateForStatusMessage(
      lastDataUpdateTime
    )}, last status poll received at ${formatDateForStatusMessage(
      lastStatusPollTime
    )})`,
  delayed: (
    lastDataUpdateTime: string | null,
    lastStatusPollTime: string | null
  ) =>
    `Live updates delayed (last data update received at ${formatDateForStatusMessage(
      lastDataUpdateTime
    )}, last status poll received at ${formatDateForStatusMessage(
      lastStatusPollTime
    )})`,
  interrupted: (
    lastDataUpdateTime: string | null,
    nextStatusPollTime: string | null,
    httpCode?: number
  ) =>
    `Live updates temporarily interrupted (HTTP code ${
      httpCode || '-'
    }, last data update received at ${formatDateForStatusMessage(
      lastDataUpdateTime
    )}, next status poll at ${formatDateForStatusMessage(nextStatusPollTime)})`,
  uncertain: (
    lastDataUpdateTime: string | null,
    nextStatusPollTime: string | null
  ) =>
    `Live update status uncertain, contact Support if this persists for a prolonged period (last data update received at ${formatDateForStatusMessage(
      lastDataUpdateTime
    )}, next status poll attempt at ${formatDateForStatusMessage(
      nextStatusPollTime
    )})`,
  disabled: (lastDataUpdateTime: string | null) =>
    `Live updates disabled (last data update received at ${formatDateForStatusMessage(
      lastDataUpdateTime
    )})`
}

const getNextStatusPollingRequestTime = (retryAfterSeconds: number): string => {
  const nextRequestTime = add(new Date(), { seconds: retryAfterSeconds })
  return nextRequestTime.toISOString()
}

/**
 * Returns the current status message of the live mode.
 * disabled - when the live mode is disabled.
 * interrupted - when the error with a response with Retry-After header was received.
 * uncertain - when another error was received.
 * delayed - when last data update timestamp is more than 5 min old.
 * healthy - all other cases.
 */
export const useLiveModeStatus = () => {
  const lastDataUpdateTime = useAppSelect(
    (state) => state.report.lastAsOfUtcMetadata.lastDataUpdateTime
  )
  const receivedTime = useAppSelect(
    (state) => state.report.lastAsOfUtcMetadata.receivedTime
  )
  const error = useAppSelect((state) => state.report.lastAsOfUtcMetadata.error)
  const isLiveModeEnabled = useAppSelect(
    (state) => state.report.isLiveModeEnabled
  )

  const isLastDataUpdateDelayed = useMemo(() => {
    if (!lastDataUpdateTime) {
      return false
    }

    return isDateOlderThanFiveMinutes(lastDataUpdateTime)
  }, [lastDataUpdateTime])

  const nextStatusPollingRequestTime: string | null = useMemo(() => {
    const retryAfterSeconds = error?.retryAfterSeconds
    return retryAfterSeconds
      ? getNextStatusPollingRequestTime(retryAfterSeconds)
      : null
  }, [error])

  const statusMessage = useMemo(() => {
    if (!isLiveModeEnabled) {
      return getStatusMessage.disabled(lastDataUpdateTime)
    }

    if (error && error.hasRetryAfterHeader) {
      return getStatusMessage.interrupted(
        lastDataUpdateTime,
        nextStatusPollingRequestTime,
        error.statusCode
      )
    }

    if (error && !error.hasRetryAfterHeader) {
      return getStatusMessage.uncertain(
        lastDataUpdateTime,
        nextStatusPollingRequestTime
      )
    }

    if (isLastDataUpdateDelayed) {
      return getStatusMessage.delayed(lastDataUpdateTime, receivedTime)
    }

    return getStatusMessage.healthy(lastDataUpdateTime, receivedTime)
  }, [
    lastDataUpdateTime,
    receivedTime,
    nextStatusPollingRequestTime,
    isLiveModeEnabled,
    isLastDataUpdateDelayed,
    error
  ])

  return {
    statusMessage
  }
}
