import { vi } from 'vitest'
import { useLiveModeStatus } from '@/components/Grid/LiveModeStatusBar'
import { getReduxWrapper, renderHookSimply } from '@/core/testing'

describe('useLiveModeStatus', () => {
  beforeEach(() => {
    vi.useFakeTimers()
    vi.setSystemTime(new Date(2023, 7, 18, 9, 55, 0))
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should display a disabled status message when live mode is disabled', () => {
    // given
    const { wrapper } = getReduxWrapper({
      report: {
        isLiveModeEnabled: false,
        lastAsOfUtcMetadata: {
          lastDataUpdateTime: '2023-08-18T07:55:00.000Z'
        }
      }
    })

    const { statusMessage } = renderHookSimply(useLiveModeStatus, wrapper)

    // then
    expect(statusMessage).toBe(
      `Live updates disabled (last data update received at 09:55:00 AM)`
    )
  })

  it('should display a delayed status message when the last data update is over 5 min old', () => {
    // given
    vi.setSystemTime(new Date(2023, 7, 18, 10, 0, 1))

    const { wrapper } = getReduxWrapper({
      report: {
        isLiveModeEnabled: true,
        lastAsOfUtcMetadata: {
          lastDataUpdateTime: '2023-08-18T07:55:00.000Z',
          receivedTime: '2023-08-18T07:55:30.000Z'
        }
      }
    })

    const { statusMessage } = renderHookSimply(useLiveModeStatus, wrapper)

    // then
    expect(statusMessage).toBe(
      'Live updates delayed (last data update received at 09:55:00 AM, last status poll received at 09:55:30 AM)'
    )
  })

  it('should display an interrupted status message when fetching data fails with a `Retry-After` header', () => {
    // given
    const { wrapper } = getReduxWrapper({
      report: {
        isLiveModeEnabled: true,
        lastAsOfUtcMetadata: {
          error: {
            retryAfterSeconds: 45,
            statusCode: 400,
            hasRetryAfterHeader: true
          },
          lastDataUpdateTime: '2023-08-18T07:55:00.000Z'
        }
      }
    })

    const { statusMessage } = renderHookSimply(useLiveModeStatus, wrapper)

    // then
    expect(statusMessage).toBe(
      'Live updates temporarily interrupted (HTTP code 400, last data update received at 09:55:00 AM, next status poll at 09:55:45 AM)'
    )
  })

  it('should display an uncertain status message when fetching data fails without a "Retry-After" header', () => {
    // given
    const { wrapper } = getReduxWrapper({
      report: {
        isLiveModeEnabled: true,
        lastAsOfUtcMetadata: {
          error: {
            retryAfterSeconds: 60,
            statusCode: 400,
            hasRetryAfterHeader: false
          },
          lastDataUpdateTime: '2023-08-18T07:55:00.000Z'
        }
      }
    })

    const { statusMessage } = renderHookSimply(useLiveModeStatus, wrapper)

    // then
    expect(statusMessage).toBe(
      'Live update status uncertain, contact Support if this persists for a prolonged period (last data update received at 09:55:00 AM, next status poll attempt at 09:56:00 AM)'
    )
  })

  it('should display a healthy status message with the last data update time and last status poll time', () => {
    // given
    const { wrapper } = getReduxWrapper({
      report: {
        isLiveModeEnabled: true,
        lastAsOfUtcMetadata: {
          lastDataUpdateTime: '2023-08-18T07:55:00.000Z',
          receivedTime: '2023-08-18T07:55:30.000Z'
        }
      }
    })

    const { statusMessage } = renderHookSimply(useLiveModeStatus, wrapper)

    // then
    expect(statusMessage).toBe(
      'Live updates healthy (last data update received at 09:55:00 AM, last status poll received at 09:55:30 AM)'
    )
  })
})
