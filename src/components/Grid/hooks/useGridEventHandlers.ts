import { env } from '@/core/config/env'
import {
  setIsPivotModeEnabled,
  setPivotResultsColumnsOrdering,
  toggleGroupExpansion
} from '@/core/redux/features/grid'
import { useColumnFilters } from '@/core/redux/features/report/hooks'
import { useAppDispatch, useAppSelect } from '@/core/redux/hooks'
import { serverSideDatasourceFactory } from '@/core/services/grid'
import {
  getGroupId,
  getPivotColumnsOrdering,
  mapAgGridFilterModelsToFilterModel,
  updateFilterSidePanel
} from '@/core/utils/grid'
import { logger } from '@/core/utils/logger'
import {
  Column,
  ColumnMovedEvent,
  ColumnPivotChangedEvent,
  ColumnPivotModeChangedEvent,
  ColumnState,
  FilterChangedEvent,
  GridReadyEvent,
  IFiltersToolPanel,
  RowGroupOpenedEvent,
  RowSelectedEvent
} from '@ag-grid-community/core'
import { useCallback, useRef } from 'react'
import {
  SyncDataEventType,
  GridFilterChangedEvent
} from '../../../core/services/glue/types'
import { useGridApi } from './useGridApi'
import { publishToChannel } from '@/core/services/glue'
import { publishGridRowSelectedThunk } from '@/core/redux/features/context/thunk/publishGridRowSelectedThunk'
import { isSyncDataPublisherSelector } from '@/core/redux/selectors'

export const useGridEventHandlers = () => {
  const dispatch = useAppDispatch()
  const { gridApiRef } = useGridApi()
  const reportName = useAppSelect((state) => state.report.reportName)
  const isPublisher = useAppSelect(isSyncDataPublisherSelector)
  const filtersPanelRef = useRef<IFiltersToolPanel | null>(null)

  const { updateFilters } = useColumnFilters({
    onFiltersUpdated: () =>
      logger.debug(
        'onFilterChangedHandler onFiltersUpdated: No longer calling gridApi?.refreshToolPanel()'
      )
  })

  const onFilterChangedHandler = async (event: FilterChangedEvent) => {
    try {
      const { columns, api } = event
      logger.debug('useFilterChangedEffect listener: Filter changed', {
        columns,
        filterModel: api.getFilterModel()
      })

      const filters = mapAgGridFilterModelsToFilterModel(api.getFilterModel())
      await updateFilters({
        filters,
        currentlyFilteredColumn: columns[0]?.getColId()
      })

      isPublisher &&
        publishToChannel({
          type: SyncDataEventType.GridFilterChanged,
          reportName,
          filters
        } as GridFilterChangedEvent)

      let filteredColumns = filters.map((filterModel) => filterModel.column)
      if (filteredColumns.length == 0) filteredColumns = ['(none)']
      logger.debug(
        `Published drill through GridFilterChanged event for columns ${filteredColumns}`
      )
      updateFilterPlaceholderHandler()
      updateFilterSidePanel(api, filtersPanelRef.current)
    } catch (error) {
      // for some reason, api.getFilterModel() can throw an error in tests
      // so we need to catch it here
      logger.error('useFilterChangedEffect listener: Error', error as any)
    }
  }

  const onPivotModeChangedHandler = ({
    columnApi
  }: ColumnPivotModeChangedEvent) => {
    const isPivotModeEnabled = columnApi.isPivotMode()
    dispatch(setIsPivotModeEnabled(isPivotModeEnabled))

    const getAggFunc = isPivotModeEnabled
      ? // Pivot mode was enabled => Prevent all columns from being made value columns in the pivot, which ag-grid determines by whether the aggFunc is set
        (_: Column<any>) => null
      : // Pivot mode was disabled => Restore defaultAggFunc as aggFunc to prevent NaNs / empty grid cells and the user having to manuall set the aggFunc for all columns
        (col: Column<any>) => col.getColDef().defaultAggFunc

    const state = columnApi.getColumns()?.map(
      (column) =>
        ({
          colId: column.getColId(),
          aggFunc: getAggFunc(column)
        } as ColumnState)
    )

    columnApi.applyColumnState({ state })
  }

  // MACROUIUX-1610 If user sets more than 5 pivot columns, prevent the pivot column from being added
  const onPivotColumnsChanged = ({
    columnApi,
    column
  }: ColumnPivotChangedEvent) => {
    if (!column) {
      logger.warn('Column is undefined in onPivotColumnsChanged')
      return
    }

    const amountOfPivotColumns =
      columnApi.getColumns()?.filter((column) => column.isPivotActive())
        .length ?? 0

    if (amountOfPivotColumns > env.amountOfPivotColumns) {
      columnApi.applyColumnState({
        state: [
          {
            colId: column?.getColId(),
            pivot: false
          }
        ]
      })

      logger.warn(`Cannot select more than 5 pivot ("label") columns.`)
    }
  }

  // At the moment we log only pivot result columns (MACROUIUX-1844)
  const onColumnMoved = ({ columnApi, finished }: ColumnMovedEvent) => {
    if (!finished) {
      return
    }

    const currentOrdering = getPivotColumnsOrdering(columnApi)

    if (currentOrdering) {
      dispatch(setPivotResultsColumnsOrdering(currentOrdering))
    }
  }

  // MACROUIUX-1996: Current version of ag-grid ignores filterPlaceholder so we have to change it ourselves
  const updateFilterPlaceholderHandler = () => {
    const elements = document.getElementsByClassName('date-field-input')
    for (let i = 0; i < elements.length; i++) {
      elements[i].setAttribute('placeholder', 'MMM-DD-YYYY')
    }
  }

  const onGridReadyHandler = useCallback(
    ({ api, columnApi }: GridReadyEvent) => {
      logger.debug('Grid ready')

      // ag-grid event listeners are cleaned up by ag-grid itself (when the grid is destroyed)
      api.addEventListener('rowGroupOpened', onRowGroupOpenedHandler)
      api.addEventListener('filterChanged', onFilterChangedHandler)
      api.addEventListener('columnPivotModeChanged', onPivotModeChangedHandler)
      api.addEventListener('columnPivotChanged', onPivotColumnsChanged)
      api.addEventListener('columnMoved', onColumnMoved)
      api.addEventListener('filterOpened', updateFilterPlaceholderHandler)

      const datasource = serverSideDatasourceFactory(dispatch)
      api.setServerSideDatasource(datasource)

      if (gridApiRef.current) {
        gridApiRef.current.api = api
        gridApiRef.current.columnApi = columnApi
      }
    },
    []
  )

  function onRowGroupOpenedHandler(event: RowGroupOpenedEvent) {
    const { node } = event

    dispatch(
      toggleGroupExpansion({
        groupId: getGroupId(node.getRoute()),
        isExpanded: node.expanded
      })
    )
  }

  function onRowSelectedHandler() {
    return (event: RowSelectedEvent) => {
      // The RowSelectedEvent is called for rows that are selected and lose their selection
      // Checking the node's isSelected state is the recommended way of figuring out which kind of event this is
      if (event.node.isSelected()) {
        dispatch(publishGridRowSelectedThunk(event))
      }
    }
  }

  return {
    onGridReadyHandler,
    onRowSelectedHandler
  }
}
