import { vi } from 'vitest'
import {
  renderWithReduxInitialState,
  setupMockServer,
  allHandlers
} from '@/core/testing'
import { act, fireEvent, waitFor } from '@testing-library/react'
import { AgGridReact } from '@ag-grid-community/react'
import { useStore } from 'react-redux'
import { RootState } from '@/core/redux/store'
import { useGridApi, useGridEventHandlers, useGridOptions } from '..'
import { rowData, columnDefs } from '@/core/testing/mocks'
import { publishToChannel } from '@/core/services/glue'
import { SyncDataEventType } from '@/core/services/glue/types'
import { GridApi } from '@ag-grid-community/core'

vi.mock('@/core/services/glue', () => {
  return {
    publishToChannel: vi.fn(),
    getGlue: vi.fn()
  }
})
vi.mock('@/core/services/glue/utils/drillThroughContext', () => {
  return {
    buildDrillThroughContext: () => ({})
  }
})

describe('useGridEventHandlers', () => {
  const gridReadySpy = vi.fn()
  setupMockServer(...allHandlers)

  beforeEach(() => {
    vi.resetAllMocks()
  })

  test('GridFilterChanged event is published', async () => {
    // Given
    let testApi: GridApi<any>
    renderWithReduxInitialState(
      <TestGrid
        onGridReady={(event) => {
          const { api } = event
          gridReadySpy()
          testApi = api
        }}
      />,
      {
        report: {
          reportName: 'testReport',
          publishSyncData: true
        } as any
      }
    )

    // When
    await waitFor(() => {
      expect(gridReadySpy).toHaveBeenCalled()
    })

    await act(async () => {
      // Directly set the filter model to trigger the onFilterChanged event
      testApi.setFilterModel({
        make: { filterType: 'text', type: 'contains', filter: 'test' }
      })
    })

    // Then
    await waitFor(() => {
      expect(publishToChannel).toHaveBeenCalled()
    })

    expect(publishToChannel).toHaveBeenCalledWith({
      type: SyncDataEventType.GridFilterChanged,
      reportName: 'testReport',
      filters: [
        {
          column: 'make',
          filters: [
            {
              operator: 'contains',
              value: 'test'
            }
          ],
          logicalOperator: 'Or'
        }
      ]
    })
  })

  test('GridFilterChanged event is not published if not a sync data publisher', async () => {
    // Given
    let testApi: GridApi<any>
    renderWithReduxInitialState(
      <TestGrid
        onGridReady={(event) => {
          const { api } = event
          gridReadySpy()
          testApi = api
        }}
      />,
      {
        report: {
          reportName: 'testReport',
          publishSyncData: false
        } as any
      }
    )

    // When
    await waitFor(() => {
      expect(gridReadySpy).toHaveBeenCalled()
    })

    await act(async () => {
      // Directly set the filter model to trigger the onFilterChanged event
      testApi.setFilterModel({
        make: { filterType: 'text', type: 'contains', filter: 'test' }
      })
    })

    // Add a small delay to ensure any async operations complete
    await new Promise((resolve) => setTimeout(resolve, 0))

    // Then
    await waitFor(() => {
      expect(publishToChannel).not.toHaveBeenCalled()
      expect(publishToChannel).toHaveBeenCalledTimes(0)
    })
  })

  test('GridRowSelected event is published', async () => {
    // given
    let testApi: GridApi<any>
    const selectSpy = vi.fn()
    renderWithReduxInitialState(
      <TestGrid
        spy={selectSpy}
        onGridReady={(event) => {
          const { api } = event
          gridReadySpy()
          testApi = api
        }}
      />,
      {
        report: {
          reportName: 'testReport',
          publishSyncData: false
        } as any
      }
    )

    // when
    await waitFor(() => {
      expect(gridReadySpy).toHaveBeenCalled()
    })

    await act(async () => {
      testApi.forEachNode((node) => {
        node.setSelected(true)
      })
    })

    // then
    await waitFor(() => {
      expect(publishToChannel).toHaveBeenCalled()
    })
  })
})

const TestGrid = ({ onGridReady, spy }: any) => {
  const { gridApiRef } = useGridApi()
  const { getState } = useStore<RootState>()
  const { gridOptions } = useGridOptions(getState)
  const { onGridReadyHandler } = useGridEventHandlers()

  return (
    <div style={{ height: 500 }}>
      <AgGridReact
        ref={gridApiRef}
        gridOptions={{
          suppressMenuHide: true,
          getMainMenuItems: gridOptions.getMainMenuItems,
          rowData,
          columnDefs,
          rowModelType: 'clientSide',
          onRowSelected: gridOptions.onRowSelected
          // onRowSelected: spy
        }}
        columnDefs={columnDefs}
        onGridReady={(event) => {
          onGridReadyHandler(event)
          onGridReady(event)
        }}
        // onFilterChanged={onFilterChangedSpy}
      />
    </div>
  )
}
