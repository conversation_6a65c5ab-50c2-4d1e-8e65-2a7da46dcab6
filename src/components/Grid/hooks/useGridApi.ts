import { useContext } from 'react'
import { GridContext } from '@/components/Grid/context/GridProvider'

export const useGridApi = () => {
  // gridApi and columnApi are not serializable, so we can't store them in redux
  const { gridApiRef } = useContext(GridContext)
  const gridApi = gridApiRef.current?.api
  const columnApi = gridApiRef.current?.columnApi

  return {
    gridApiRef,
    gridApi,
    columnApi
  }
}
