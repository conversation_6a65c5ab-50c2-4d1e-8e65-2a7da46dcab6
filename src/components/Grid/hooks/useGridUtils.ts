import { useGridApi } from '@/components/Grid/hooks'
import { useAppSelect } from '@/core/redux/hooks'
import { IColumnInfo } from '@/core/services/riskReport'
import {
  getGroupId,
  getPivotColumnsOrdering as getPivotColumnsOrderingFn
} from '@/core/utils/grid'
import { ColDef, ColGroupDef, Column } from '@ag-grid-community/core'

interface ChangeHeaderNamePayload {
  targetColumnId: string
  newHeaderName: string
}

export const useGridUtils = () => {
  const { gridApi, columnApi } = useGridApi()
  const columnInfo = useAppSelect((state) => state.report.columnInfo)

  // TODO: Handle optionality.
  if (!gridApi) {
    throw 'Grid API not set yet.'
  }

  const changeHeaderName = (payload: ChangeHeaderNamePayload) => {
    const { targetColumnId, newHeaderName } = payload

    const targetColumn = gridApi.getColumnDef(targetColumnId)

    if (targetColumn) {
      targetColumn.headerName = newHeaderName
      gridApi.refreshHeader()
      gridApi.refreshToolPanel()
      gridApi.dispatchEvent(new Event('displayedColumnsChanged')) // Triggers the preset save icon to appear
    }
  }

  const restoreOriginalHeaderName = (targetColumnId: string) => {
    const columnDefs = gridApi.getColumnDefs()
    const essentialColumn = getEssentialColumn(targetColumnId)

    if (!columnDefs || !essentialColumn) {
      return
    }

    const targetColumn = gridApi.getColumnDef(targetColumnId)

    if (targetColumn) {
      targetColumn.headerName = essentialColumn.name
      gridApi.refreshHeader()
      gridApi.refreshToolPanel()
      gridApi.dispatchEvent(new Event('displayedColumnsChanged')) // Triggers the preset save icon to appear
    }
  }

  const getEssentialColumn = (columnId: string): IColumnInfo | undefined => {
    const targetColumn = gridApi.getColumnDef(columnId)

    return columnInfo.find((column) => column.column === targetColumn?.field)
  }

  const isColumnRenamed = (columnId: string): boolean => {
    const targetColumn = gridApi.getColumnDef(columnId)
    const essentialColumn = getEssentialColumn(columnId)

    return Boolean(
      targetColumn &&
        essentialColumn &&
        targetColumn.headerName !== essentialColumn.column
    )
  }

  const getHeaderName = (columnId: string): string => {
    const column = gridApi.getColumnDef(columnId)
    return column?.headerName || ''
  }

  const getExpandedGroupsIds = (): string[] => {
    const expandedGroupsIds: string[] = []

    gridApi.forEachNode((row) => {
      if (row.expanded) {
        expandedGroupsIds.push(getGroupId(row.getRoute()))
      }
    })

    return expandedGroupsIds
  }

  const expandGroups = (groupsIds: string[]) => {
    gridApi.forEachNode((row) => {
      const groupId = getGroupId(row.getRoute())
      if (groupsIds.includes(groupId)) {
        row.setExpanded(true)
      }
    })
  }

  const getColIds = (columns: Column[]): string[] => {
    return columns.map((col) => col.getColId())
  }

  const getPivotColumnsIds = (): string[] =>
    getColIds(columnApi?.getPivotColumns() || [])

  const getValueColumnsIds = (): string[] =>
    getColIds(columnApi?.getValueColumns() || [])

  const getPivotResultsColumns = (): (ColDef | ColGroupDef)[] => {
    return (
      columnApi
        ?.getPivotResultColumns()
        // because pivot results are virtual columns, we have to sort by its actual position against the left
        ?.sort((a, b) => (a.getLeft() ?? 0) - (b.getLeft() ?? 0))
        ?.map((col) => ({
          ...col.getColDef(),
          ...col.getUserProvidedColDef()
        })) || []
    )
  }

  /**
   * not reactive, use only for initial setup
   */
  const isPivotMode = () => columnApi?.isPivotMode()

  const getPivotColumnsOrdering = () => getPivotColumnsOrderingFn(columnApi)

  return {
    changeHeaderName,
    restoreOriginalHeaderName,
    getEssentialColumn,
    isColumnRenamed,
    getHeaderName,
    getExpandedGroupsIds,
    expandGroups,
    getPivotColumns: getPivotColumnsIds,
    getValueColumns: getValueColumnsIds,
    isPivotMode,
    getPivotResultsColumns,
    getPivotColumnsOrdering
  }
}
