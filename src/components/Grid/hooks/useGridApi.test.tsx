import {
  columnApiMock,
  combineWrappers,
  getInitializeAppWrapper,
  gridApiMock,
  gridApiRefMock,
  handlers,
  setupMockServer,
  waitForCurrent,
  wrapWithUrl
} from '@/core/testing'
import { renderHook } from '@testing-library/react'
import { vi } from 'vitest'
import { useGridApi } from '.'

import { GridProvider } from '@/components/Grid/context/GridProvider'
describe('useGridApi', () => {
  const url = '/reports/pnl-live'
  setupMockServer(
    handlers.latestAsOf,
    handlers.getSummary,
    handlers.getUserPreference,
    handlers.getReportDetails,
    handlers.getMetadata,
    handlers.getDistinctColumnValues,
    handlers.getUserProfile,
    handlers.getAllPresets,
    handlers.getUsersByRoles,
    handlers.getClientDetails,
    handlers.connectAggregatorWebSocket
  )
  beforeEach(() => {
    vi.clearAllMocks()
  })

  const gridContextWrapper =
    (gridApiRef: any) =>
    ({ children }: any) => {
      return <GridProvider gridApiRef={gridApiRef}>{children}</GridProvider>
    }

  it('should retun gridApi from context', async () => {
    // Given
    const { result } = renderHook(useGridApi, {
      wrapper: combineWrappers(
        wrapWithUrl(url),
        gridContextWrapper(gridApiRefMock)
      )
    })

    // When
    await waitForCurrent(result)

    // Then
    expect(result.current.gridApi).toEqual(gridApiRefMock.current.api)
  })

  it('should retun columnApi from context', async () => {
    // Given
    const { result } = renderHook(useGridApi, {
      wrapper: combineWrappers(
        wrapWithUrl(url),
        gridContextWrapper(gridApiRefMock)
      )
    })

    // When
    await waitForCurrent(result)

    // Then
    expect(result.current.columnApi).toEqual(columnApiMock)
  })
})
