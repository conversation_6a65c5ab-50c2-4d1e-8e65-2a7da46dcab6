import { useEffect, useState } from 'react'
import { logger } from '@/core/utils/logger'
import {
  getCurrentChannel,
  getGlue,
  publishToChannel,
  subscribeToChannel
} from '@/core/services/glue'
import { useAppDispatch, useAppSelect } from '@/core/redux/hooks'
import { handlePublisherEvents } from '@/core/services/glue/utils/handlePublisherEvents'
import { handleSubscriberEvents } from '@/core/services/glue/utils/handleSubscriberEvents'
import { SyncDataEventType } from '@/core/services/glue/types'
import { isSyncDataPublisherSelector } from '@/core/redux/selectors'
import {
  setConnectedToGlueChannel,
  setIsConnectedSubscriber
} from '@/core/redux/features/context/contextSlice'

// Due to complexity of mocking glue channels this part of the code should be tested
// in e2e tests.
export const useGlueChannelListenerEffect = () => {
  const glue = getGlue()
  const [currentChannel, setCurrentChannel] = useState<string | undefined>()
  const dispatch = useAppDispatch()
  const isSyncDataPublisher = useAppSelect(isSyncDataPublisherSelector)

  useEffect(() => {
    if (!glue) {
      return
    }

    const init = async () => {
      const currentChannel = getCurrentChannel()
      setCurrentChannel(currentChannel)

      // Subscribe to channel changes
      const unsubscribeFromChannelChange = glue.channels.onChanged(
        (channel) => {
          setCurrentChannel(channel)
        }
      )

      const glueEventHandler = isSyncDataPublisher
        ? handlePublisherEvents(dispatch)
        : handleSubscriberEvents(dispatch)

      const unsubscribe = await subscribeToChannel(glueEventHandler)
      logger.debug(
        `useGlueChannelListenerEffect: subscribed to glue channels as ${
          isSyncDataPublisher ? 'publisher' : 'subscriber'
        }`
      )

      // If currentChannel changes - we publish event to propagate initial state
      !isSyncDataPublisher &&
        publishToChannel({
          type: SyncDataEventType.ChannelListenerConnected
        })

      dispatch(setConnectedToGlueChannel(Boolean(currentChannel)))

      return () => {
        dispatch(setIsConnectedSubscriber(false))
        dispatch(setConnectedToGlueChannel(false))
        unsubscribeFromChannelChange()
        unsubscribe()
        logger.debug(
          'useGlueChannelListenerEffect: unsubscribed from glue channels'
        )
      }
    }

    const cleanup = init()

    return () => {
      cleanup.then((cleanupFn) => cleanupFn())
    }
  }, [glue, currentChannel])
}
