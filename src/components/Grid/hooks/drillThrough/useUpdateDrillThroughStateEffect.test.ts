import {
  setSelectedTradingAreas,
  startLatestAsOfUtcListener
} from '@/core/redux/features/report'
import { combineWrappers, getReduxWrapper } from '@/core/testing'
import {
  SyncDataEventType,
  LiveModeChangedEvent,
  SelectedTradingAreasChangedEvent
} from '@/core/services/glue/types'
import { Glue42 } from '@glue42/desktop'
import { renderHook, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { useUpdateDrillThroughStateEffect } from './useUpdateDrillThroughStateEffect'
import { publishToChannel } from '@/core/services/glue'

describe('useUpdateDrillThroughState', () => {
  const initialStoreState = {
    report: {
      isLiveModeEnabled: false,
      selectedTradingAreas: [],
      currentAsOfUtc: new Date().toISOString(),
      reportName: 'test',
      isInitialized: true,
      lastAsOfUtcMetadata: {},
      publishSyncData: true
    }
  }

  const publishMock = vi.fn()

  window.glue = {
    channels: {
      publish: publishMock,
      my: () => 'some-channel-name'
    }
  } as unknown as Glue42.Glue

  beforeEach(() => {
    vi.resetAllMocks()
    publishMock.mockReset()
  })

  const { wrapper: reduxWrapper, store } = getReduxWrapper(initialStoreState)
  const wrapper = combineWrappers(reduxWrapper)

  it('should update glue channel when live mode is changed', async () => {
    renderHook(() => useUpdateDrillThroughStateEffect(), { wrapper })

    store.dispatch(startLatestAsOfUtcListener())

    await waitFor(() => {
      expect(publishMock).toHaveBeenCalledWith({
        type: SyncDataEventType.LiveModeChanged,
        isLiveModeEnabled: true,
        reportName: 'test'
      } as LiveModeChangedEvent)
    })
  })

  it('should update glue channel when trading areas are changed', async () => {
    renderHook(() => useUpdateDrillThroughStateEffect(), { wrapper })

    store.dispatch(setSelectedTradingAreas(['1', '2', '3']))

    await waitFor(() => {
      expect(publishMock).toHaveBeenCalledWith({
        type: SyncDataEventType.SelectedTradingAreasChanged,
        selectedTradingAreas: ['1', '2', '3'],
        reportName: 'test'
      } as SelectedTradingAreasChangedEvent)
    })
  })
})
