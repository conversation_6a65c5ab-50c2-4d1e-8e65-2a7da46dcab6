import { useAppDispatch, useAppSelect } from '@/core/redux/hooks'
import { useEffect } from 'react'
import {
  isLiveModeEnabledSelector,
  selectedTradingAreasSelector
} from '@/core/redux/selectors'
import { publishSelectedTradingAreasThunk } from '@/core/redux/features/context/thunk/publishSelectedTradingAreasThunk'
import { publishLiveModeChangedThunk } from '@/core/redux/features/context/thunk/publishLiveModeChangedThunk'

export const useUpdateDrillThroughStateEffect = () => {
  const selectedTradingAreas = useAppSelect(selectedTradingAreasSelector)
  const isLiveModeEnabled = useAppSelect(isLiveModeEnabledSelector)
  const dispatch = useAppDispatch()

  useEffect(() => {
    dispatch(publishLiveModeChangedThunk())
  }, [isLiveModeEnabled])

  useEffect(() => {
    dispatch(publishSelectedTradingAreasThunk())
  }, [selectedTradingAreas])
}
