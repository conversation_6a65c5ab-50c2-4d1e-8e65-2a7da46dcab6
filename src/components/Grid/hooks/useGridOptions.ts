import { LiveModeStatusBar } from '@/components/Grid/LiveModeStatusBar'
import { useIsServerSideGroupOpenByDefault } from '@/components/Grid/hooks'
import { Tooltip } from '@/components/Tooltip'
import { appConfig } from '@/core/config'
import {
  useAppDispatch,
  useAppSelect,
  useColumnToRename,
  useColumnsReset,
  useNotifications
} from '@/core/redux/hooks'
import { ColumnType } from '@/core/services/riskReport'
import { getRowId as getId } from '@/core/utils/grid'
import {
  GetContextMenuItemsParams,
  GetRowIdFunc,
  GridOptions,
  ProcessCellForExportParams
} from '@ag-grid-community/core'
import { Config, getAgGridOptions } from '@core-clib/ag-grid'
import { format, parseISO } from 'date-fns'
import { useMemo } from 'react'
import { ComponentVisibilitySidePanel } from '../Grid/ComponentVisibilitySidePanel'
import { getContextMenuItems } from '@/core/services/glue/utils/drillThroughContext'
import { useGridEventHandlers } from './useGridEventHandlers'
import { componentVisibilitySelector } from '@/core/redux/selectors'
import { setPresetsReady } from '@/core/redux/features/presets'
import { resetColumnOrderThunk } from '@/core/redux/features/report/thunk/resetColumnOrderThunk'
import { RootState } from '@/core/redux/store'

const onRowDoubleClicked: GridOptions['onRowDoubleClicked'] = (event) => {
  const { node } = event

  if (node.rowGroupColumn && node.isExpandable()) {
    node.setExpanded(!node.expanded)
  }
}

const onCellClicked: GridOptions['onCellClicked'] = (event) => {
  const { node, colDef } = event

  const isActionable =
    colDef.showRowGroup && node.rowGroupColumn && node.isExpandable()

  if (isActionable) {
    node.setExpanded(!node.expanded)
  }
}

export const exportCellFormattingCallback = (
  params: ProcessCellForExportParams
): string => {
  if (
    params.value &&
    params.column.getColDef()?.refData?.type === ColumnType.Date
  ) {
    return format(parseISO(params.value), appConfig.csvExportDateFormat)
  }
  return params.value
}

export const useGridOptions = (
  getState: () => RootState
): { gridOptions: GridOptions } => {
  const keyColumnNames = useAppSelect((state) => state.report.keyColumnNames)
  const { addDangerNotification } = useNotifications()
  const { setColumnToRename } = useColumnToRename()
  const { setResetColumns } = useColumnsReset()
  const componentVisibilityState = useAppSelect(componentVisibilitySelector)
  const { isServerSideGroupOpenByDefault } = useIsServerSideGroupOpenByDefault()
  const { onRowSelectedHandler } = useGridEventHandlers()
  const dispatch = useAppDispatch()

  const getRowId: GetRowIdFunc = (params) => {
    return getId(params, keyColumnNames, addDangerNotification)
  }

  const defaultColDef = useMemo(() => {
    return {
      minWidth: 10
    }
  }, [])

  // Since SDK 1.98.8 the sdk configuration for AgGrid should be provided as Config instance.
  const customConfig = new Config({
    // This is sdk specific property. If you delete this, we will see 2 context menus.
    showClibContextMenu: false
  })

  const gridOptions = getAgGridOptions({
    baseGridOptions: {
      skipHeaderOnAutoSize: false,
      defaultColDef,
      rowModelType: 'serverSide',
      rowGroupPanelShow:
        componentVisibilityState === undefined ||
        componentVisibilityState.isGroupingBarVisible
          ? 'always'
          : 'never',
      autoGroupColumnDef: {
        filter: 'agGroupColumnFilter',
        pinned: 'left',
        lockPosition: true,
        tooltipComponent: Tooltip
      },
      allowDragFromColumnsToolPanel: true,
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressValues: !getState().report.supportsPivotMode,
              suppressPivots: !getState().report.supportsPivotMode,
              suppressPivotMode: !getState().report.supportsPivotMode
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel',
            toolPanelParams: {
              // Keeps column order in Filters Tool Panel preserved when column order in grid changes
              // Turns out keeping this in sync can slow down the application considerably:
              // Only change this setting with extensive performance and memory testing!!
              suppressSyncLayoutWithGrid: true
            }
          },
          {
            id: 'componentVisibilitySidePanel',
            labelDefault: 'Visibility',
            labelKey: 'componentVisibilitySidePanel',
            iconKey: 'menu',
            toolPanel: ComponentVisibilitySidePanel,
            toolPanelParams: {
              title: 'Visibility Tool Panel'
            }
          }
        ]
      },
      statusBar: {
        statusPanels: [
          {
            statusPanel: 'agAggregationComponent',
            statusPanelParams: {
              aggFuncs: ['sum', 'avg', 'count']
            },
            align: 'right'
          },
          {
            statusPanel: LiveModeStatusBar,
            align: 'left',
            key: 'liveModeStatusBar'
          }
        ]
      },
      pagination: true,
      paginationPageSize: 1000,
      getRowId,
      processCellForClipboard: (params: ProcessCellForExportParams) =>
        exportCellFormattingCallback(params),
      cacheQuickFilter: true,
      // `suppressAnimationFrame` set to `false` enables `requestAnimationFrame` which makes animations more performant
      suppressAnimationFrame: false,
      // Remove unnecessary animations, as they potentially consume too many resources.
      suppressColumnMoveAnimation: true,
      animateRows: false,
      suppressAggFuncInHeader: true,
      localeText: {
        loadingOoo: '' // setting this to an empty string to disable the "loading" text
      },
      enableRangeSelection: true,
      // SDK uses getRowHeight on their side. Disable it to use maxBlocksInCache as both settings can't be used together.
      getRowHeight: undefined,
      // With some subsequent 'getSubset' request for the same row group, the number of requests per group can increase by 1
      // (if there are more rows to be loaded in the group).
      // The limit is set so that the ag grid fetches a maximum of 10 blocks per group for storing in the cache.
      maxBlocksInCache: 10,
      rowSelection: 'single',
      // Setting it to `false` enables custom context menu.
      suppressContextMenu: false,
      isServerSideGroupOpenByDefault,
      getMainMenuItems: (params: any) => [
        ...params.defaultItems.filter(
          (eachItem: string) => eachItem !== 'resetColumns'
        ),
        {
          name: 'Reset Grid Layout',
          action: () => setResetColumns(true)
        },
        {
          name: 'Reset Column Order',
          action: () => dispatch(resetColumnOrderThunk(params.columnApi))
        },
        {
          name: 'Rename Column',
          action: () => setColumnToRename(params.column.getId())
        },
        {
          name: 'Remove Column',
          action: () => {
            if (params.column) {
              params.columnApi.setColumnVisible(params.column, false)
            }
          }
        },
        {
          name: 'Move to left',
          action: () => {
            if (params.column) {
              params.columnApi.moveColumn(
                params.column,
                params.columnApi.getRowGroupColumns().length > 0 ? 1 : 0
              )
            }
          }
        },
        {
          name: 'Remove All Filters',
          action: () => {
            params.api.setFilterModel(null)
            params.api.onFilterChanged()
          }
        }
      ],
      getContextMenuItems: (params: GetContextMenuItemsParams) =>
        getContextMenuItems(params, getState, dispatch),
      onRowDoubleClicked,
      onCellClicked,
      defaultCsvExportParams: {
        processCellCallback: exportCellFormattingCallback
      },
      enableCellChangeFlash: true,

      /*
       * On Row group Opened is needed to resize the group Column on Groups Collapse time
       * as "useAfterRefreshEffect" event will not get triggered on Collapse time as there is no fresh data change.
       */
      onRowGroupOpened: (event) => {
        event.columnApi.autoSizeColumn('ag-Grid-AutoColumn')
      },

      // To prevent unexpected changes from SDK regard to custom components and style changes.
      components: undefined,
      // reactiveCustomComponents: true,
      onRowSelected: onRowSelectedHandler(),
      onFirstDataRendered: () => {
        dispatch(setPresetsReady(true))
      }
    },
    config: customConfig
  })

  return { gridOptions }
}
