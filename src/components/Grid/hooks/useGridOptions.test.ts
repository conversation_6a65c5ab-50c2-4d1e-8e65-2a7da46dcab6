import { ProcessCellForExportParams } from '@ag-grid-community/core'
import { ColumnType } from '@/core/services/riskReport'
import { exportCellFormattingCallback } from '@/components/Grid/hooks'

describe('useGridOptions', () => {
  describe('exportCellFormattingCallback ', () => {
    const getProcessCellForExportParamsMock = (
      value: string,
      columnType: ColumnType
    ): ProcessCellForExportParams => {
      return {
        value,
        column: {
          getColDef: () => ({
            refData: { type: columnType as ColumnType } // Form type through refData of grid columndefs prperty
          })
        }
      } as any as ProcessCellForExportParams
    }

    it('should return formatted date when the value exists and a column is of type ColumnType.Date', () => {
      // given
      const params = getProcessCellForExportParamsMock(
        '2021-11-01T00:00:00.000Z',
        ColumnType.Date
      )

      // when
      const result = exportCellFormattingCallback(params)

      // then
      expect(result).toEqual('2021-11-01')
    })

    it('should return value as is when the value exists and a column is of type different than ColumnType.Date', () => {
      // given
      const params = getProcessCellForExportParamsMock(
        '2021-11-01T00:00:00.000Z',
        ColumnType.DateTime
      )

      // when
      const result = exportCellFormattingCallback(params)

      // then
      expect(result).toEqual('2021-11-01T00:00:00.000Z')
    })

    it('should return value as is when the value does not exist and a column is of type ColumnType.Date', () => {
      // given
      const params = getProcessCellForExportParamsMock('', ColumnType.Date)

      // when
      const result = exportCellFormattingCallback(params)

      // then
      expect(result).toBe('')
    })

    it('should return value as is when the value does not exist and a column is of type different than ColumnType.Date', () => {
      // given
      const params = getProcessCellForExportParamsMock('', ColumnType.String)

      // when
      const result = exportCellFormattingCallback(params)

      // then
      expect(result).toBe('')
    })
  })
})
