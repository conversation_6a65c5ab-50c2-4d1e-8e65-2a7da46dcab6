import { useAppStore } from '@/core/redux/hooks/useAppStore'
import { getGroupId, isRowGroupOpenByDefault } from '@/core/utils/grid'
import { logger } from '@/core/utils/logger'
import { IsServerSideGroupOpenByDefaultParams } from '@ag-grid-community/core'
import { useCallback } from 'react'

/**
 * `useIsServerSideGroupOpenByDefault` is a custom hook that returns a function
 * to determine if a row group should be open by default in ag-Grid based on
 * the current `expandedGroups` state.
 */
export const useIsServerSideGroupOpenByDefault = () => {
  const store = useAppStore()

  const isServerSideGroupOpenByDefault = useCallback(
    (params: IsServerSideGroupOpenByDefaultParams) => {
      //  We have to get the expandedGroupsIds value directly from the store to create a closure.
      //  Otherwise, `isServerSideGroupOpenByDefault` will retain the initial value throughout the lifecycle of the app.
      const state = store.getState()
      const expandedGroupsIds = state.grid.expandedGroupsIds

      const groupRoute = params.rowNode.getRoute()
      const groupIsExpanded = isRowGroupOpenByDefault(
        getGroupId(groupRoute),
        expandedGroupsIds
      )

      if (groupIsExpanded) {
        logger.debug(`ServerSideGroupOpenByDefault Route: ${groupRoute}`)
      }
      return groupIsExpanded
    },
    [store]
  )

  return { isServerSideGroupOpenByDefault }
}
