import { appConfig } from '@/core/config'
import { riskReportSummaryThunk } from '@/core/redux/features/report/thunk/riskReportSummaryThunk'
import { useAppDispatch, useAppSelect } from '@/core/redux/hooks'
import { debounce } from 'lodash'
import { useEffect } from 'react'

export const useRefreshRiskReportSummaryEffect = () => {
  const dispatch = useAppDispatch()
  const selectedTradingAreas = useAppSelect(
    (state) => state.report.selectedTradingAreas
  )
  const currentAsOfUtc = useAppSelect((state) => state.report.currentAsOfUtc)
  const preProcessingParams = useAppSelect(
    (state) => state.report.preProcessingParams
  )

  // TODO MACROUIUX-1663: Remove debounce - make sure this effect is only called once when any of the dependencies change
  const debounceRefreshRiskMetrics = debounce(
    () =>
      dispatch(
        riskReportSummaryThunk({
          selectedTradingAreas: selectedTradingAreas || [],
          preProcessingParams
        })
      ),
    appConfig.defaultDebounceTimeMs
  )

  useEffect(() => {
    debounceRefreshRiskMetrics()
  }, [selectedTradingAreas, currentAsOfUtc, preProcessingParams])
}
