import { renderHook } from '@testing-library/react'
import { useIsServerSideGroupOpenByDefault } from './useIsServerSideGroupOpenByDefault'
import { ReduxWrapper } from '@/core/testing'

describe('useIsServerSideGroupOpenByDefault', () => {
  it('should be defined', () => {
    expect(useIsServerSideGroupOpenByDefault).toBeDefined()
  })

  it('should return true if group is expanded', () => {
    const expandedGroupsStub = ['test']
    const { result } = renderHook(() => useIsServerSideGroupOpenByDefault(), {
      wrapper: ReduxWrapper({
        grid: {
          expandedGroupsIds: expandedGroupsStub
        }
      })
    })

    const { isServerSideGroupOpenByDefault } = result.current

    const params = {
      rowNode: {
        getRoute: () => ['test'],
        expanded: true
      }
    }

    expect(isServerSideGroupOpenByDefault(params as any)).toBe(true)
  })

  it('should return false if group is expanded', () => {
    const expandedGroupsStub = ['test']
    const { result } = renderHook(() => useIsServerSideGroupOpenByDefault(), {
      wrapper: ReduxWrapper({
        grid: {
          expandedGroupsIds: expandedGroupsStub
        }
      })
    })

    const { isServerSideGroupOpenByDefault } = result.current

    const params = {
      rowNode: {
        getRoute: () => ['abc'],
        expanded: true
      }
    }

    expect(isServerSideGroupOpenByDefault(params as any)).toBe(false)
  })
})
