import {
  setPreProcessingParams,
  setSelectedTradingAreas
} from '@/core/redux/features/report'
import * as riskReportSummaryThunkMock from '@/core/redux/features/report/thunk/riskReportSummaryThunk'
import { getReduxWrapper } from '@/core/testing'
import { renderHook } from '@testing-library/react'
import { act } from 'react-dom/test-utils'
import { vi } from 'vitest'
import { useRefreshRiskReportSummaryEffect } from './useRefreshRiskReportSummary'

describe('useRefreshRiskMetrics', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
  })

  it('should refresh risk metrics when selectedTradingAreas, currentAsOfUtc, or preProcessingParams change', async () => {
    const riskReportSummaryThunkSpy = vi.spyOn(
      riskReportSummaryThunkMock,
      'riskReportSummaryThunk'
    )

    const { store, wrapper } = getReduxWrapper({
      report: {
        selectedTradingAreas: ['1', '2'],
        currentAsOfUtc: '2023-11-15T10:48:22.2649764Z',
        reportName: 'reportName',
        preProcessingParams: {},
        lastAsOfUtcMetadata: {}
      }
    })

    renderHook(useRefreshRiskReportSummaryEffect, { wrapper })
    act(() => store.dispatch(setSelectedTradingAreas([])))
    vi.advanceTimersToNextTimer()

    // Start with two because 1st time the thunk is called in the store initialization
    expect(riskReportSummaryThunkSpy).toHaveBeenCalledTimes(2)

    act(() => store.dispatch(setPreProcessingParams({})))
    vi.advanceTimersToNextTimer()
    expect(riskReportSummaryThunkSpy).toHaveBeenCalledTimes(3)
  })
})
