import { useAppSelect } from '@/core/redux/hooks'
import { columnInfo, columnsStatusSelector } from '@/core/redux/selectors'
import { logger } from '@/core/utils/logger'
import { ColDef } from '@ag-grid-community/core'
import { useEffect } from 'react'
import { useGridApi } from '../hooks'
import { getColumnTooltipContent } from '@/core/utils/grid'

export const useColumnsStatusEffect = () => {
  const { gridApi, columnApi } = useGridApi()
  const columnsStatus = useAppSelect(columnsStatusSelector)
  const columnsInfo = useAppSelect(columnInfo)

  useEffect(() => {
    if (!gridApi) {
      return
    }

    gridApi.getColumnDefs()?.forEach((colDef: ColDef) => {
      const colId = colDef.colId || colDef.field

      if (colId && !colDef.hide) {
        const colStatus = columnsStatus?.[colId]

        if (colStatus) {
          const statusClass = {
            Error: 'column-status-error',
            Warning: 'column-status-warn',
            Ok: undefined
          }[colStatus.status]

          columnApi?.getColumn(colId)?.setColDef(
            {
              ...colDef,
              headerClass: statusClass,
              headerTooltip: colStatus.message
            },
            null
          )
        } else {
          const priorColInfo = columnsInfo.find(
            (colInfo) => colInfo.column === colId
          )

          if (!priorColInfo) {
            logger.warn(
              `Column with id ${colId} was not found in the columnInfo.`
            )
          } else {
            columnApi?.getColumn(colId)?.setColDef(
              {
                ...colDef,
                headerClass: undefined,
                headerTooltip: getColumnTooltipContent(colDef, priorColInfo)
              },
              null
            )
          }
        }
      }
    })
  }, [columnsStatus])
}
