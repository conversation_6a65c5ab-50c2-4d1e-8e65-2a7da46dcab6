import { appConfig } from '@/core/config'
import { useAppSelect } from '@/core/redux/hooks'
import { refreshGridRows, refreshInconsistentRows } from '@/core/services/grid'
import { logger } from '@/core/utils/logger'
import { debounce } from 'lodash'
import { useCallback, useEffect, useState } from 'react'
import { useGridApi } from '../hooks'
import { currentKnowledgeAsOfSelector } from '@/core/redux/features/report/reportMetadata/selectors'

export const useRefreshGridRowsEffect = () => {
  const currentAsOfUtc = useAppSelect((state) => state.report.currentAsOfUtc)
  const currentKnowledgeAsOf = useAppSelect(currentKnowledgeAsOfSelector)

  const selectedTradingAreas = useAppSelect(
    (state) => state.report.selectedTradingAreas
  )

  const preProcessingParams = useAppSelect(
    (state) => state.report.preProcessingParams
  )

  const { gridApi } = useGridApi()

  // Debouncing is used to prevent race conditions that could occur when selected trading areas are rapidly changed by the user.
  // Without debouncing, each change triggers an immediate `refreshGridRows` call, which could lead to inconsistent grid state due to overlapping asynchronous operations.
  // By debouncing, we ensure that `refreshGridRows` is only invoked once per rapid sequence of changes. This mitigates the race condition and maintains consistent grid state corresponding to the user's final selection.
  const debouncedRefreshGridRows = useCallback(
    debounce(refreshGridRows, appConfig.defaultDebounceTimeMs),
    []
  )

  const [dirty, setDirty] = useState(false)

  useEffect(() => {
    if (gridApi && selectedTradingAreas && currentAsOfUtc) {
      debouncedRefreshGridRows(gridApi)
      setDirty(true)
    }
  }, [
    selectedTradingAreas,
    currentAsOfUtc,
    preProcessingParams,
    currentKnowledgeAsOf
  ])

  useEffect(() => {
    if (dirty && gridApi) {
      logger.debug(
        'refreshInconsistentRows called from useRefreshGridRowsEffect'
      )
      refreshInconsistentRows(gridApi, currentAsOfUtc)
      setDirty(false)
    }
  }, [dirty])
}
