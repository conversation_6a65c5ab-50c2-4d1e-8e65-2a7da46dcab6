import { renderHook, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import {
  columnApiMock,
  combineWrappers,
  getGridProviderWrapper,
  getReduxWrapper
} from '@/core/testing'
import { useAutoSizeGroupColumnEffect } from '../useAutoSizeGroupColumnEffect'

describe('useAutoSizeGroupColumnEffect', () => {
  afterEach(() => {
    vi.clearAllMocks()
  })

  it('should call columnApi.autoSizeColumn', async () => {
    const { wrapper: gridProviderWrapper } = getGridProviderWrapper()
    const { wrapper: reduxWrapper } = getReduxWrapper({
      notifications: {
        isLoading: false
      }
    })

    renderHook(() => useAutoSizeGroupColumnEffect(), {
      wrapper: combineWrappers(reduxWrapper, gridProviderWrapper)
    })

    await waitFor(() => {
      expect(columnApiMock.autoSizeColumn).toHaveBeenCalled()
    })
  })
})
