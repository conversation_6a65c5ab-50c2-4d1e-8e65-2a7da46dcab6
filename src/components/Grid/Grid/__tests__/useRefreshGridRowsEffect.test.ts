import {
  setCurrentAsOfLocal,
  setSelectedTradingAreas
} from '@/core/redux/features/report'
import {
  combineWrappers,
  getGridApiMock,
  getGridProviderWrapper,
  getReduxWrapper
} from '@/core/testing'
import { AgGridReact } from '@ag-grid-community/react'
import { renderHook, waitFor } from '@testing-library/react'
import { RefObject } from 'react'
import { vi } from 'vitest'
import { useRefreshGridRowsEffect } from '../useRefreshGridRowsEffect'

describe('useRefreshGridRowsEffect', () => {
  afterEach(() => {
    vi.clearAllMocks()
    vi.clearAllTimers()
  })

  it('should purge the entire grid when there are no displayed rows', async () => {
    // given
    const { wrapper: reduxWrapper, store } = getReduxWrapper({
      report: {
        currentAsOfUtc: '2023-11-15T10:48:22.2649764Z'
      }
    })

    const gridApi = getGridApiMock({
      getDisplayedRowCount: vi.fn().mockReturnValue(0)
    })

    const gridApiRef = {
      current: { api: gridApi }
    } as RefObject<AgGridReact>

    const { wrapper: gridProviderWrapper } = getGridProviderWrapper({
      gridApiRef
    })

    const wrapper = combineWrappers(reduxWrapper, gridProviderWrapper)

    renderHook(useRefreshGridRowsEffect, {
      wrapper
    })

    // when
    store.dispatch(setSelectedTradingAreas([]))

    // then
    await waitFor(() =>
      expect(gridApi.refreshServerSide).toHaveBeenCalledTimes(1)
    )
    expect(gridApi.refreshServerSide).toHaveBeenCalledWith({ purge: true })
  })

  it('should refresh unique routes when there are displayed rows', async () => {
    // given
    const { wrapper: reduxWrapper, store } = getReduxWrapper({
      report: {
        currentAsOfUtc: '2023-11-15T10:48:22.2649764Z'
      }
    })

    const gridApi = getGridApiMock({
      getDisplayedRowCount: vi.fn().mockReturnValue(3),
      getModel: vi.fn().mockReturnValue({
        forEachNode: vi.fn().mockImplementation((callback) => {
          callback({
            data: { asOfUtc: '2023-11-15T10:48:22.2649764Z' },
            getGroupKeys: vi.fn().mockReturnValueOnce(['route1']),
            getRoute: vi.fn(),
            displayed: true
          })
          callback({
            data: { asOfUtc: '2023-11-15T10:48:22.2649764Z' },
            getGroupKeys: vi.fn().mockReturnValueOnce(['route2']),
            getRoute: vi.fn(),
            displayed: true
          })

          // Non-unique route
          callback({
            data: { asOfUtc: '2023-11-15T10:48:22.2649764Z' },
            getGroupKeys: vi.fn().mockReturnValueOnce(['route1']),
            getRoute: vi.fn(),
            displayed: true
          })
        })
      })
    })

    const gridApiRef = {
      current: { api: gridApi }
    } as RefObject<AgGridReact>

    const { wrapper: gridProviderWrapper } = getGridProviderWrapper({
      gridApiRef
    })

    const wrapper = combineWrappers(reduxWrapper, gridProviderWrapper)

    renderHook(useRefreshGridRowsEffect, {
      wrapper
    })

    // when
    store.dispatch(setSelectedTradingAreas([]))

    // then
    await waitFor(
      () => expect(gridApi.refreshServerSide).toHaveBeenCalledTimes(3) // root route + 2 named routes, ignoring the duplicate route1
    )
    expect(gridApi.refreshServerSide).toHaveBeenCalledWith({
      route: [],
      purge: false
    })
    expect(gridApi.refreshServerSide).toHaveBeenCalledWith({
      route: ['route1'],
      purge: false
    })
    expect(gridApi.refreshServerSide).toHaveBeenCalledWith({
      route: ['route2'],
      purge: false
    })
  })

  it('should purge invisible routes', async () => {
    // given
    const { wrapper: reduxWrapper, store } = getReduxWrapper({
      report: {
        currentAsOfUtc: '2023-11-15T10:48:22.2649764Z'
      }
    })

    const gridApi = getGridApiMock({
      getDisplayedRowCount: vi.fn().mockReturnValue(3),
      getModel: vi.fn().mockReturnValue({
        forEachNode: vi.fn().mockImplementation((callback) => {
          callback({
            data: { asOfUtc: '2023-11-15T10:48:22.2649764Z' },
            getGroupKeys: vi.fn().mockReturnValueOnce(['route1']),
            getRoute: vi.fn(),
            displayed: true
          })
          callback({
            data: { asOfUtc: '2023-11-15T10:48:22.2649764Z' },
            getGroupKeys: vi.fn().mockReturnValueOnce(['route2']),
            getRoute: vi.fn(),
            displayed: false
          })
        })
      })
    })

    const gridApiRef = {
      current: { api: gridApi }
    } as RefObject<AgGridReact>

    ;(window as any).purgeCollapsed = true

    const { wrapper: gridProviderWrapper } = getGridProviderWrapper({
      gridApiRef
    })

    const wrapper = combineWrappers(reduxWrapper, gridProviderWrapper)

    renderHook(useRefreshGridRowsEffect, {
      wrapper
    })

    // when
    store.dispatch(setSelectedTradingAreas([]))

    // then
    await waitFor(
      () => expect(gridApi.refreshServerSide).toHaveBeenCalledTimes(3) // root route + 2 named routes
    )
    expect(gridApi.refreshServerSide).toHaveBeenCalledWith({
      route: [],
      purge: false
    })
    expect(gridApi.refreshServerSide).toHaveBeenCalledWith({
      route: ['route1'],
      purge: false
    })
    expect(gridApi.refreshServerSide).toHaveBeenCalledWith({
      route: ['route2'],
      purge: true
    })
  })

  it('refreshInconsistentRows should only refresh inconsistent routes ', async () => {
    // given
    const { wrapper: reduxWrapper, store } = getReduxWrapper({
      report: {
        currentAsOfUtc: '2023-11-15T10:48:22.2649764Z'
      }
    })

    const gridApi = getGridApiMock({
      getModel: vi.fn().mockReturnValue({
        forEachNode: vi.fn().mockImplementation((callback) => {
          callback({
            data: { asOfUtc: '2023-11-15T10:48:22.2649764Z' },
            getGroupKeys: vi.fn().mockReturnValueOnce(['route1']),
            getRoute: vi.fn(),
            displayed: true
          })
          callback({
            data: { asOfUtc: '2023-11-10T10:48:22.2649764Z' }, // out of sync
            getGroupKeys: vi.fn().mockReturnValueOnce(['route2']),
            getRoute: vi.fn(),
            displayed: true
          })
          callback({
            data: { asOfUtc: '2023-11-15T10:48:22.2649764Z' },
            getGroupKeys: vi.fn().mockReturnValueOnce(['route3']),
            getRoute: vi.fn(),
            displayed: true
          })
        })
      })
    })

    const gridApiRef = {
      current: { api: gridApi }
    } as RefObject<AgGridReact>

    const { wrapper: gridProviderWrapper } = getGridProviderWrapper({
      gridApiRef
    })

    const wrapper = combineWrappers(reduxWrapper, gridProviderWrapper)

    renderHook(useRefreshGridRowsEffect, {
      wrapper
    })

    // when
    store.dispatch(setSelectedTradingAreas([]))

    // then
    await waitFor(
      () => expect(gridApi.refreshServerSide).toHaveBeenCalledTimes(5) // 4 for initial refresh (root route + 3 named routes) and 1 for refreshInconsistentRows
    )
    expect(gridApi.refreshServerSide).toHaveBeenCalledWith({
      route: [],
      purge: false
    })
    expect(gridApi.refreshServerSide).toHaveBeenCalledWith({
      route: ['route1'],
      purge: false
    })
    expect(gridApi.refreshServerSide).toHaveBeenCalledWith({
      route: ['route2'],
      purge: false
    })
  })

  it('should refresh grid rows only when the selected trading areas and current as of UTC are specified', async () => {
    // given
    const { wrapper: reduxWrapper, store } = getReduxWrapper({
      report: {
        selectedTradingAreas: null,
        currentAsOfUtc: ''
      }
    })

    const gridApi = getGridApiMock()

    const gridApiRef = {
      current: { api: gridApi }
    } as RefObject<AgGridReact>

    const { wrapper: gridProviderWrapper } = getGridProviderWrapper({
      gridApiRef
    })

    const wrapper = combineWrappers(reduxWrapper, gridProviderWrapper)

    renderHook(useRefreshGridRowsEffect, {
      wrapper
    })

    // then
    expect(gridApi.refreshServerSide).toHaveBeenCalledTimes(0)

    // when
    store.dispatch(setSelectedTradingAreas([]))

    // then
    expect(gridApi.refreshServerSide).toHaveBeenCalledTimes(0)

    // when
    store.dispatch(setCurrentAsOfLocal('2023-11-15T10:48:22.2649764Z'))

    // then
    await waitFor(() =>
      expect(gridApi.refreshServerSide).toHaveBeenCalledTimes(1)
    )

    // when
    // Reset to the initial state for testing purposes.
    store.dispatch(setSelectedTradingAreas(null as unknown as string[]))

    // then
    expect(gridApi.refreshServerSide).toHaveBeenCalledTimes(1)

    // when
    store.dispatch(setSelectedTradingAreas([]))

    // then
    await waitFor(() =>
      expect(gridApi.refreshServerSide).toHaveBeenCalledTimes(2)
    )
  })

  it('should refresh grid rows after the data changes', async () => {
    // given
    const { wrapper: reduxWrapper, store } = getReduxWrapper({
      report: {
        currentAsOfUtc: '2023-11-15T10:48:22.2649764Z',
        selectedTradingAreas: null
      }
    })

    const gridApi = getGridApiMock()

    const gridApiRef = {
      current: { api: gridApi }
    } as RefObject<AgGridReact>

    const { wrapper: gridProviderWrapper } = getGridProviderWrapper({
      gridApiRef
    })

    const wrapper = combineWrappers(reduxWrapper, gridProviderWrapper)

    renderHook(useRefreshGridRowsEffect, {
      wrapper
    })

    // when
    store.dispatch(setSelectedTradingAreas(['ABC']))

    // then
    await waitFor(() =>
      expect(gridApi.refreshServerSide).toHaveBeenCalledTimes(1)
    )
  })

  it('should refresh grid rows only one time once a user finishes making rapid selection changes', async () => {
    // given
    const { wrapper: reduxWrapper, store } = getReduxWrapper({
      report: {
        currentAsOfUtc: '2023-11-15T10:48:22.2649764Z',
        selectedTradingAreas: null
      }
    })

    const gridApi = getGridApiMock()

    const gridApiRef = {
      current: { api: gridApi }
    } as RefObject<AgGridReact>

    const { wrapper: gridProviderWrapper } = getGridProviderWrapper({
      gridApiRef
    })

    const wrapper = combineWrappers(reduxWrapper, gridProviderWrapper)

    renderHook(useRefreshGridRowsEffect, {
      wrapper
    })

    // when
    store.dispatch(setSelectedTradingAreas(['ABC']))
    store.dispatch(setSelectedTradingAreas(['CDE']))
    store.dispatch(setSelectedTradingAreas(['CDE', 'FGH']))
    store.dispatch(setCurrentAsOfLocal('2023-11-15T10:48:22.2649764Z'))
    store.dispatch(setCurrentAsOfLocal('2022-10-10T11:25:21.1234567Z'))

    // then
    await waitFor(() =>
      expect(gridApi.refreshServerSide).toHaveBeenCalledTimes(1)
    )
  })
})
