import { setColumnsStatus } from '@/core/redux/features/report'
import { getReduxWrapper } from '@/core/testing'
import { renderHook, waitFor } from '@testing-library/react'
import { act } from 'react-dom/test-utils'
import { vi } from 'vitest'
import * as hooks from '../../hooks'
import { useColumnsStatusEffect } from '../useColumnsStatusEffect'

describe('useColumnsStatusEffect', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  let mockedColDefs = [] as {
    colId: string
    headerClass?: undefined | string
    headerTooltip?: undefined | string
  }[]

  const setColDefSpy = vi.fn()

  vi.spyOn(hooks, 'useGridApi').mockReturnValue({
    // @ts-expect-error mock
    gridApi: {
      getColumnDefs: vi.fn(() => mockedColDefs)
    },
    columnApi: {
      // @ts-expect-error mock
      getColumn: vi.fn(() => ({
        setColDef: setColDefSpy
      }))
    }
  })

  test('add column header class and tooltip if column status is warning', async () => {
    mockedColDefs = [{ colId: 'test' }, { colId: 'test2' }]
    const { wrapper: reduxWrapper, store } = getReduxWrapper()

    renderHook(() => useColumnsStatusEffect(), {
      wrapper: reduxWrapper
    })

    act(() => {
      store.dispatch(
        setColumnsStatus({
          test: {
            status: 'Warning',
            message: 'test message'
          }
        })
      )
    })

    await waitFor(() => {
      expect(setColDefSpy).toHaveBeenCalledWith(
        {
          colId: 'test',
          headerClass: 'column-status-warn',
          headerTooltip: 'test message'
        },
        null
      )
    })
  })

  test('add column header class and tooltip if column status is error', async () => {
    mockedColDefs = [{ colId: 'test' }, { colId: 'test2' }] as {
      colId: string
      headerClass?: undefined | string
      headerTooltip?: undefined | string
    }[]

    const { wrapper: reduxWrapper, store } = getReduxWrapper()

    renderHook(() => useColumnsStatusEffect(), {
      wrapper: reduxWrapper
    })

    act(() => {
      store.dispatch(
        setColumnsStatus({
          test: {
            status: 'Error',
            message: 'test message'
          }
        })
      )
    })

    await waitFor(() => {
      expect(setColDefSpy).toHaveBeenCalledWith(
        {
          colId: 'test',
          headerClass: 'column-status-error',
          headerTooltip: 'test message'
        },
        null
      )
    })
  })

  test('remove column header class if column status is OK', async () => {
    mockedColDefs = [
      {
        colId: 'test',
        headerClass: 'column-status-warn',
        headerTooltip: 'test message'
      },
      { colId: 'test2' }
    ] as {
      colId: string
      headerClass?: undefined | string
      headerTooltip?: undefined | string
    }[]

    const { wrapper: reduxWrapper, store } = getReduxWrapper()

    renderHook(() => useColumnsStatusEffect(), {
      wrapper: reduxWrapper
    })

    act(() => {
      store.dispatch(
        setColumnsStatus({
          test: {
            status: 'Ok',
            message: 'test message'
          }
        })
      )
    })

    await waitFor(() => {
      expect(setColDefSpy).toHaveBeenCalledWith(
        {
          colId: 'test',
          headerClass: undefined,
          headerTooltip: 'test message'
        },
        null
      )
    })
  })

  test('remove column header class and tooltip if column status is not presented', async () => {
    mockedColDefs = [
      {
        colId: 'test',
        headerClass: 'column-status-warn',
        headerTooltip: 'test message'
      },
      {
        colId: 'test2',
        headerClass: 'column-status-error',
        headerTooltip: 'test message error',
        aggFunc: 'sum'
      }
    ] as {
      colId: string
      headerClass?: undefined | string
      headerTooltip?: undefined | string
    }[]

    const { wrapper: reduxWrapper, store } = getReduxWrapper({
      report: {
        columnInfo: [
          {
            column: 'test',
            shortDescription: 'original message for 1st'
          },
          {
            column: 'test2'
          }
        ]
      }
    })

    renderHook(() => useColumnsStatusEffect(), {
      wrapper: reduxWrapper
    })

    act(() => {
      store.dispatch(setColumnsStatus({}))
    })

    await waitFor(() => {
      expect(setColDefSpy).toHaveBeenCalledWith(
        {
          colId: 'test',
          headerClass: undefined,
          headerTooltip: 'original message for 1st\n\nAggregation: none'
        },
        null
      )
      expect(setColDefSpy).toHaveBeenCalledWith(
        {
          colId: 'test2',
          aggFunc: 'sum',
          headerClass: undefined,
          headerTooltip: 'Aggregation: sum'
        },
        null
      )
    })
  })
})
