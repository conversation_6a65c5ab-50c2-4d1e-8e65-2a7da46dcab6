import { logger } from '@/core/utils/logger'
import {
  combineWrappers,
  getGridApiMock,
  getGridProviderWrapper,
  getReduxWrapper
} from '@/core/testing'
import { vi } from 'vitest'
import { act, renderHook, waitFor } from '@testing-library/react'
import { addLoader, removeLoader } from '@/core/redux/features/notifications'
import { useRefreshGridLoggerEffect } from '../useRefreshGridLoggerEffect'

// mock logger
vi.mock('@/core/utils/logger', () => ({
  logger: {
    info: vi.fn(),
    debug: vi.fn()
  }
}))

it('should log the refresh time', async () => {
  const { wrapper: reduxWrapper, store } = getReduxWrapper({})
  const gridApi = getGridApiMock()
  const { wrapper: gridProviderWrapper } = getGridProviderWrapper({
    // @ts-expect-error - we don't need to mock all GridApi methods
    gridApi
  })

  const wrapper = combineWrappers(reduxWrapper, gridProviderWrapper)

  act(() => store.dispatch(addLoader('test')))
  renderHook(() => useRefreshGridLoggerEffect(), { wrapper })

  act(() => store.dispatch(removeLoader('test')))

  await waitFor(() => expect(logger.info).toHaveBeenCalled())

  expect(logger.info).toHaveBeenCalledWith(
    expect.stringContaining('Grid refreshed'),
    { refreshTimeMs: expect.any(Number), refreshedChunks: expect.any(Number) }
  )
})
