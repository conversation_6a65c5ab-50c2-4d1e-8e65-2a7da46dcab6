import { userPreferencesSelector } from '@/core/redux/features/users/selectors/userPreferencesSelector'
import { useAppSelect } from '@/core/redux/hooks'
import { componentVisibilityOptions } from './constants'
import { useMemo } from 'react'
import { isPivotModeEnabledSelector } from '@/core/redux/features/grid/selectors'

export const useComponentVisibilitySidePanelMenuItems = () => {
  const { componentVisibility } = useAppSelect(userPreferencesSelector)
  const isPivotModeEnabled = useAppSelect(isPivotModeEnabledSelector)

  const menuItems = useMemo(
    () => [
      {
        label: 'Header Bar',
        name: componentVisibilityOptions.headerBar,
        checked: componentVisibility.isAppHeaderVisible,
        toolTipText:
          'Displays Presets, Trading Areas, AsOf time-travel date picker, Live Mode toggle, Report timestamps',
        disabled: false
      },
      {
        label: 'Summary Bar',
        labelPlacement: 'right',
        name: componentVisibilityOptions.summaryBar,
        checked: componentVisibility.isSummaryBarVisible,
        toolTipText:
          'Displays summary values relevant for the shown report, e.g. aggregate risk for the selected Trading Areas, and some quickly accessible user controls like Repo dropdown & Trade QuickFilters.',
        disabled: false
      },
      {
        label: 'Grouping Bar',
        name: componentVisibilityOptions.groupingBar,
        checked: componentVisibility.isGroupingBarVisible,
        toolTipText:
          'Displays the column grouping hierarchy. Use the Columns side panel to select the columns grouping when this is hidden.',
        disabled: false
      },
      {
        label: 'Grid Filters',
        name: componentVisibilityOptions.floatingFiltersBar,
        checked: componentVisibility.isFloatingFiltersBarVisible,
        toolTipText:
          'Displays text boxes where filter values can be entered and a quickly accessible filter icon, under the column name header.',
        disabled: isPivotModeEnabled
      },
      {
        label: 'Footer Bar',
        name: componentVisibilityOptions.footerBar,
        checked: componentVisibility.isFooterBarVisible,
        toolTipText:
          'Displays the current App version and number of rows in the grid & pagination controls when the grid view has more than 1,000 rows.',
        disabled: false
      }
    ],
    [componentVisibility, isPivotModeEnabled]
  )

  return {
    menuItems
  }
}
