import { componentVisibilitySelector } from '@/core/redux/features/users/selectors/componentVisibilitySelector'
import { useAppSelect } from '@/core/redux/hooks'
import { useEffect } from 'react'
import { selectors } from './constants'

export const useComponentVisibilitySidePanelEffect = () => {
  const componentVisibility = useAppSelect(componentVisibilitySelector)
  const toggleVisibility = ({
    selector,
    isVisible
  }: {
    selector: string
    isVisible: boolean | undefined
  }) => {
    const element = document.querySelector<HTMLDivElement>(selector)
    if (element) {
      if (!isVisible) {
        element.style.display = 'none'
      } else {
        element.style.display = 'flex'
      }
    }
  }

  useEffect(() => {
    toggleVisibility({
      selector: selectors.summaryBar,
      isVisible: componentVisibility?.isSummaryBarVisible
    })
  }, [componentVisibility?.isSummaryBarVisible])

  useEffect(() => {
    toggleVisibility({
      selector: selectors.groupingBar,
      isVisible: componentVisibility?.isGroupingBarVisible
    })
  }, [componentVisibility?.isGroupingBarVisible])

  useEffect(() => {
    toggleVisibility({
      selector: selectors.footerBar,
      isVisible: componentVisibility?.isFooterBarVisible
    })
  }, [componentVisibility?.isFooterBarVisible])
}
