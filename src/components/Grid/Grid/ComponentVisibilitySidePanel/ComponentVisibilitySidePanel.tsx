import {
  toggleAppHeader,
  toggleFloating<PERSON>ar,
  toggleFooterBar,
  toggleGroupingBar,
  toggleSummaryBar
} from '@/core/redux/features/users'
import { componentVisibilitySelector } from '@/core/redux/features/users/selectors/componentVisibilitySelector'
import { useAppDispatch, useAppSelect } from '@/core/redux/hooks'
import { Field, TooltipWrapper } from '@core-clib/react'
import { useGridApi } from '../../hooks'
import { componentVisibilityOptions } from './constants'
import styles from './ComponentVisibilitySidePanel.module.scss'
import { useComponentVisibilitySidePanelMenuItems } from './useComponentVisibilitySidePanelMenuItems'

export const ComponentVisibilitySidePanel = () => {
  const { menuItems } = useComponentVisibilitySidePanelMenuItems()
  const theme = useAppSelect((state) => state.users.theme)
  const dispatch = useAppDispatch()
  const { gridApi } = useGridApi()
  const componentVisibilityState = useAppSelect(componentVisibilitySelector)

  const toggleGroupingBarVisibility = () => {
    if (componentVisibilityState?.isGroupingBarVisible) {
      gridApi?.setRowGroupPanelShow('never')
    } else {
      gridApi?.setRowGroupPanelShow('always')
    }
    dispatch(toggleGroupingBar())
  }

  const toggleVisibility = (name: string) => {
    switch (name) {
      case componentVisibilityOptions.headerBar:
        dispatch(toggleAppHeader())
        break
      case componentVisibilityOptions.summaryBar:
        dispatch(toggleSummaryBar())
        break
      case componentVisibilityOptions.footerBar:
        dispatch(toggleFooterBar())
        break
      case componentVisibilityOptions.groupingBar:
        toggleGroupingBarVisibility()
        break
      case componentVisibilityOptions.floatingFiltersBar:
        dispatch(toggleFloatingBar())
        break
      default:
        break
    }
  }

  return (
    <div
      data-testid="componentVisibilitySidePanel"
      className={styles['custom-wrapper']}
    >
      <div className={styles.fieldSet}>
        {menuItems.map(({ checked, label, name, toolTipText, disabled }) => (
          <TooltipWrapper
            key={label}
            tippyOptions={{
              duration: 0,
              content: `<div class="tippy-tooltip ${theme}">${toolTipText}</div>`,
              allowHTML: true
            }}
          >
            <Field
              label={label}
              labelPlacement="right"
              className={styles.field}
            >
              <input
                id={name}
                type="checkbox"
                name={name}
                onChange={() => toggleVisibility(name)}
                className="ag-input-field-input ag-checkbox-input"
                defaultChecked={checked}
                data-testid={`field-${name}`}
                placeholder={label}
                title={label}
                disabled={disabled}
              />
            </Field>
          </TooltipWrapper>
        ))}
      </div>
    </div>
  )
}
