export enum selectors {
  summaryBar = `core-clib-collapse>div`,
  footerBar = 'div.ag-paging-panel.ag-unselectable',
  groupingBar = 'div.ag-column-drop-wrapper',
  appHeader = 'core-clib-app-header',
  floatingGridFiltersBar = ''
}

export enum componentVisibilityOptions {
  headerBar = 'headerBar',
  summaryBar = 'summaryBar',
  footerBar = 'footerBar',
  groupingBar = 'groupingBar',
  floatingFiltersBar = 'floatingFiltersBar'
}
