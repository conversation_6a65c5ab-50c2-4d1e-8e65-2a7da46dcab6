import { ComponentVisibilitySidePanel } from '@/components/Grid/Grid/ComponentVisibilitySidePanel'
import { renderWithReduxInitialState } from '@/core/testing'
import { cleanup, fireEvent, waitFor } from '@testing-library/react'
import { ReactNode } from 'react'
import { vi } from 'vitest'
import { componentVisibilityOptions } from './constants'

vi.mock('@core-clib/react', () => ({
  DateHeaderFilter: ({ children }: { children: ReactNode }) => (
    <div data-testid="date-header-filter">{children}</div>
  ),
  TooltipWrapper: ({ children }: { children: ReactNode }) => (
    <div data-testid="tooltip-wrapper">{children}</div>
  ),
  Field: ({ children }: { children: ReactNode }) => (
    <div data-testid="field">{children}</div>
  )
}))

describe('ComponentVisibilitySidePanel', () => {
  beforeEach(() => {
    cleanup()
  })

  it('should render ComponentVisibilitySidePanel', () => {
    const { findByTestId } = renderWithReduxInitialState(
      <ComponentVisibilitySidePanel />
    )
    expect(findByTestId('componentVisibilitySidePanel')).toBeTruthy()
  })

  it('should render 5 elements', async () => {
    const { getAllByTestId } = renderWithReduxInitialState(
      <ComponentVisibilitySidePanel />
    )
    expect(getAllByTestId('tooltip-wrapper')).toHaveLength(5)
  })

  it('toggleAppHeader', async () => {
    const { getByTestId, store } = renderWithReduxInitialState(
      <ComponentVisibilitySidePanel />
    )
    const field = getByTestId(`field-${componentVisibilityOptions.headerBar}`)

    expect(
      store.getState().users.userPreferences.componentVisibility
        ?.isAppHeaderVisible
    ).toBe(true)

    fireEvent.click(field)

    await waitFor(() =>
      expect(
        store.getState().users.userPreferences.componentVisibility
          ?.isAppHeaderVisible
      ).toBe(false)
    )
  })

  it('toggleSummaryBar', async () => {
    const { getByTestId, store } = renderWithReduxInitialState(
      <ComponentVisibilitySidePanel />
    )
    const field = getByTestId(`field-${componentVisibilityOptions.summaryBar}`)

    expect(
      store.getState().users.userPreferences.componentVisibility
        ?.isSummaryBarVisible
    ).toBe(true)

    fireEvent.click(field)

    await waitFor(() =>
      expect(
        store.getState().users.userPreferences.componentVisibility
          ?.isSummaryBarVisible
      ).toBe(false)
    )
  })

  it('toggleFooterBar', async () => {
    const { getByTestId, store } = renderWithReduxInitialState(
      <ComponentVisibilitySidePanel />
    )
    const field = getByTestId(`field-${componentVisibilityOptions.footerBar}`)

    expect(
      store.getState().users.userPreferences.componentVisibility
        ?.isFooterBarVisible
    ).toBe(true)

    fireEvent.click(field)

    await waitFor(() =>
      expect(
        store.getState().users.userPreferences.componentVisibility
          ?.isFooterBarVisible
      ).toBe(false)
    )
  })

  it('toggleFloatingFiltersBar', async () => {
    const { getByTestId, store } = renderWithReduxInitialState(
      <ComponentVisibilitySidePanel />
    )
    const field = getByTestId(
      `field-${componentVisibilityOptions.floatingFiltersBar}`
    )

    expect(
      store.getState().users.userPreferences.componentVisibility
        ?.isFloatingFiltersBarVisible
    ).toBe(true)

    fireEvent.click(field)

    await waitFor(() =>
      expect(
        store.getState().users.userPreferences.componentVisibility
          ?.isFloatingFiltersBarVisible
      ).toBe(false)
    )
  })
})
