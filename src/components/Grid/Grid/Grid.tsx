import { ColumnRenamingDialog } from '@/components/Grid/ColumnRenamingDialog'
import {
  useGridApi,
  useGridEventHandlers,
  useGridOptions
} from '@/components/Grid/hooks'
import { useUpdateDrillThroughStateEffect } from '@/components/Grid/hooks/drillThrough/useUpdateDrillThroughStateEffect'
import { PIVOT_SEPARATOR } from '@/core/redux/features/report'
import {
  isFloatingFiltersBarVisibleSelector,
  componentVisibilitySelector
} from '@/core/redux/features/users/selectors/componentVisibilitySelector'
import {
  useAppSelect,
  useColumnToRename,
  useSetColumnDefsEffect
} from '@/core/redux/hooks'
import { Module } from '@ag-grid-community/core'
import { AgGridReact } from '@ag-grid-community/react'
import { ClipboardModule } from '@ag-grid-enterprise/clipboard'
import { ColumnsToolPanelModule } from '@ag-grid-enterprise/column-tool-panel'
import { FiltersToolPanelModule } from '@ag-grid-enterprise/filter-tool-panel'
import { MenuModule } from '@ag-grid-enterprise/menu'
import { RangeSelectionModule } from '@ag-grid-enterprise/range-selection'
import { ServerSideRowModelModule } from '@ag-grid-enterprise/server-side-row-model'
import { SetFilterModule } from '@ag-grid-enterprise/set-filter'
import { SideBarModule } from '@ag-grid-enterprise/side-bar'
import { StatusBarModule } from '@ag-grid-enterprise/status-bar'
import { GroupingPanelContextMenu } from '../GroupingPanelContextMenu'
import { useGlueChannelListenerEffect } from '../hooks/drillThrough/useGlueChannelListenerEffect'
import { useRefreshRiskReportSummaryEffect } from '../hooks/useRefreshRiskReportSummary'
import './Grid.scss'
import { useAutoSizeGroupColumnEffect } from './useAutoSizeGroupColumnEffect'
import { useRefreshGridLoggerEffect } from './useRefreshGridLoggerEffect'
import { useRefreshGridRowsEffect } from './useRefreshGridRowsEffect'
import { useComponentVisibilitySidePanelEffect } from '@/components/Grid/Grid/ComponentVisibilitySidePanel/useComponentVisibilitySidePanel'
import { useStore } from 'react-redux'
import { RootState } from '@/core/redux/store'
import { useColumnsStatusEffect } from './useColumnsStatusEffect'
import { purgeEmptyCache } from '@/core/services/grid'

const agGridModules: Module[] = [
  ServerSideRowModelModule,
  ColumnsToolPanelModule,
  SideBarModule,
  FiltersToolPanelModule,
  SetFilterModule,
  RangeSelectionModule,
  MenuModule,
  StatusBarModule,
  ClipboardModule
]

export const Grid = () => {
  const componentVisibilityState = useAppSelect(componentVisibilitySelector)
  const { onGridReadyHandler } = useGridEventHandlers()
  const { getState } = useStore<RootState>()
  const { gridOptions } = useGridOptions(getState)
  const { columnToRename } = useColumnToRename()
  const { gridApiRef } = useGridApi()
  const isFloatingFiltersBarVisible = useAppSelect(
    isFloatingFiltersBarVisibleSelector
  )

  useSetColumnDefsEffect()
  useRefreshGridRowsEffect()
  useRefreshRiskReportSummaryEffect()
  useAutoSizeGroupColumnEffect()
  useRefreshGridLoggerEffect()
  useUpdateDrillThroughStateEffect()
  useGlueChannelListenerEffect()
  useComponentVisibilitySidePanelEffect()
  useColumnsStatusEffect()

  return (
    <div
      className={`ag-theme-core-sdk grid-container ${
        isFloatingFiltersBarVisible ? 'hide-filter-icon' : ''
      }`}
      id="ag-grid-container"
    >
      <AgGridReact
        ref={gridApiRef}
        gridOptions={gridOptions}
        maintainColumnOrder
        onGridReady={onGridReadyHandler}
        onStoreRefreshed={purgeEmptyCache}
        tooltipMouseTrack={false}
        modules={agGridModules}
        pagination={componentVisibilityState?.isFooterBarVisible}
        pivotPanelShow={
          componentVisibilityState?.isGroupingBarVisible
            ? 'onlyWhenPivoting'
            : 'never'
        }
        serverSidePivotResultFieldSeparator={PIVOT_SEPARATOR}
        floatingFiltersHeight={isFloatingFiltersBarVisible ? 30 : 0}
      />
      <GroupingPanelContextMenu />
      {columnToRename && <ColumnRenamingDialog columnId={columnToRename} />}
    </div>
  )
}
