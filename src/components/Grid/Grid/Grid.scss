.core-light-theme {
  #ag-grid-container {
    --ag-foreground-color: var(--black);
    --clib-ag-row-group-text-color: var(--black);
    --ag-header-foreground-color: var(--black);
    --ag-cell-horizontal-border: #c0c0c0;
    --ag-selected-row-background-color: #598dae3e;
    --ag-row-group-divider-color: #777474;

    .ag-row {
      &.ag-row-even {
        background: #ffffff;
      }

      &.ag-row-odd {
        background: #f6f6f6;
      }
    }

    .ag-cell {
      border-right: 1px solid #777474;
      border-bottom: 1px solid #777474;
      &.ag-cell-range-bottom {
        border-bottom-color: var(--ag-range-selection-border-color);
      }
      &.ag-cell-range-right:not(.ag-cell-range-right) {
        border-right-color: var(--ag-range-selection-border-color);
      }
      &.ag-cell-last-left-pinned:not(.ag-cell-range-right):not(
          .ag-cell-range-single-cell
        ) {
        border-right: var(--ag-borders-critical) #161616;
      }
      &.ag-cell-first-right-pinned:not(.ag-cell-range-left):not(
          .ag-cell-range-single-cell
        ) {
        border-left: var(--ag-borders-critical) #161616;
      }
      &.ag-cell-range-selected:not(
          .ag-cell-range-single-cell
        ).ag-cell-range-left {
        border-left: var(--ag-borders-critical)
          var(--ag-range-selection-border-color);
      }
      &.ag-cell-range-selected:not(
          .ag-cell-range-single-cell
        ).ag-cell-range-bottom {
        border-bottom: var(--ag-borders-critical)
          var(--ag-range-selection-border-color);
      }
      &.ag-cell-range-selected:not(
          .ag-cell-range-single-cell
        ).ag-cell-range-top {
        border-top: var(--ag-borders-critical)
          var(--ag-range-selection-border-color);
      }
      &.ag-cell-range-selected:not(
          .ag-cell-range-single-cell
        ).ag-cell-range-right {
        border-right: var(--ag-borders-critical)
          var(--ag-range-selection-border-color);
      }
    }

    .negative {
      color: var(--red-3);
    }

    .positive {
      color: var(--green-4);
    }

    // MACROUIUX-1883: Missing column border when deepest level (no pivot mode)
    .ag-center-cols-viewport:not([aria-colindex='1']) {
      border-left: 1px solid var(--ag-row-group-divider-color);
      margin-left: -1px;
    }
  }
}

.grid-container {
  height: 100%;
  width: 100%;
  overflow: auto;

  .negative {
    color: var(--red-2);
  }

  .positive {
    color: var(--green-3);
  }

  .ag-root {
    height: 100%;
    width: 100%;
  }

  .ag-root-wrapper {
    height: 100%;
  }

  .ag-root-wrapper-body {
    height: 100%;
  }

  .align-right {
    text-align: right;
  }
}

.hide-filter-icon span[ref='eFilter'].ag-filter-icon {
  display: none;
}

//  hide the spinner on row expand
.ag-loading > .ag-loading-icon {
  display: none !important;
}

// Fix for wavy column borders
.ag-theme-core-sdk .ag-row .ag-cell,
.ag-theme-core-sdk .ag-row .ag-full-width-row .ag-cell-wrapper.ag-row-group {
  border: 1px;
  border-right: 1px solid #0d273f;
}

// Fix SDK colors braking changes

.ag-theme-core-sdk {
  --ag-selected-row-background-color: rgba(0, 145, 234, 0.28);
}

.ag-row-hover {
  background-color: var(--ag-row-hover-color) !important;
}

.ag-row-selected {
  background-color: var(--ag-selected-row-background-color) !important;
}

.filter-active {
  color: var(--ag-side-button-selected-color) !important  ;
}

// MACROUIUX-1847: fix pivot mode column border color and alignment
.ag-ltr .ag-header-cell-resize {
  border-right: 1.5px solid var(--ag-border-color);
  margin-right: 3px;
}
.ag-header-cell .ag-header-group-cell-no-group:not([aria-colindex='1']):not(
    [aria-colindex='2']
  ) {
  width: calc(240px - 0.5px) !important;
}

// MACROUIUX-1862: fix border color in pivot mode when row is expanded
.ag-theme-core-sdk .ag-row.ag-row-group.ag-full-width-row,
.ag-theme-core-sdk .ag-row.ag-row-group .auto-group-column-cell {
  border-right: 1px solid var(--clib-ag-row-group-divider-color);
}
.ag-theme-core-sdk
  .ag-root:has(.columns-group-border[aria-colindex='1'])
  .ag-header-group-cell-no-group[aria-colindex='1'],
.ag-theme-core-sdk
  .ag-root:has(.columns-group-border[aria-colindex='1'])
  .ag-header-cell[aria-colindex='1'] {
  border-right: 1px solid var(--ag-border-color);
}

// MACROUIUX-1883: Missing column border when deepest level (no pivot mode)
.ag-theme-core-sdk .ag-center-cols-viewport:not([aria-colindex='1']) {
  border-left: 1px solid var(--clib-ag-row-group-divider-color);
  margin-left: -1px;
}

// MACROUIUX-1879: Update SDK, added zero value class which overrides positive and negative classes
.ag-theme-core-sdk .ag-row .ag-cell:is(.positive).zero-value {
  color: var(--green-3);
}

.column-status-error {
  $color: #ef4e4e;
  background: $color;

  &:hover {
    background: darken($color, 10%) !important;
  }
}

.column-status-warn {
  $color: #f5a623;

  background: $color;

  &:hover {
    background: darken($color, 10%) !important;
  }
}

// MACROUIUX-1861: Filter button colour is not changing to yellow when column index is larger than 100, adding 101 to 300
.ag-theme-core-sdk {
  --clib-ag-active-filter-icon-color: var(--gold-3);
  .ag-filter-icon {
    color: var(--clib-ag-active-filter-icon-color);
  }
  @for $i from 100 to 300 {
    .ag-root:has(
        .ag-header-cell[aria-colindex='#{$i}'] .ag-filter-icon:not(.ag-hidden)
      ) {
      .ag-floating-filter[aria-colindex='#{$i}'] .ag-floating-filter-button {
        color: var(--clib-ag-active-filter-icon-color);
      }
      .ag-floating-filter[aria-colindex='#{$i}'] .ag-floating-filter-input {
        // this is so that, when the floating filter (default ag grid text input), has a value, ergo it is active the border color will be changed to the variable
        .date-field-input {
          // here we set the border color because the date field filter is a custom component
          border-color: var(--clib-ag-column-filter-active-border-color);
        }
        .ag-input-field-input {
          --ag-input-border-color: var(
            --clib-ag-column-filter-active-border-color
          );
        }
      }
    }
  }
}

// MACROUIUX-1003: Highlight filter side panel label yellow
.ag-theme-core-sdk .ag-side-button.ag-highlight-yellow {
  color: var(--ag-side-button-selected-color);
}

// MACROUIUX-1003: Highlight individual filters within filter side panel
.ag-theme-core-sdk .ag-filter-toolpanel-group-title.ag-highlight-yellow {
  color: var(--ag-side-button-selected-color);
}

// MACROUIUX-2037 Overriding styles for radio buttons and checkboxes
core-clib-field input[type='radio'],
core-clib-field input[type='checkbox'] {
  background-color: transparent;
  border: none;
}
