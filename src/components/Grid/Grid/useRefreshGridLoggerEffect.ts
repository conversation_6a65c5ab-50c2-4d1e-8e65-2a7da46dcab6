import { useGridApi } from '../hooks'
import { useEffect, useRef } from 'react'
import { useAppSelect } from '@/core/redux/hooks'
import { logger } from '@/core/utils/logger'
import { useAfterRefreshEffect } from '@/core/hooks/useAfterRefreshEffect'
import { appConfig } from '@/core/config'

// TODO: Replace with native onModelUpdated agGrid option
export const useRefreshGridLoggerEffect = () => {
  const refreshTimestamp = useRef(0)
  const chunkCount = useRef(0)
  const measuredTime = useRef(0)
  const isLoading = useAppSelect((state) => state.notifications.isLoading)
  const { gridApi } = useGridApi()
  const logStartTime = () =>
    logger.debug(
      `Grid refresh started at ${new Date(
        refreshTimestamp.current
      ).toLocaleTimeString()} and ${new Date(
        refreshTimestamp.current
      ).getMilliseconds()} ms`
    )
  const logChunk = () =>
    logger.debug(
      `Grid chunk refresh ${chunkCount.current} completed in ${
        measuredTime.current / 1000
      } s`
    )
  const logCompletedTime = () => {
    const completionTime = new Date(
      refreshTimestamp.current + measuredTime.current
    )
    logger.debug(
      `Grid refresh completed at ${completionTime.toLocaleTimeString()} and ${completionTime.getMilliseconds()} ms`
    )
  }

  useEffect(() => {
    if (gridApi) {
      if (isLoading && !refreshTimestamp.current) {
        refreshTimestamp.current = Date.now()
        logStartTime()
      }
      if (!isLoading && refreshTimestamp.current) {
        measuredTime.current = Date.now() - refreshTimestamp.current
        chunkCount.current++
        logChunk()
      }
    }
  }, [isLoading])

  useAfterRefreshEffect(
    () => {
      if (refreshTimestamp.current) {
        measuredTime.current = Date.now() - refreshTimestamp.current
        logger.info(`Grid refreshed in ${measuredTime.current / 1000} s`, {
          refreshTimeMs: measuredTime.current,
          refreshedChunks: chunkCount.current
        })
        logCompletedTime()
      }
      measuredTime.current = 0
      refreshTimestamp.current = 0
      chunkCount.current = 0
    },
    [gridApi],
    appConfig.afterRefreshDebounceTimeMs * 2
  )
}
