import { useGridApi } from '../hooks'
import { useAfterRefreshEffect } from '@/core/hooks/useAfterRefreshEffect'

export const useAutoSizeGroupColumnEffect = () => {
  const { gridApi, columnApi } = useGridApi()

  useAfterRefreshEffect(() => {
    if (gridApi && columnApi) {
      // By deferring the function call with setTimeout,
      // we're allowing any pending render operations to complete
      // before autoSizeColumn is called,
      // This action will get triggered after onRowGroupOpened Event and Closed events also.
      setTimeout(() => {
        if (columnApi) {
          columnApi.autoSizeColumn('ag-Grid-AutoColumn')
        }
      }, 0)
    }
  }, [gridApi, columnApi])
}
