import { ChangeEvent, useMemo, useState } from 'react'
import { useColumnToRename } from '@/core/redux/hooks'
import { useGridUtils } from '@/components/Grid/hooks'

interface UseColumnRenamingDialogProps {
  columnId: string
}

export const useColumnRenamingDialog = ({
  columnId
}: UseColumnRenamingDialogProps) => {
  const [newColumnName, setNewColumnName] = useState<string>('')
  const { columnToRename, cancelColumnRenaming } = useColumnToRename()
  const {
    changeHeaderName,
    isColumnRenamed: isRenamed,
    getHeaderName,
    restoreOriginalHeaderName
  } = useGridUtils()

  const isOpen = Boolean(columnToRename)

  const title = useMemo(() => {
    const headerName = getHeaderName(columnId)
    return `Rename Column: ${headerName}`
  }, [columnId, getHeaderName])

  const isColumnRenamed = isRenamed(columnId)
  const isUndoRenamingButtonDisabled = !isColumnRenamed
  const isSaveButtonDisabled = !newColumnName
  const onColumnNameInputChange = (event: ChangeEvent<HTMLInputElement>) => {
    setNewColumnName(event.target.value)
  }

  const cancelRenaming = () => {
    cancelColumnRenaming()
  }

  const undoRenaming = () => {
    restoreOriginalHeaderName(columnId)
    cancelColumnRenaming()
  }

  const renameColumn = () => {
    changeHeaderName({
      targetColumnId: columnId,
      newHeaderName: newColumnName
    })
    cancelColumnRenaming()
  }

  return {
    isOpen,
    title,
    newColumnName,
    isUndoRenamingButtonDisabled,
    isSaveButtonDisabled,
    cancelRenaming,
    onColumnNameInputChange,
    undoRenaming,
    renameColumn,
    setNewColumnName
  }
}
