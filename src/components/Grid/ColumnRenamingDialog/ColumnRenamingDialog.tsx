import './ColumnRenamingDialog.scss'
import { ModalDialog, Field, Button } from '@core-clib/react'
import { useColumnRenamingDialog } from '@/components/Grid/ColumnRenamingDialog'
import { FC } from 'react'

interface ColumnRenamingDialogProps {
  columnId: string
}

export const ColumnRenamingDialog: FC<ColumnRenamingDialogProps> = ({
  columnId
}) => {
  const {
    title,
    newColumnName,
    isUndoRenamingButtonDisabled,
    isSaveButtonDisabled,
    cancelRenaming,
    onColumnNameInputChange,
    undoRenaming,
    renameColumn
  } = useColumnRenamingDialog({ columnId })

  return (
    <ModalDialog isOpen title={title} onClose={cancelRenaming}>
      <Field fullWidth>
        <input
          placeholder="New Column Name..."
          onInput={onColumnNameInputChange}
          value={newColumnName}
          className="input"
        />
      </Field>
      <div className="buttons">
        <Button
          text="Undo Renaming"
          intent="danger"
          disabled={isUndoRenamingButtonDisabled}
          onClick={undoRenaming}
        />
        <div>
          <Button
            text="Save"
            intent="success"
            onClick={renameColumn}
            disabled={isSaveButtonDisabled}
          />
          <Button text="Cancel" intent="primary" onClick={cancelRenaming} />
        </div>
      </div>
    </ModalDialog>
  )
}
