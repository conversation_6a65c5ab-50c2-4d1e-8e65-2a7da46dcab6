import { useGridA<PERSON>, useGridOptions } from '../../hooks'
import { vi } from 'vitest'
import {
  allHandlers,
  renderWithReduxInitialState,
  setupMockServer
} from '@/core/testing'
import { useColumnToRename } from '@/core/redux/hooks'
import { ColumnRenamingDialog } from '../ColumnRenamingDialog'
import { useStore } from 'react-redux'
import { RootState } from '@/core/redux/store'
import { fireEvent, screen, waitFor } from '@testing-library/react'
import { AgGridReact } from '@ag-grid-community/react'
import { waitForElementByQuerySelector } from '@/core/testing/utils'
import { rowData, columnDefs } from '@/core/testing/mocks'

const gridReadySpy = vi.fn()

describe('ColumnRenamingDialog', () => {
  setupMockServer(...allHandlers)

  beforeEach(() => {
    vi.resetAllMocks()
  })

  test('adGrid renders rename column menu', async () => {
    /**
     * Render Grid
     */
    const { container } = renderWithReduxInitialState(
      <TestGrid onGridReady={gridReadySpy} />
    )

    await waitFor(() => {
      expect(gridReadySpy).toHaveBeenCalled()
    })

    /**
     * Open mainMenu
     */
    const mainMenu = container.querySelector('span[ref="eMenu"]')
    expect(mainMenu).toBeTruthy()
    fireEvent.click(mainMenu)

    expect(screen.getByText('Rename Column')).toBeInTheDocument()
    expect(screen.getByText('Reset Grid Layout')).toBeInTheDocument()
    expect(screen.getByText('Reset Column Order')).toBeInTheDocument()
    expect(screen.getByText('Make')).toBeInTheDocument()
  })

  test('adGrid renders rename column dialog and change column name', async () => {
    /**
     * Render Grid
     */
    const { container } = renderWithReduxInitialState(
      <TestGrid onGridReady={gridReadySpy} />,
      {
        report: {
          name: 'Test Report',
          columnInfo: [
            {
              column: 'make',
              name: 'Make old name'
            }
          ]
        }
      }
    )
    await waitFor(() => {
      expect(gridReadySpy).toHaveBeenCalled()
    })

    /**
     * Open mainMenu
     */
    let mainMenu = container.querySelector('span[ref="eMenu"]')
    expect(mainMenu).toBeTruthy()
    fireEvent.click(mainMenu)

    /**
     * Rename column
     */
    fireEvent.click(screen.getByText('Rename Column'))

    expect(screen.getByTitle('Rename Column:')).toBeInTheDocument()

    const input = await waitForElementByQuerySelector(
      document.body,
      'input[placeholder="New Column Name..."]'
    )

    const saveButton = await waitForElementByQuerySelector(
      document.body,
      'core-clib-button[text="Save"]'
    )

    fireEvent.input(input, { target: { value: 'abc123' } })
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(screen.getByText(/abc123/)).toBeInTheDocument()
    })

    /**
     * Undo renaming
     */
    mainMenu = container.querySelector('span[ref="eMenu"]')

    waitFor(() => {
      expect(fireEvent.click(mainMenu)).toBeTruthy()
    })

    fireEvent.click(screen.getByText('Rename Column'))
    const undoButton = await waitForElementByQuerySelector(
      document.body,
      'core-clib-button[text="Undo Renaming"]'
    )

    fireEvent.click(undoButton)

    screen.debug()

    await waitFor(() => {
      expect(screen.getByText('Make old name')).toBeInTheDocument()
    })

    expect(screen.queryByText('abc123')).toBeNull()
  })
})

const TestGrid = ({ onGridReady }: any) => {
  const { gridApiRef } = useGridApi()
  const { columnToRename } = useColumnToRename()
  const { getState } = useStore<RootState>()
  const { gridOptions } = useGridOptions(getState)

  return (
    <div style={{ height: 500 }}>
      <AgGridReact
        ref={gridApiRef}
        gridOptions={{
          suppressMenuHide: true,
          getMainMenuItems: gridOptions.getMainMenuItems,
          rowData,
          columnDefs
        }}
        onGridReady={onGridReady}
      />
      {columnToRename && <ColumnRenamingDialog columnId={columnToRename} />}
    </div>
  )
}
