export const getGroupPath = (sourceElement: HTMLElement): string[] => {
  const getContainer = (element: HTMLElement | null): HTMLElement | null => {
    if (!element) {
      return null
    }

    const role = element.getAttribute('role')

    if (role === 'listbox') {
      return element
    }

    return getContainer(element.parentElement)
  }

  const container = getContainer(sourceElement)

  if (!container) {
    return []
  }

  const path: string[] = []

  container.childNodes.forEach((childNode) => {
    const role = (childNode as HTMLElement).getAttribute('role')

    if (role === 'option') {
      const titleElement = (childNode as HTMLElement).querySelector(
        'span.ag-column-drop-cell-text'
      )

      if (titleElement?.innerHTML) {
        path.push(titleElement?.innerHTML)
      }
    }
  })

  return path
}
