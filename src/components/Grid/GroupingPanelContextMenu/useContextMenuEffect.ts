import { useEffect } from 'react'

interface ContextMenuEffectProps {
  showContextMenu: (event: MouseEvent) => void
}

export const useContextMenuEffect = ({
  showContextMenu
}: ContextMenuEffectProps) => {
  useEffect(() => {
    window.addEventListener('contextmenu', showContextMenu)
    window.addEventListener('click', showContextMenu)

    // Cleanup function to remove event listeners when component unmounts
    return () => {
      window.removeEventListener('contextmenu', showContextMenu)
      window.removeEventListener('click', showContextMenu)
    }
  }, [showContextMenu])
}
