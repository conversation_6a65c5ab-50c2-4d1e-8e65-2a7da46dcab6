import { useState } from 'react'
import { getGroupPath } from './getGroupPath'

export const useGroupContextMenuEventHandlers = () => {
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null)
  const [groupPath, setGroupPath] = useState<string[]>([])

  return {
    handleContextMenuShow: (event: MouseEvent) => {
      const target = event.target as HTMLSpanElement

      setGroupPath(getGroupPath(target))
      setSelectedGroup(target?.innerHTML)
    },
    handleContextMenuHide: () => {
      setSelectedGroup(null)
    },
    showContextMenuPredicate: (event: MouseEvent) => {
      const target = event.target as HTMLSpanElement
      const isGroupButton = target?.classList.contains(
        'ag-column-drop-cell-text'
      )

      // Show context menu only when user right clicks on group button
      return isGroupButton && event.type !== 'click'
    },
    contextMenuData: {
      selectedGroup,
      groupPath
    }
  }
}
