import { useState } from 'react'

interface ContextMenuProps {
  onContextMenuShow: (event: MouseEvent) => void
  onContextMenuHide: () => void
  showContextMenuPredicate: (event: MouseEvent) => boolean
}

export const useContextMenu = ({
  onContextMenuShow,
  onContextMenuHide,
  showContextMenuPredicate
}: ContextMenuProps) => {
  const [position, setPosition] = useState<{
    top: number
    left: number
  } | null>(null)

  const showContextMenu = (event: MouseEvent) => {
    if (showContextMenuPredicate(event)) {
      event.preventDefault()

      onContextMenuShow(event)
      setPosition({ top: event.y, left: event.x })
    } else {
      // Hide context menu if predicate is not satisfied
      setPosition(null)
      onContextMenuHide()
    }
  }

  return {
    position,
    showContextMenu
  }
}
