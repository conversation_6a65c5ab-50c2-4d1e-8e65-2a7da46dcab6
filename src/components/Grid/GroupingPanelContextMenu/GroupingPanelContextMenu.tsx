import { FC } from 'react'
import { ContextMenu } from '@core-clib/react'
import { useContextMenuEffect } from './useContextMenuEffect'
import { useContextMenuActions } from './useContextMenuActions'
import { useContextMenu } from './useContextMenu'
import { useGroupContextMenuEventHandlers } from './useGroupContextMenuEventHandlers'

export const GroupingPanelContextMenu: FC = () => {
  // context menu event handlers for group rows
  const {
    contextMenuData,
    handleContextMenuShow,
    handleContextMenuHide,
    showContextMenuPredicate
  } = useGroupContextMenuEventHandlers()

  // generic context menu hook
  const { position, showContextMenu } = useContextMenu({
    onContextMenuShow: handleContextMenuShow,
    onContextMenuHide: handleContextMenuHide,
    showContextMenuPredicate
  })

  const { actions, handleExpandCollapseAction } =
    useContextMenuActions(contextMenuData)

  useContextMenuEffect({ showContextMenu })

  return (
    <div
      style={{
        position: 'absolute',
        top: position?.top + 'px',
        left: position?.left + 'px',
        visibility: position ? 'visible' : 'hidden',
        zIndex: 9999
      }}
    >
      <ContextMenu actions={actions} onSelected={handleExpandCollapseAction} />
    </div>
  )
}
