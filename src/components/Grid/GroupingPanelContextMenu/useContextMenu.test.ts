import { act, renderHook, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { useContextMenu } from './useContextMenu'

describe('useContextMenu', () => {
  it('should return position and showContextMenu', () => {
    const { result } = renderHook(() =>
      useContextMenu({
        onContextMenuShow: vi.fn(),
        onContextMenuHide: vi.fn(),
        showContextMenuPredicate: () => true
      })
    )

    expect(result.current).toEqual({
      position: null,
      showContextMenu: expect.any(Function)
    })
  })

  it('should call onContextMenuShow and setPosition when showContextMenu is called', () => {
    const onContextMenuShow = vi.fn()
    const onContextMenuHide = vi.fn()
    const showContextMenuPredicate = vi.fn(() => true)
    const { result } = renderHook(() =>
      useContextMenu({
        onContextMenuShow,
        onContextMenuHide,
        showContextMenuPredicate
      })
    )
    const mockedMouseEvent = {
      preventDefault: vi.fn(),
      x: 12,
      y: 34
    } as any

    act(() => {
      result.current.showContextMenu(mockedMouseEvent)
    })

    waitFor(() => {
      expect(onContextMenuShow).toHaveBeenCalled()
    })
    expect(onContextMenuHide).not.toHaveBeenCalled()
    waitFor(() => {
      expect(result.current.position).toEqual({ top: 34, left: 12 })
    })
  })

  it('should call onContextMenuHide and setPosition when showContextMenu is called and predicate is not satisfied', () => {
    const onContextMenuShow = vi.fn()
    const onContextMenuHide = vi.fn()
    const showContextMenuPredicate = vi.fn(() => false)

    const { result } = renderHook(() =>
      useContextMenu({
        onContextMenuShow,
        onContextMenuHide,
        showContextMenuPredicate
      })
    )
    const mockedMouseEvent = {
      preventDefault: vi.fn(),
      x: 12,
      y: 34
    } as any

    result.current.showContextMenu(mockedMouseEvent)

    expect(onContextMenuHide).toHaveBeenCalled()
    expect(onContextMenuShow).not.toHaveBeenCalled()
    expect(result.current.position).toEqual(null)
  })
})
