import { getGridProviderWrapper, gridApiMock } from '@/core/testing'
import { renderHook } from '@testing-library/react'
import { ActionTypesEnum } from './types'
import { useContextMenuActions } from './useContextMenuActions'

describe('useContextMenuActions', () => {
  const { wrapper } = getGridProviderWrapper({ gridApi: gridApiMock })

  it('should return handleExpandCollapseAction', () => {
    const { result } = renderHook(
      () =>
        useContextMenuActions({
          selectedGroup: 'group1',
          groupPath: ['group1', 'group2']
        }),
      { wrapper }
    )

    expect(result.current).toEqual({
      actions: expect.any(Array),
      handleExpandCollapseAction: expect.any(Function)
    })
  })

  it('should call handle expandGroup Action', () => {
    const { result } = renderHook(
      () =>
        useContextMenuActions({
          selectedGroup: 'group1',
          groupPath: ['group1', 'group2']
        }),
      {
        wrapper
      }
    )

    result.current.handleExpandCollapseAction({
      id: ActionTypesEnum.expandGroup
    })

    expect(gridApiMock.forEachNode).toHaveBeenCalled()
  })

  it('should call handle collapseGroup Action', () => {
    const { result } = renderHook(
      () =>
        useContextMenuActions({
          selectedGroup: 'group1',
          groupPath: ['group1', 'group2']
        }),
      {
        wrapper
      }
    )

    result.current.handleExpandCollapseAction({
      id: ActionTypesEnum.collapseGroup
    })

    expect(gridApiMock.forEachNode).toHaveBeenCalled()
  })
})
