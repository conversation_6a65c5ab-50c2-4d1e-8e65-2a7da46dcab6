import { useGridA<PERSON> } from '../hooks'
import { IAction } from '@core-clib/web-components/types'
import { ActionTypesEnum, ContextMenuData } from './types'

export const useContextMenuActions = ({
  selectedGroup,
  groupPath
}: ContextMenuData) => {
  const { gridApi } = useGridApi()

  const actions: IAction[] = [
    {
      label: 'Expand Group',
      id: ActionTypesEnum.expandGroup,
      itemClass: 'menu-item'
    },
    {
      label: 'Collapse Group',
      id: ActionTypesEnum.collapseGroup,
      itemClass: 'menu-item'
    }
  ]

  if (!gridApi) {
    return {
      actions,
      handleExpandCollapseAction: () => {
        throw new Error('Grid API is not available')
      }
    }
  }

  const handleExpandCollapseAction = ({ id: action }: IAction) => {
    const groupSequenceLength = groupPath.findIndex(
      (path) => path === selectedGroup
    )

    if (action === ActionTypesEnum.expandGroup) {
      expandGroup(groupSequenceLength)
    } else if (action === ActionTypesEnum.collapseGroup) {
      collapseGroup(groupSequenceLength)
    }
  }

  const expandGroup = (groupSequenceLength: number) => {
    gridApi.forEachNode((node) => {
      if (
        node.displayed &&
        !node.expanded &&
        node.level === groupSequenceLength
      ) {
        node.setExpanded(true)
      }
    })
  }

  const collapseGroup = (groupSequenceLength: number) => {
    gridApi.forEachNode((node) => {
      if (
        node.displayed &&
        node.expanded &&
        node.level === groupSequenceLength
      ) {
        node.setExpanded(false)
      }
    })
  }

  return {
    actions,
    handleExpandCollapseAction
  }
}
