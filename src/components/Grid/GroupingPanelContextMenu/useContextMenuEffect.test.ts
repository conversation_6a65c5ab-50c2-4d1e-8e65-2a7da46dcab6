import { renderHook } from '@testing-library/react'
import { vi } from 'vitest'
import { useContextMenuEffect } from './useContextMenuEffect'

describe('useContextMenuEffect', () => {
  const mockShowContextMenu = vi.fn()
  const mockAddEventListener = vi.fn()
  const mockRemoveEventListener = vi.fn()

  window.addEventListener = mockAddEventListener
  window.removeEventListener = mockRemoveEventListener

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should call window.addEventListener', () => {
    renderHook(() =>
      useContextMenuEffect({
        showContextMenu: mockShowContextMenu
      })
    )

    expect(mockAddEventListener).toHaveBeenCalledTimes(2)
    expect(mockAddEventListener).toHaveBeenNthCalledWith(
      1,
      'contextmenu',
      mockShowContextMenu
    )
    expect(mockAddEventListener).toHaveBeenNthCalledWith(
      2,
      'click',
      mockShowContextMenu
    )
  })

  it('should call window.removeEventListener', () => {
    const { unmount } = renderHook(() =>
      useContextMenuEffect({
        showContextMenu: mockShowContextMenu
      })
    )

    unmount()
    expect(mockRemoveEventListener).toHaveBeenCalledTimes(2)
    expect(mockRemoveEventListener).toHaveBeenNthCalledWith(
      1,
      'contextmenu',
      mockShowContextMenu
    )
    expect(mockRemoveEventListener).toHaveBeenNthCalledWith(
      2,
      'click',
      mockShowContextMenu
    )
  })
})
