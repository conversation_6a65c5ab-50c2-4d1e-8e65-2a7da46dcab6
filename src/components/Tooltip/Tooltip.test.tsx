import { screen } from '@testing-library/react'
import { Tooltip } from './Tooltip'
import { ColumnApi, GridApi } from '@ag-grid-community/core'
import { renderWithReduxInitialState } from '@/core/testing'

describe('test Tooltip component', () => {
  const props = {
    value: '-1709504.9971317758',
    location: '',
    api: {} as GridA<PERSON>,
    columnApi: {} as ColumnApi,
    context: undefined
  }

  it('should render number value with Precision', () => {
    renderWithReduxInitialState(<Tooltip {...props} />)
    expect(screen.getByText('-1709504.9971317758')).toBeInTheDocument()
  })
})
