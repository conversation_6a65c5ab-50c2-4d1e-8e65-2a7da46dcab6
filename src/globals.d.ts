interface Performance extends Performance {
  /**
   * MDN says that `performance.memory` is non-standard extension added to Chrome.
   * So this is the reason why it is not defined in TypeScript.
   */
  memory?: {
    /** The maximum size of the heap, in bytes, that is available to the context. */
    jsHeapSizeLimit: number
    /** The total allocated heap size, in bytes. */
    totalJSHeapSize: number
    /** The currently active segment of JS heap, in bytes. */
    usedJSHeapSize: number
  }
}
