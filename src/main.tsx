import React from 'react'
import ReactDOM from 'react-dom/client'
import { Router } from './components/App/Router'
import { env } from './core/config'

const urlParams = new URLSearchParams(window.location.search)

console.log(`M72UI running in ${env.mode} mode`)
;(window as any).debugMode =
  env.isLoggerDebugModeEnabledByDefault || urlParams.has('debugMode')
;(window as any).purgeCollapsed = env.purgeCollapsed

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <React.StrictMode>
    <Router />
  </React.StrictMode>
)
