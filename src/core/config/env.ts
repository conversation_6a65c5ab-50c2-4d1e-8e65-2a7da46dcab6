/**
 * Implements the fail-fast approach. This means that the application will thorw an error immediately after startup if a given variable is not present.
 * The reason for this is that the application becomes useless, and unable to work without crucial variables such as api url, tokens, public keys, and so on.
 * Therefore, let the app throw an error immediately instead of letting the application pass the error silently
 * and forcing developer to debug why the app does not work.
 *
 * More about pros and cons of this solution:
 * https://prdbitbucket.saccap.int/projects/MSTRAT/repos/m72ui/pull-requests/9/overview?commentId=183890
 */

const mode = getEnvironment()

export const getEnvironmentVariable = (
  variable: string,
  fallback?: string
): string => {
  const defaultValue: string | undefined = import.meta.env[variable]
  const value: string | undefined =
    import.meta.env[`${variable}_${mode}`] ?? defaultValue ?? fallback

  if (!value) {
    throw new Error(`Couldn't find environment variable: ${variable}`)
  }

  return value
}

export const env = {
  riskReportApiUrl: getEnvironmentVariable('VITE_RISK_REPORT_API_URL'),
  riskReportTimetravelApiUrl: getEnvironmentVariable(
    'VITE_RISK_REPORT_TIMETRAVEL_API_URL'
  ),
  agGridLicenseKey: getEnvironmentVariable('VITE_AG_GRID_LICENSE_KEY'),
  epicApiUrl: getEnvironmentVariable('VITE_EPIC_API_URL'),
  aggregatorApiUrl: getEnvironmentVariable('VITE_AGGREGATOR_API_URL'),
  epicMacro: getEnvironmentVariable('VITE_EPIC_MACRO_API_URI'),
  isLoggerEnabled: getEnvironmentVariable('VITE_LOGGER_ENABLED') === 'true',
  isLoggerDebugModeEnabledByDefault:
    getEnvironmentVariable('VITE_LOGGER_DEBUG_MODE_ENABLED') === 'true',
  purgeCollapsed:
    getEnvironmentVariable('VITE_PURGE_COLLAPSED_GROUPS_FROM_GRID_CACHE') ===
    'true',
  defaultDebounceTimeMs: Number(
    getEnvironmentVariable('VITE_DEFAULT_DEBOUNCE_TIME_MS')
  ),
  toastDisplayTimeMs: 5000,
  amountOfPivotColumns: 5,
  extendedDebounceTimeMs: Number(
    getEnvironmentVariable('VITE_EXTENDED_DEBOUNCE_TIME_MS')
  ),
  oktaClientId: getEnvironmentVariable('VITE_OKTA_CLIENT_ID'),
  oktaIssuerId: getEnvironmentVariable('VITE_OKTA_ISSUER_ID'),
  isOkta: getEnvironmentVariable('VITE_IS_OKTA_ENABLED', 'false') === 'true',
  oktaIssuerUri: `${getEnvironmentVariable(
    'VITE_OKTA_ISSUER_URI'
  )}/${getEnvironmentVariable('VITE_OKTA_ISSUER_ID')}`,
  mode
}

export function getEnvironment(appUrl?: string): string {
  const url = appUrl ?? window.location.href

  if (url.includes('localhost') || url.includes('127.0.0.1')) {
    return 'LOCAL'
  }

  if (url.includes('-qa.')) {
    return 'UAT'
  }

  if (url.includes('-dev.') || url.includes('-v1')) {
    return 'DEV'
  }

  if (!url.includes('-dev.') && !url.includes('-qa.')) {
    return 'PRD'
  }

  return 'LOCAL'
}
