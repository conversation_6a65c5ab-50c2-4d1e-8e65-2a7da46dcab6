import { getEnvironment, getEnvironmentVariable } from './env'

describe('env', () => {
  // let window: any
  beforeEach(() => {
    global.window = Object.create(window)
    const url = 'http://dummy.com'
    Object.defineProperty(window, 'location', {
      value: {
        href: url
      },
      writable: true
    })
  })
  it('getEnvironmentVariable should throw an error if the variable is not present', () => {
    expect(() => getEnvironmentVariable('unknown_variable')).toThrow(
      "Couldn't find environment variable: unknown_variable"
    )
  })

  it('returns value', () => {
    const value = getEnvironmentVariable('VITE_TEST_VARIABLE')
    const localOverrideValue = getEnvironmentVariable(
      'VITE_TEST_VARIABLE_OVERRIDE'
    )
    expect(value).toEqual('test value')
    expect(localOverrideValue).toEqual('local value')
  })

  it('returns LOCAL value', () => {
    // mock window.location.href
    // window.location.href = 'http://localhost:3000'

    const value = getEnvironmentVariable('VITE_TEST_VARIABLE_OVERRIDE')
    expect(value).toEqual('local value')
  })
})

describe('getEnvironment', () => {
  it('returns LOCAL', () => {
    const url = 'http://localhost:3000'
    const mode = getEnvironment(url)
    expect(mode).toEqual('LOCAL')
  })

  it('returns UAT', () => {
    const url = 'http://dummy-qa.com'
    const mode = getEnvironment(url)
    expect(mode).toEqual('UAT')
  })

  it('returns DEV', () => {
    const url = 'http://dummy-dev.com'
    const mode = getEnvironment(url)
    expect(mode).toEqual('DEV')
  })

  it('returns PRD', () => {
    const url = 'http://dummy.com'
    const mode = getEnvironment(url)
    expect(mode).toEqual('PRD')
  })

  it('returns environment if url is not provided', () => {
    const mode = getEnvironment()
    expect(mode).toEqual(expect.any(String)) // different ENV could be returned in pipeline or local
  })
})
