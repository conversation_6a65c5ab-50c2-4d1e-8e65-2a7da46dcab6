import { env } from '@/core/config'
import { logger } from '@/core/utils/logger'
import { okta } from '@/core/utils/okta/oktaInstance'
import axios, { InternalAxiosRequestConfig } from 'axios'
import axiosRetry from 'axios-retry'

const instance = axios.create({
  withCredentials: !env.isOkta,
  baseURL: env.riskReportApiUrl
})

const timetravelInstance = axios.create({
  withCredentials: !env.isOkta,
  baseURL: env.riskReportTimetravelApiUrl
})

const setAuthorizationHeaders = (config: InternalAxiosRequestConfig<any>) => {
  const authorization = `Bearer ${okta?.getAccessToken()}`

  if (authorization) {
    config.headers.Authorization = authorization
  }

  return config
}

if (env.isOkta) {
  timetravelInstance.interceptors.request.use(setAuthorizationHeaders)
  instance.interceptors.request.use(setAuthorizationHeaders)
}

axiosRetry(instance, {
  retries: 10,
  retryDelay: (retryCount) => retryCount * 2000, // delays: 2000ms, 4000ms, 6000ms
  onRetry: (retryCount, error, requestConfig) =>
    logger.warn(
      `Retry fetch... count: ${retryCount} url: ${requestConfig.url}`,
      { requestConfig, retryCount },
      error
    )
})

axiosRetry(timetravelInstance, {
  retries: 3,
  retryDelay: (retryCount) => retryCount * 2000, // delays: 2000ms, 4000ms, 6000ms
  onRetry: (retryCount, error, requestConfig) =>
    logger.warn(
      `Retry (timetravel) fetch... count: ${retryCount} url: ${requestConfig.url}`,
      { requestConfig, retryCount },
      error
    )
})

export const getUiBackendClient = (isLiveMode: boolean) =>
  isLiveMode ? instance : timetravelInstance

export const getTimeTravelUiBackendClient = () => timetravelInstance
