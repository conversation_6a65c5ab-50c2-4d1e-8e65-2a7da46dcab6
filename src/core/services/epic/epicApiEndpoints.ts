import { APP_NAME, STORE_NAME } from '@/core/services/epic'

export const epicApiEndpoints = {
  getUserProfile: `/firm.epic.api/Macro/UserProfile`,
  getUsersByRoles: `/firm.epic.api/Azman/GetUsersByRole?appName=${encodeURIComponent(
    APP_NAME
  )}&storeName=${encodeURIComponent(STORE_NAME)}`,
  saveUserPreference: `/firm.epic.api/Macro/UserPreferences`,
  getUserPreference: (key: string) =>
    `/firm.epic.api/Macro/UserPreferences/${encodeURIComponent(key)}`,
  getAllPresets: (reportType: string) =>
    `/firm.epic.api/GridPreset/GetAll?appName=${encodeURIComponent(
      APP_NAME
    )}&gridName=${encodeURIComponent(reportType)}`,
  savePreset: `/firm.epic.api/GridPreset/Save`,
  sharePreset: `/firm.epic.api/GridPreset/Share`,
  deletePreset: `/firm.epic.api/GridPreset/Delete`,
  deletePresetQuery: (presetId: string) =>
    `/firm.epic.api/GridPreset/Delete?presetId=${encodeURIComponent(presetId)}`,
  sendPresetCopy: (presetId = '') =>
    presetId
      ? `/firm.epic.api/GridPreset/SendACopy?presetId=${encodeURIComponent(
          presetId
        )}`
      : `/firm.epic.api/GridPreset/SendACopy`
} as const
