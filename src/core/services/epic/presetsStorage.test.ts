import { IPresetsGroupId, getPresetsStorage } from '.'
import { vi } from 'vitest'
import { getPresetDummy, getUsersByRolesDummy } from '@/core/testing/dummies'

describe('getPresetStorage', () => {
  const userPresetDummy = getPresetDummy({
    PresetName: 'preset1'
  })
  const sharedPresetDummy = getPresetDummy({
    PresetName: 'preset2'
  })

  const presetStorage = getPresetsStorage({
    userId: '1',
    reportType: 'reportType',
    onPresetCreated: vi.fn(),
    columnInfo: [],
    getExpandedGroupsIds: vi.fn(),
    userPresets: [userPresetDummy],
    sharedPresets: [sharedPresetDummy],
    usersByRoles: getUsersByRolesDummy()
  })

  it('should return the preset storage', () => {
    expect(presetStorage).toEqual({
      loadPresetsAndGroups: expect.any(Function),
      createPreset: expect.any(Function),
      updatePreset: expect.any(Function),
      deletePreset: expect.any(Function),
      sharePreset: expect.any(Function),
      getUsers: expect.any(Function)
    })
  })

  it('should thow error on duplicate user preset', async () => {
    await expect(
      presetStorage.createPreset({
        name: userPresetDummy.PresetName,
        groupId: IPresetsGroupId.Personal
      } as any)
    ).rejects.toThrow('Preset with the same name already exists!')
  })

  it('should thow error on duplicate shared preset', async () => {
    await expect(
      presetStorage.createPreset({
        name: sharedPresetDummy.PresetName,
        groupId: IPresetsGroupId.Shared
      } as any)
    ).rejects.toThrow('Preset with the same name already exists!')
  })
})
