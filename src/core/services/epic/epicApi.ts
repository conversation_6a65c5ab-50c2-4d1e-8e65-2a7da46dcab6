import { withErrorHandling } from '@/core/utils/api/withErrorHandling'
import axios from 'axios'
import {
  epicApiEndpoints,
  IPreset,
  IEpicUserProfile,
  IUserRole,
  SERVICE_ID,
  TOKEN,
  IUsersByRole,
  IPresetToSave,
  IPresetToShare,
  IPreferencesToSave,
  IEpicUserPreference,
  IPresetToUpdate
} from '@/core/services/epic'
import { apiUrls } from '@/core/config'
import { v4 as uuidv4 } from 'uuid'
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import { logger } from '@/core/utils/logger'

const apiConfig = {
  baseUrl: apiUrls.epic,
  headers: {
    'X-EPIC-Service-ID': SERVICE_ID,
    'X-EPIC-Authorization-Token': TOKEN,
    'X-EPIC-Request-ID': uuidv4(),
    'Content-Type': 'application/json'
  }
}

const instance = axios.create({
  baseURL: apiConfig.baseUrl,
  withCredentials: true,
  headers: apiConfig.headers
})

const getErrorContext = (endpoint: string) => ({
  service: 'epicApi',
  endpoint
})

export const epicApi = {
  saveUserPreference: <T>(preferences: IPreferencesToSave<T>) =>
    instance.post<IEpicUserPreference<T>>(
      epicApiEndpoints.saveUserPreference,
      preferences
    ),
  getUserPreference: (key: string) =>
    instance.get(epicApiEndpoints.getUserPreference(key)),
  savePreset: (preset: IPresetToSave | IPresetToUpdate) =>
    instance
      .post<IPreset>(epicApiEndpoints.savePreset, preset)
      .then((res) => res.data)
      .catch((error) => {
        logger.error(
          'Error saving preset',
          getErrorContext(epicApiEndpoints.savePreset),
          error
        )
        throw error
      }),
  sharePreset: (preset: IPresetToShare) =>
    instance.post<void>(epicApiEndpoints.sharePreset, preset).catch((error) => {
      logger.error(
        'Error sharing preset',
        {
          service: 'epicApi',
          endpoint: epicApiEndpoints.sharePreset
        },
        error
      )
      throw error
    }),
  deletePreset: (presetId: string) =>
    instance
      .delete<void>(epicApiEndpoints.deletePresetQuery(presetId))
      .catch((error) => {
        logger.error(
          'Error deleting preset',
          getErrorContext(epicApiEndpoints.deletePresetQuery(presetId)),
          error
        )
        throw error
      }),
  sendPresetCopy: (presetId: string, userIds: string[]) =>
    instance
      .post<void>(epicApiEndpoints.sendPresetCopy(presetId), userIds)
      .catch((error) => {
        logger.error(
          'Error sending preset copy',
          getErrorContext(epicApiEndpoints.sendPresetCopy(presetId)),
          error
        )
        throw error
      }),
  getUserProfile: () =>
    withErrorHandling<IEpicUserProfile>(
      () => instance.get(epicApiEndpoints.getUserProfile),
      `Can't get user profile from Epic`,
      getErrorContext(epicApiEndpoints.getUserProfile)
    ),
  getUsersByRoles: () => {
    const roles = [
      IUserRole.PresetAdmin,
      IUserRole.PreviewAccess,
      IUserRole.PreviewAccessDelayed,
      IUserRole.PreviewAccessBBGOverlay,
      IUserRole.PreviewAccessBBGOverlayDelayed
    ]

    return withErrorHandling<IUsersByRole>(
      () => instance.post(epicApiEndpoints.getUsersByRoles, roles),
      `Can't get roles for these roles: ${roles}`,
      getErrorContext(epicApiEndpoints.getUsersByRoles)
    )
  },
  getAllPresets: (reportType: string) =>
    withErrorHandling<IPreset[]>(
      () => instance.get(epicApiEndpoints.getAllPresets(reportType)),
      `Can't get user presets for ${reportType} report type`,
      getErrorContext(epicApiEndpoints.getAllPresets(reportType))
    )
}

export const epicRtkQueryApi = createApi({
  reducerPath: 'epicApi',
  baseQuery: fetchBaseQuery({
    baseUrl: apiConfig.baseUrl,
    credentials: 'include',
    headers: apiConfig.headers
  }),
  endpoints: (builder) => ({
    getUserPreference: builder.query<IEpicUserPreference<string>, string>({
      query: (key: string) => epicApiEndpoints.getUserPreference(key)
    }),
    getAllPresets: builder.query<IPreset[], string>({
      query: (reportType: string) => epicApiEndpoints.getAllPresets(reportType)
    }),
    getUserProfile: builder.query<IEpicUserProfile, void>({
      query: () => epicApiEndpoints.getUserProfile
    }),
    getUsersByRoles: builder.mutation<IUsersByRole, void>({
      query: () => ({
        url: epicApiEndpoints.getUsersByRoles,
        method: 'post',
        body: [
          IUserRole.PresetAdmin,
          IUserRole.PreviewAccess,
          IUserRole.PreviewAccessBBGOverlay
        ]
      })
    })
  })
})

export type EpicApiType = ReturnType<typeof epicRtkQueryApi.reducer>
