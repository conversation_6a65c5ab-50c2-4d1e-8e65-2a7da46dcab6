import { ExecutionTimeFilterState } from '@/core/redux/features/quickFilters/executionTimeFilter/types'
import { PreProcessingReportParams } from '@/core/redux/features/report'
import { IColumnInfo } from '@/core/services/riskReport'
import {
  ColDef,
  ColGroupDef,
  ColumnState,
  GridApi
} from '@ag-grid-community/core'
import { Preset, PresetToCreate } from '@core-clib/web-components'

export interface IEpicUserProfile {
  UserId: string
  UserFullName: string
  UserFirstName: string
  UserLastName: string
  UserPreferredName: string
  UserInitials: null | string
  UserEmail: string
  TeamId: string
  OriginalTeamId: string
  ImpersonatingAs: null | string
  EffectiveFrom: string
  EffectiveTo: string
  OnBoardingDate: string
  CreatedById: string
  ModifiedDate: string
  ModifiedById: string
  TimeZoneId: null | string
  CountryCode: null | string
  EmployeeLocation: null | string
  Region: null | string
  JobTitle: null | string
  EmployeeId: string
  IsServiceAccount: boolean
  IsPm: boolean
  IsAnalyst: boolean
  IsDataAnalyst: boolean
  IsAdmin: boolean
  IsManagement: boolean
  IsExecutionTrader: boolean
  IsPreferredMap: boolean
  IsTMTProfessional: boolean
  IsSE: null | boolean
  IsXO: null | boolean
  IsDeveloper: boolean
  LastHireDate: string
  CompanyAddressCity: null | string
  CompanyAddressState: null | string
  CompanyAddressCountry: null | string
  WorkLocation: null | string
  ManagerName: null | string
  DepartmentName: null | string
  Id: string
  CreatedDate: string
  IsDeleted: boolean
}

export interface IUser {
  EmployeeFirstName: string
  EmployeeLastName: string
  ADUserID: string
}

export type IUsersByRole = Record<IUserRole, IUser[]>

/**
 * The roles were introduced to facilitate a gradual roll-out, rather than a big bang release.
 */
export enum IUserRole {
  /**
   * Allows users write-access to Shared Presets. Users without that role can read Shared Presets, and write/send Personal Presets.
   */
  PresetAdmin = 'Preset Admin',
  /**
   * Gives access to the new UI with the `PnlLive` report.
   */
  PreviewAccess = 'Preview Access',
  /**
   * Gives access to the the delayed report.
   */
  PreviewAccessDelayed = 'Preview Access (Delayed Market Data)',
  /**
   * Gives access to the `PnlLiveBbgOverlay` report.
   */
  PreviewAccessBBGOverlay = 'Preview Access (BBG Overlay Market Data)',
  /**
   * Gives access to the `PnlLiveBbgOverlay` delayed report.
   */
  PreviewAccessBBGOverlayDelayed = 'Preview Access (BBG Overlay Delayed Market Data)'
}

export enum IUserPreferenceKey {
  DefaultPresetId = 'defaultPreset', // deprecated in favor of `lastUsedPresetId`
  LastUsedPresetId = 'lastUsedPresetId',
  IsSummaryBarCollapsed = 'isSummaryBarCollapsed',
  ComponentVisibilitySidePanel = 'componentVisibility'
  // add new preference key here
}

export interface IEpicUserPreference<T> {
  Id: string
  CreatedDate: string
  IsDeleted: boolean
  UserId: string
  Key: string
  ModifiedDate: string
  ModifiedBy: string
  CreatedBy: string
  Data: T
}

export interface IComponentVisibilitySidePanel {
  isAppHeaderVisible: boolean
  isSummaryBarVisible: boolean
  isFooterBarVisible: boolean
  isGroupingBarVisible: boolean
  isFloatingFiltersBarVisible: boolean
}

export interface IUserPreferences {
  [IUserPreferenceKey.DefaultPresetId]?: string // deprecated - use `lastUsedPresetId` instead
  lastUsedPresetId?: string
  isSummaryBarCollapsed?: boolean
  preProcessingReportParams?: PreProcessingReportParams
  componentVisibility: IComponentVisibilitySidePanel
  // add more preferences as needed
}

// Type guard
export const isEpicUserPreference = (
  obj: unknown
): obj is IEpicUserPreference<IUserPreferences> =>
  typeof obj === 'object' &&
  obj !== null &&
  Object.prototype.hasOwnProperty.call(obj, 'Key') &&
  Object.prototype.hasOwnProperty.call(obj, 'Data')

export interface IPreferencesToSave<T> {
  Key: string
  Data: T
}

// TODO: https://prdjira.saccap.int/browse/MACROUIUX-1221
//  `AgGridState` should be imported from `@core-clib/shared-types`
export interface AgGridState {
  columnState: ColumnState[]
  filterModel: {
    [key: string]: unknown
  }
  /**
   * Header names are included only if `AgGridStateManagerConfig.storeHeaderNames` is `true`
   */
  headerNames?: HeaderName[]
}

export type ClibPreset = Preset<IPresetState>
export type ClibPresetToCreate = PresetToCreate<IPresetState>

export interface HeaderName {
  colId: string
  name: string
}

export interface IPreset {
  Id: string
  IsDeleted: boolean
  PresetName: string
  SharedWith: IPresetShare[] | null
  Data: ClibPreset
}

export interface IPresetShare {
  AccessType: IPresetShareAccessType
  Type: IPresetShareType
  Names: string[]
}

export enum IPresetShareType {
  ADGroup = 'ADGroup',
  AZManRole = 'AZManRole',
  User = 'User',
  Public = 'Public'
}

export enum IPresetsGroupId {
  Shared = 'shared',
  Personal = 'personal'
}

export enum IPresetsGroupName {
  SharedPresets = 'Shared Presets',
  MyPresets = 'My Presets'
}

export enum IPresetShareAccessType {
  Read = 'Read',
  Write = 'Write'
}

export interface IPresetState extends AgGridState {
  expandedGroupIds?: string[]
  executionTimeFilter?: ExecutionTimeFilterState
  isPivotMode?: boolean
  pivotColumns?: string[]
  pivotResultsColumns?: (ColDef | ColGroupDef)[]
  valueColumns?: string[]
}

export interface IPresetToSave {
  AppName: string
  GridName: string
  PresetName: string
  PresetData: ClibPresetToCreate
}

export interface IPresetToUpdate extends IPresetToSave {
  Id: string
}

export interface IGetPresetToSavePayload {
  preset: ClibPresetToCreate
  columnInfo: IColumnInfo[]
  expandedGroupsIds: string[]
  reportName: string
}

export interface IGetPresetToUpdatePayload extends IGetPresetToSavePayload {
  id: string
}

export interface IPresetToShare {
  Id: string
  Shares: IPresetShare[]
}

export interface PresetsStorageParams {
  gridApi?: GridApi
  userId: string
  onPresetCreated: (preset: IPreset) => void
  onPresetDeleted: (presetId: string) => void
  onPresetUpdated: (preset: IPresetToUpdate) => void
  reportType: string
  columnInfo: IColumnInfo[]
  getExpandedGroupsIds: () => string[]
  userPresets: IPreset[]
  sharedPresets: IPreset[]
  usersByRoles: IUsersByRole
  getExecutionTimeFilter: () => ExecutionTimeFilterState | undefined
  getPivotColumns: () => string[]
  getPivotResultsColumns: () => (ColDef | ColGroupDef)[]
  getValueColumns: () => string[]
  isPivotMode: () => boolean | undefined
}
