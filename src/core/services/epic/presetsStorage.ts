import {
  APP_NAME,
  epicApi,
  IPreset,
  IPresetsGroupId,
  IPresetsGroupName,
  IPresetState,
  IPresetToSave,
  IPresetToUpdate,
  IUserRole,
  PresetsStorageParams
} from '@/core/services/epic'
import { logger } from '@/core/utils/logger'
import {
  clearClibPresetsFromSystemData,
  customizePreset,
  filterOutSystemDataColumns,
  getPresetToSharePayload,
  hasAdminRole,
  isUserUnique,
  mapPresetToClibPreset,
  mapPresetToClibSharedPreset,
  mapUserToPresetUser,
  sortUsersAlphabetically
} from '@/core/utils/presets'
import {
  danger,
  PresetsStorage,
  PresetToCreate,
  success
} from '@core-clib/web-components'

export const getPresetsStorage = ({
  gridApi,
  userId,
  reportType,
  onPresetCreated,
  onPresetDeleted,
  onPresetUpdated,
  columnInfo,
  getExpandedGroupsIds,
  userPresets,
  sharedPresets,
  usersByRoles,
  getExecutionTimeFilter,
  getPivotColumns,
  getPivotResultsColumns,
  getValueColumns,
  isPivotMode
}: PresetsStorageParams): PresetsStorage<IPresetState> => {
  const isDuplicatePreset = (preset: PresetToCreate<IPresetState>) => {
    const presetName = preset.name.toLowerCase()
    const isDuplicate = (preset: IPreset) =>
      preset.PresetName.toLowerCase() === presetName

    return preset.groupId === IPresetsGroupId.Shared
      ? sharedPresets.some(isDuplicate)
      : userPresets.some(isDuplicate)
  }

  // It is crutch for specific case when we need to throttle updatePreset method
  // Should be rethink later
  let isBusy = false

  return {
    loadPresetsAndGroups: async () => {
      const clibUserPresets = userPresets.map(mapPresetToClibPreset)
      const clibSharedPresets = sharedPresets.map(mapPresetToClibSharedPreset)

      const presets = clearClibPresetsFromSystemData(
        [...clibUserPresets, ...clibSharedPresets],
        columnInfo
      )

      const isAdmin = hasAdminRole(userId, usersByRoles)

      const groups = [
        {
          id: IPresetsGroupId.Shared,
          name: IPresetsGroupName.SharedPresets,
          readOnly: !isAdmin,
          showIfEmpty: true
        },
        {
          id: IPresetsGroupId.Personal,
          name: IPresetsGroupName.MyPresets,
          readOnly: false,
          showIfEmpty: true,
          allowSharing: true
        }
      ]

      return {
        presets,
        groups
      }
    },
    createPreset: async (preset) => {
      if (isDuplicatePreset(preset)) {
        const message = 'Preset with the same name already exists!'
        danger(message)
        throw Error(message)
      }

      try {
        const columnState = filterOutSystemDataColumns(
          preset.state.columnState,
          columnInfo
        )

        const presetData = customizePreset(preset, {
          columnState,
          expandedGroupIds: getExpandedGroupsIds(),
          executionTimeFilter: getExecutionTimeFilter(),
          pivotColumns: getPivotColumns(),
          pivotResultsColumns: getPivotResultsColumns(),
          valueColumns: getValueColumns(),
          isPivotMode: isPivotMode()
        })

        const presetToSave: IPresetToSave = {
          AppName: APP_NAME,
          GridName: reportType,
          PresetName: preset.name,
          PresetData: presetData
        }

        const savedPreset = await epicApi.savePreset(presetToSave)

        if (preset.groupId === IPresetsGroupId.Shared) {
          const presetToShare = getPresetToSharePayload(savedPreset.Id)
          await epicApi.sharePreset(presetToShare)
        }

        onPresetCreated(savedPreset)

        return {
          ...savedPreset.Data,
          id: savedPreset.Id
        }
      } catch (error: unknown) {
        logger.error(
          `Unable to Create a Preset using createPreset method. Requested Preset details are provided with the error information: `,
          { function: 'getPresetsStorage.createPreset', preset },
          error as Error
        )
        throw Error('Error while saving preset')
      }
    },
    updatePreset: async (id, updatedValues, originalPreset) => {
      if (!isBusy) {
        isBusy = true
        try {
          const preset = { ...originalPreset, ...updatedValues }
          const columnDefs = gridApi?.getColumnDefs() ?? []

          const columnState = filterOutSystemDataColumns(
            preset.state.columnState,
            columnInfo
          )

          preset.state.headerNames = columnDefs.map((column: any) => ({
            colId: column.colId,
            name: column.headerName
          }))

          const presetData = customizePreset(preset, {
            columnState,
            expandedGroupIds: getExpandedGroupsIds(),
            executionTimeFilter: getExecutionTimeFilter(),
            pivotColumns: getPivotColumns(),
            pivotResultsColumns: getPivotResultsColumns(),
            valueColumns: getValueColumns(),
            isPivotMode: isPivotMode()
          })

          const presetToUpdate: IPresetToUpdate = {
            Id: id,
            AppName: APP_NAME,
            GridName: reportType,
            PresetName: preset.name,
            PresetData: presetData
          }

          await epicApi.savePreset(presetToUpdate)
          onPresetUpdated(presetToUpdate)

          return { id, ...presetData }
        } catch (error: unknown) {
          logger.error(
            `Unable to update a preset using updatePreset method for the given Preset ID of ${id}. Updated Values and OriginalPreset details are provided with the error information: `,
            {
              function: 'getPresetsStorage.updatePreset',
              updatedValues,
              originalPreset
            },
            error as Error
          )
          throw Error('Error while updating preset')
        } finally {
          isBusy = false
        }
      }

      return Promise.reject('Update in progress')
    },
    deletePreset: async (id) => {
      try {
        await epicApi.deletePreset(id)
        onPresetDeleted(id)
      } catch (error: unknown) {
        logger.error(
          `Unable to Delete a Preset using deletePreset method for the given Preset ID of ${id} with the provided error information: `,
          { function: 'getPresetsStorage.deletePreset' },
          error as Error
        )
        throw Error('Error while deleting preset')
      }
    },
    getUsers: async () => {
      const usersWithPreviewAccess = usersByRoles[IUserRole.PreviewAccess]
      const usersWithPreviewAccessDelayed =
        usersByRoles[IUserRole.PreviewAccessDelayed]
      const usersWithPreviewAccessBBGOverlay =
        usersByRoles[IUserRole.PreviewAccessBBGOverlay]
      const usersWithPreviewAccessBBGOverlayDelayed =
        usersByRoles[IUserRole.PreviewAccessBBGOverlayDelayed]

      return [
        ...usersWithPreviewAccess,
        ...usersWithPreviewAccessDelayed,
        ...usersWithPreviewAccessBBGOverlay,
        ...usersWithPreviewAccessBBGOverlayDelayed
      ]
        .map(mapUserToPresetUser)
        .filter(isUserUnique)
        .sort(sortUsersAlphabetically)
    },
    /**
     * Share preset between specific users.
     * The method name is SDK specific.
     * It is used by send a copy functionality.
     */
    sharePreset: async (preset, userIds) => {
      try {
        await epicApi.sendPresetCopy(preset.id, userIds)
        const successMessage = `Successfully sent preset ${
          preset.name
        } to user ${userIds.join(
          '/'
        )}, ask them to refresh their application (F5) to see the preset `
        logger.info(successMessage)
        success(successMessage)
      } catch (error: unknown) {
        const errorMessage = `Error while trying to send preset ${
          preset.name
        } to user ${userIds.join(
          '/'
        )}, contact M72 Support with this error message: ${error}`
        logger.error(errorMessage, {
          function: 'getPresetsStorage.sharePreset',
          error
        })
        danger(errorMessage)
        throw Error('Error while sharing preset')
      }
    }
  }
}
