import { useAppSelect } from '@/core/redux/hooks'
import { datadogLogs } from '@datadog/browser-logs'
import { datadogRum } from '@datadog/browser-rum'
import { useCallback, useEffect } from 'react'

export const useDatadogUserRegistrationEffect = () => {
  const userProfile = useAppSelect((state) => state.users.profile)

  // Create a memoized callback function to avoid
  // creating a new closure every time the component renders
  const handleUserProfileChange = useCallback(() => {
    if (userProfile === null) {
      datadogRum.clearUser()
      datadogLogs.clearUser()
    } else {
      const user = {
        id: userProfile.UserId,
        name: `${userProfile.UserLastName}, ${userProfile.UserFirstName}`,
        email: userProfile.UserEmail
      }

      datadogRum.setUser(user)
      datadogLogs.setUser(user)
    }
  }, [userProfile])

  useEffect(() => {
    handleUserProfileChange()
  }, [userProfile])
}
