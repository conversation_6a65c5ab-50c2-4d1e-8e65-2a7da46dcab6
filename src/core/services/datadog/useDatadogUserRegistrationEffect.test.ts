import { datadogLogs } from '@datadog/browser-logs'
import { datadogRum } from '@datadog/browser-rum'
import { vi } from 'vitest'
import { useDatadogUserRegistrationEffect } from '.'
import { getReduxWrapper, renderHookSimply } from '@/core/testing'

vi.mock('@datadog/browser-rum')
vi.mock('@datadog/browser-logs')

describe('useDatadogRumUserRegistrationEffect', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('test datadogRUM', () => {
    const setUser = vi.fn()
    const clearUser = vi.fn()

    vi.spyOn(datadogRum, 'clearUser').mockImplementation(clearUser)
    vi.spyOn(datadogRum, 'setUser').mockImplementation(setUser)

    it('should clear user when user is null', () => {
      const { wrapper } = getReduxWrapper({
        users: {
          profile: null
        }
      })

      renderHookSimply(() => useDatadogUserRegistrationEffect(), wrapper)

      expect(clearUser).toHaveBeenCalled()
      expect(setUser).not.toHaveBeenCalled()
    })

    it('should set user when user is not null', () => {
      const { wrapper } = getReduxWrapper({
        users: {
          profile: {
            UserId: 'asd',
            UserFullName: 'John Doe',
            UserFirstName: 'John',
            UserLastName: 'Doe',
            UserEmail: '<EMAIL>'
          }
        }
      })

      renderHookSimply(() => useDatadogUserRegistrationEffect(), wrapper)

      expect(clearUser).not.toHaveBeenCalled()
      expect(setUser).toHaveBeenCalledWith({
        id: 'asd',
        name: 'Doe, John',
        email: '<EMAIL>'
      })
    })
  })

  describe('test datadogLogs', () => {
    const setUser = vi.fn()
    const clearUser = vi.fn()

    vi.spyOn(datadogLogs, 'clearUser').mockImplementation(clearUser)
    vi.spyOn(datadogLogs, 'setUser').mockImplementation(setUser)

    it('should clear user when user is null', () => {
      const { wrapper } = getReduxWrapper({
        users: {
          profile: null
        }
      })

      renderHookSimply(() => useDatadogUserRegistrationEffect(), wrapper)

      expect(clearUser).toHaveBeenCalled()
      expect(setUser).not.toHaveBeenCalled()
    })

    it('should set user when user is not null', () => {
      const { wrapper } = getReduxWrapper({
        users: {
          profile: {
            UserId: 'asd',
            UserFullName: 'John Doe',
            UserFirstName: 'John',
            UserLastName: 'Doe',
            UserEmail: '<EMAIL>'
          }
        }
      })

      renderHookSimply(() => useDatadogUserRegistrationEffect(), wrapper)

      expect(clearUser).not.toHaveBeenCalled()
      expect(setUser).toHaveBeenCalledWith({
        id: 'asd',
        name: 'Doe, John',
        email: '<EMAIL>'
      })
    })
  })
})
