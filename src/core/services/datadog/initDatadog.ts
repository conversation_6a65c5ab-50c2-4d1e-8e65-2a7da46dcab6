import { app_version as version } from '@/../pipeline.json'
import { env } from '@/core/config'
import { datadogLogs } from '@datadog/browser-logs'
import { datadogRum } from '@datadog/browser-rum'

// https://docs.datadoghq.com/real_user_monitoring/
export const initDatadog = () => {
  if (datadogRum.getInitConfiguration() && datadogLogs.getInitConfiguration()) {
    return
  }

  const commonConfig = {
    clientToken: 'pubb5f556233b5d1609576568c79f620c67',
    site: 'datadoghq.com',
    sessionSampleRate: 100, // Track 100% of user sessions (see https://docs.datadoghq.com/real_user_monitoring/guide/sampling-browser-plans/)
    service: 'm72ui',
    version,
    env: env.mode.toLowerCase()
  }

  datadogRum.init({
    ...commonConfig,
    applicationId: '8a361d42-4e63-4fe2-98c8-40aeadfa30dd',
    sessionReplaySampleRate: 100, // Record replay session for 100% of tracked user sessions (see https://docs.datadoghq.com/real_user_monitoring/guide/sampling-browser-plans/)
    trackUserInteractions: true,
    trackResources: true,
    trackLongTasks: true,
    //Mask all data recorded in replay sessions to begin with. If this proves unusable, review this setting and/or selectively set the data privacy attributes on individual HTML elements as described here: https://docs.datadoghq.com/real_user_monitoring/session_replay/privacy_options#override-an-html-element
    defaultPrivacyLevel: 'mask'
  })

  datadogLogs.init({
    ...commonConfig,
    forwardErrorsToLogs: true
  })
}
