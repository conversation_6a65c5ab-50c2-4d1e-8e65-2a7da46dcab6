import { datadogRum } from '@datadog/browser-rum'
import { appConfig } from '@/core/config'
import { logger } from '@/core/utils/logger'

export const measureMemory = () => {
  // `window.performance.memory` object is available only in Chromium-based browsers.
  if (performance.memory) {
    const { jsHeapSizeLimit, totalJSHeapSize, usedJSHeapSize } =
      performance.memory
    const usagePct = (usedJSHeapSize / jsHeapSizeLimit) * 100

    const memoryObj = {
      heap: {
        MaxJsHeapSizeBytes: jsHeapSizeLimit,
        TotalJsHeapSizeBytes: totalJSHeapSize,
        UsedJSHeapSizeBytes: usedJSHeapSize,
        JsHeapUsagePercent: usagePct
      }
    }

    datadogRum.addAction('measure memory', memoryObj)
    logger.info('measure memory', memoryObj)
  }
}

export const initMemoryMeasurement = () =>
  setInterval(measureMemory, appConfig.memoryMeasurementIntervalMs)
