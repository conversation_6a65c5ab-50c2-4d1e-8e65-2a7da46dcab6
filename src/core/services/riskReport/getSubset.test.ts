import { api<PERSON><PERSON>s, create<PERSON><PERSON><PERSON>, setupMockServer } from '@/core/testing'
import { ISubsetRequest } from '@/core/services/riskReport'
import { getSubsetResponseDummy } from '@/core/testing/dummies'
import { getRiskReportApi } from './riskReportApi'
import { vi } from 'vitest'
import { logger } from '@/core/utils/logger'

const server = setupMockServer()

vi.mock('@/core/utils/logger', () => ({
  logger: {
    error: vi.fn(),
    warn: vi.fn()
  }
}))

const riskReportApi = getRiskReportApi(true)

describe('getSubset', () => {
  it('should return a resolved promise with data when response is ok', async () => {
    // given
    server.use(
      createHandler({
        method: 'post',
        path: apiPaths.getSubset,
        response: getSubsetResponseDummy()
      })
    )

    // when
    const response = await riskReportApi.getSubset(
      'testReport',
      {} as ISubsetRequest
    )

    // then
    expect(response).toEqual({
      data: {
        WellKnownType: 'Table',
        Columns: [
          {
            Name: 'Team',
            Type: 'String',
            Count: 2,
            ContainsNulls: false,
            Values: ['ARNO', 'ARNO']
          },
          {
            Name: 'TradingArea',
            Type: 'String',
            Count: 2,
            ContainsNulls: false,
            Values: ['ARNO', 'ESPO']
          },
          {
            ContainsNulls: false,
            Count: 2,
            Name: 'ExecDateTimeUtc',
            Type: 'String',
            Values: [
              '2024-08-16T13:27:15.0000000Z',
              '2024-08-17T10:00:10.0000000Z'
            ]
          }
        ]
      },
      totalRowCount: 2
    })
  })

  it('should return a rejected promise with error text when response is not ok', async () => {
    // given
    server.use(
      createHandler({
        method: 'post',
        path: apiPaths.getSubset,
        response: 'Error occurred.',
        status: 400
      })
    )

    // expect error thrown
    await expect(
      riskReportApi.getSubset('testReport', {} as ISubsetRequest)
    ).rejects.toThrow('Request failed with status code 400')
  })
})
