import { getErrorResponseHeader } from '..'

describe('getResponseHeader', () => {
  it('should return axiosError and has a response header', () => {
    // given
    const error = {
      isAxiosError: true,
      response: {
        headers: {
          'Retry-After': '120'
        }
      }
    }

    // when
    const result = getErrorResponseHeader('Retry-After')(error)

    // then
    expect(result).toBe('120')
  })

  it('should be case insensitive', () => {
    // given

    // when
    const result = getErrorResponseHeader('retry-after')({
      isAxiosError: true,
      response: {
        headers: {
          'Retry-After': '11'
        }
      }
    })

    // then
    expect(result).toBe('11')

    // when
    const result2 = getErrorResponseHeader('retry-after')({
      isAxiosError: true,
      response: {
        headers: {
          'retry-after': '22'
        }
      }
    })

    // then
    expect(result2).toBe('22')

    // when
    const result3 = getErrorResponseHeader('Retry-After')({
      isAxiosError: true,
      response: {
        headers: {
          'retry-after': '33'
        }
      }
    })

    // then
    expect(result3).toBe('33')
  })

  it('should return undefined if the error is not an AxiosError or does not have a response', () => {
    // given
    const error = {
      isAxiosError: false,
      response: null
    }

    // when
    const result = getErrorResponseHeader('Retry-After')(error)

    // then
    expect(result).toBeUndefined()
  })
})
