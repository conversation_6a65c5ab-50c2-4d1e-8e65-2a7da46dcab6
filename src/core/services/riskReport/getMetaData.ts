import { riskReportApiEndpoints } from '@/core/services/riskReport/apiEndpoints'
import { getUiBackendClient } from '../http/uiBackendClient'

type GetMetaDataRequest = {
  reportName: string
  asOfUtc: string
  isLiveModeEnabled?: boolean
}

export type MetadataResponse = {
  executionTimeUtc: string
  tradeSnapshotTimeUtc: string
  marketSnapshotTimeUtc: string
  metadata: Record<string, string>
}

export const getMetaData = ({
  reportName,
  asOfUtc,
  isLiveModeEnabled
}: GetMetaDataRequest) => {
  const uiBackendClient = getUiBackendClient(isLiveModeEnabled ?? true)

  // TODO: check if this is still useful, it is called once from glue, and drillthrough report should get metadata from event

  return uiBackendClient.post<MetadataResponse>(
    riskReportApiEndpoints.GetMetadata(reportName),
    {
      snapshot: {
        asOfUtc
      }
    }
  )
}
