import { apiUrls } from '@/core/config'
import { ILatestAsOfResponse } from '@/core/services/riskReport'
import { logger } from '@/core/utils/logger'
import { AxiosError, isAxiosError } from 'axios'
import { getUiBackendClient } from '../http/uiBackendClient'
import { riskReportApiEndpoints } from './apiEndpoints'

type LatestAsOfUtcRequestParams = {
  reportName: string
  currentAsOfUtc?: string
  signal?: AbortSignal
  isLiveModeEnabled?: boolean
}

export const getLatestAsOfUtcErrorRetryDelay = (
  error: unknown
): number | undefined =>
  Number(getErrorResponseHeader('Retry-After')(error)) || undefined

export const getErrorResponseHeader =
  (headerName: string) => (error: unknown) => {
    if (isAxiosError(error) && error.response) {
      const { headers } = error.response
      return Object.entries(headers).find(
        ([key]) => key.toLowerCase() === headerName.toLowerCase()
      )?.[1]
    } else {
      return undefined
    }
  }

export const getLatestAsOfUtcErrorStatusCode = (
  error: unknown
): number | undefined => {
  if (isAxiosError(error) && error.response) {
    return error.response.status
  }
}

const fetchLatestAsOfUtc = ({
  reportName,
  currentAsOfUtc,
  signal,
  isLiveModeEnabled
}: LatestAsOfUtcRequestParams) => {
  const isLive = isLiveModeEnabled === undefined ? true : isLiveModeEnabled
  const url = getUrl(reportName, currentAsOfUtc, isLive)

  return getUiBackendClient(isLive).get<ILatestAsOfResponse>(url, {
    signal
  })
}

export const getLatestAsOfUtc = async ({
  reportName,
  currentAsOfUtc,
  signal,
  isLiveModeEnabled
}: LatestAsOfUtcRequestParams) => {
  try {
    const response = await fetchLatestAsOfUtc({
      reportName,
      currentAsOfUtc,
      signal,
      isLiveModeEnabled
    })

    const {
      data: { latestAsOfUtc }
    } = response

    logger.debug(
      `asOfUtc Data based on LatestAsOfUtc call incase of isDrillThroughView is not set`,
      {
        latestAsOfUtc,
        currentAsOfUtc,
        newCurrentAsOfUtc: latestAsOfUtc ?? currentAsOfUtc
      }
    )

    return {
      latestAsOfUtc: latestAsOfUtc ?? currentAsOfUtc
    } as ILatestAsOfResponse
  } catch (error: unknown) {
    const shouldBeLogged =
      !isAxiosError(error) ||
      (isAxiosError(error) &&
        (error as AxiosError).code !== AxiosError.ERR_CANCELED)

    if (shouldBeLogged) {
      logger.error(
        `Failed to fetch latest as of UTC date using getLatestAsOfUtc method for the given Report Name of ${reportName} and Current As of UTC of ${currentAsOfUtc} with the provided error information: `,
        {
          service: 'riskReport',
          function: 'getLatestAsOfUtc',
          endpoint: getUrl(reportName, currentAsOfUtc, isLiveModeEnabled)
        },
        error as Error
      )
    }

    return Promise.reject(error)
  }
}

export const getUrl = (
  reportName: string,
  currentAsOf?: string,
  isLiveModeEnabled = true
) => {
  const reportURL = isLiveModeEnabled
    ? apiUrls.riskReport
    : apiUrls.riskReportTimetravel

  const url = `${reportURL}${riskReportApiEndpoints.LatestAsOf(
    reportName,
    currentAsOf ? `?laterThan=${encodeURIComponent(currentAsOf)}` : undefined
  )}`
  logger.debug(`Report Name and Current As of Dates used to generate URL for`, {
    reportName,
    currentAsOf,
    url
  })
  return url
}
