export const createEndpoint =
  (path: string) =>
  (reportName: string, query = '') =>
    `/api/RiskReports/${encodeURIComponent(reportName)}${path}${query}` as const

/**
 * API endpoints.
 */
export const riskReportApiEndpoints = {
  /**
   * GET list of all risk report names
   */
  RiskReports: createEndpoint(''),
  /**
   * GET the latest available AsOf UTC timestamp, for usage in subsequent POST requests
   */
  LatestAsOf: createEndpoint('/LatestAsOf'),
  getReportDetails: createEndpoint('/ReportDetails'),
  /**
   * POST
   * Get metadata associated with report snapshot
   */
  GetMetadata: createEndpoint('/GetMetadata'),
  /**
   * POST
   * Get trading areas
   */
  GetTradingAreas: createEndpoint('/GetDistinctColumnValues'),
  /**
   * POST
   * Get distinct column values
   */
  GetDistinctColumnValues: createEndpoint('/GetDistinctColumnValues'),
  /**
   * POST
   * Get a filtered, aggregated and sorted subset of the report
   */
  GetSubset: createEndpoint('/GetSubset'),
  /**
   * POST
   * Get a summary of risk reports
   */
  GetSummary: createEndpoint('/GetSummary'),
  /**
   * GET
   * Get list of all available knowledge UTC timestamp, for usage in subsequent POST requests
   */
  GetKnowledgeDatesAsOf: createEndpoint('/KnowledgeDatesAsOf')
}
