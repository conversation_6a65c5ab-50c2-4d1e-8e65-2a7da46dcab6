import { PreProcessingParams } from '@/core/redux/features/report/types'

export interface ILatestAsOfResponse {
  latestAsOfUtc: string
  originatingReportLatestAsOfUtc?: string
}

export interface Snapshot {
  asOfUtc: string
}

// TODO : on the backend add 'DateTime' type
export type FilterType = 'Text' | 'TextSet' | 'Date' | 'Number'

export enum ColumnType {
  Boolean = 'Boolean',
  String = 'String',
  Date = 'Date',
  DateTime = 'DateTime',
  Double = 'Double'
}
export type ValueType = string | number | boolean

export enum FilterOperators {
  Equals = 'Equals',
  NotEquals = 'NotEquals',
  Contains = 'Contains',
  StartsWith = 'StartsWith',
  EndsWith = 'EndsWith',
  GreaterThan = 'GreaterThan',
  LessThan = 'LessThan',
  In = 'In',
  Null = 'Null',
  NotNull = 'NotNull',
  NotContains = 'NotContains',
  InRange = 'InRange',
  LessThanOrEquals = 'LessThanOrEquals',
  GreaterThanOrEquals = 'GreaterThanOrEquals'
}

export interface IColumn {
  Name: string
  Type: ColumnType
  Count: number
  ContainsNulls: boolean
  Values: ValueType[]
}

export interface PreProcessingParamValueSpec {
  value: string
  displayValue: string
  shortDescription?: string
}

export enum IPreProcessingParamName {
  RepoNetting = 'RepoNetting'
}

export interface PreProcessingParamSpec {
  paramName: IPreProcessingParamName
  displayLabel: string
  paramValues: PreProcessingParamValueSpec[]
}

export interface IGetReportDetailsResponse {
  columnInfo: IColumnInfo[]
  preProcessingParamSpecs?: PreProcessingParamSpec[]
  reportType: string
  publishSyncData: boolean
  eodReportType?: string
}

export interface NumberFormat {
  decimalDigits?: number
  significantFigures?: number
  multiplier: number
  prefix?: string
  suffix?: string
}

export interface IColumnInfo {
  name?: string
  type: ColumnType
  column: string
  aggregationOperations?: string[]
  filterType?: FilterType
  canGroupBy: boolean
  isPrimaryKey: boolean
  numberFormat?: NumberFormat
  shortDescription?: string
  isSystemData: boolean
  canPivotBy: boolean
  children?: IColumnInfo[]
}

export interface IMetaDataResponse {
  metadata: Record<string, string | number>
  displayMetadata: string[]
  synchronizedReportAsOfUtcs: Record<string, string>
}

export interface IMetaDataRequest {
  snapshot: Snapshot
  preProcessingParams?: PreProcessingParams
}

export interface IFilterModel {
  column: string
  logicalOperator: ILogicalFilterOperator
  filters: IFilter[]
}

export type ILogicalFilterOperator = 'Or' | 'And'

export interface IFilter {
  operator: FilterOperators
  value: string
}

export interface IDistinctColumnValuesRequest {
  snapshot: Snapshot
  tradingAreas: string[]
  filters: IFilterModel[]
  columns: string[]
  preProcessingParams?: PreProcessingParams
}

export type IDistinctColumnValues = Record<
  string,
  (string | number | boolean | null)[]
>

export interface IDistinctColumnValuesResponse {
  distinctColumnValues: IDistinctColumnValues
}

export interface IGetTradingAreasResponse {
  distinctColumnValues: {
    TradingArea: string[]
  }
}

export type ColumnStatus = Record<
  string,
  {
    status: 'Ok' | 'Warning' | 'Error'
    message?: string
  }
>

export interface ISubsetResponse {
  data: {
    WellKnownType: 'Table'
    Columns: IColumn[]
  }
  totalRowCount: number
  pivotResultColumns?: string[]
  columnStatus?: ColumnStatus
}

export interface IRowRange {
  rowOffset: number
  rowCount: number
}

/**
 * It reflects the column name and the corresponding aggregation operation type.
 * Eg.:
 * {
 *   Team: 'UniqueValue',
 *   TradingArea: 'UniqueValue',
 *   TickerNetBoxedTrades: 'CommonPrefix',
 *   ProfitLossTradingUSD: 'Sum',
 *   FxCrossDelta: 'SumByKey(FxCross)',
 *   Description: 'CommonPrefix'
 * }
 */
export type IColumnsAggregation = Record<string, string>

export interface ISortModel {
  column: string
  direction: 'asc' | 'desc'
}

export interface ITableSubsetRequest extends IRowRange {
  aggregationColumns: IColumnsAggregation
  sortBy: ISortModel[]
  groupingColumns: string[]
  groupingKey: string[]
  filters: IFilterModel[]
  pivotMode: boolean
  pivotResultFieldSeparator?: string
  pivotColumns?: string[]
}

export interface ISubsetRequest {
  snapshot: Snapshot
  tradingAreas: string[]
  tableSubsetRequest: ITableSubsetRequest
  preProcessingParams?: PreProcessingParams
}

export interface ISummaryRequest {
  snapshot: Snapshot
  tradingAreas: string[]
  preProcessingParams?: PreProcessingParams
}

export type ISummaryHeadlineValues = Record<string, number>

export interface ISummaryResponse {
  headlineValues: ISummaryHeadlineValues
}

export interface KnowledgeDatesAsOfRequest {
  asOf: string
}

export interface KnowledgeDatesAsOfResponse {
  knowledgeAsOfDates: string[]
}
