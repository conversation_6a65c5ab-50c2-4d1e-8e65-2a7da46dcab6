import { apiUrls } from '@/core/config'
import {
  getLatestAsOfUtc,
  getLatestAsOfUtcErrorRetryDelay,
  getLatestAsOfUtcErrorStatusCode,
  getUrl
} from './getLatestAsOfUtc'
import {
  apiPaths,
  create<PERSON><PERSON><PERSON>,
  handlers,
  setupMockServer
} from '@/core/testing'

describe('getLatestAsOfUtcErrorRetryDelay', () => {
  it('should return the Retry-After header value as a number if the error is an AxiosError and has a response', () => {
    // given
    const error = {
      isAxiosError: true,
      response: {
        headers: {
          'Retry-After': '120'
        }
      }
    }

    // when
    const result = getLatestAsOfUtcErrorRetryDelay(error)

    // then
    expect(result).toEqual(120)
  })

  it('should return undefined if the error is not an AxiosError or does not have a response', () => {
    // given
    const error = {
      isAxiosError: false,
      response: null
    }

    // when
    const result = getLatestAsOfUtcErrorRetryDelay(error)

    // then
    expect(result).toBeUndefined()
  })
})

describe('getLatestAsOfUtcErrorStatusCode', () => {
  it('should return the status code if the error is an AxiosError and has a response', () => {
    // given
    const error = {
      isAxiosError: true,
      response: {
        status: 404
      }
    }

    // when
    const result = getLatestAsOfUtcErrorStatusCode(error)

    // then
    expect(result).toEqual(404)
  })

  it('should return undefined if the error is not an AxiosError or does not have a response', () => {
    // given
    const error = {
      isAxiosError: false,
      response: null
    }

    // when
    const result = getLatestAsOfUtcErrorStatusCode(error)

    // then
    expect(result).toBeUndefined()
  })
})

describe('getUrl', () => {
  it('should return a string URL with the correct format and parameters', () => {
    const reportName = 'exampleReport'
    const laterThan = '2022-01-01'
    const expectedUrl = `${
      apiUrls.riskReport
    }/api/RiskReports/${reportName}/LatestAsOf?laterThan=${encodeURIComponent(
      laterThan
    )}`

    const result = getUrl(reportName, laterThan)

    expect(result).toBe(expectedUrl)
  })

  it('should return a string URL with the correct format and parameters when given empty strings as inputs', () => {
    const reportName = 'exampleReport'
    const expectedUrl = `${apiUrls.riskReport}/api/RiskReports/${reportName}/LatestAsOf`

    const result = getUrl(reportName)

    expect(result).toBe(expectedUrl)
  })
})

describe('getLatestAsOfUtc', () => {
  const server = setupMockServer(handlers.latestAsOf)

  const currentAsOfUtc = '2022-01-01'
  const reportName = 'exampleReport'
  const handlerBody = {
    method: 'get',
    path: apiPaths.latestAsOf,
    response: {
      latestAsOfUtc: '2099-09-19'
    },
    status: 200
  } as const

  it('should return latestAsOf', async () => {
    server.use(createHandler(handlerBody))
    const result = await getLatestAsOfUtc({
      reportName,
      currentAsOfUtc,
      signal: new AbortController().signal
    })

    expect(result.latestAsOfUtc).toBe('2099-09-19')
    expect(result.originatingReportLatestAsOfUtc).toBeUndefined()
  })

  it('should return currentAsOfUtc when latestAsOfUtc is not changed', async () => {
    server.use(
      createHandler({
        ...handlerBody,
        status: 204,
        response: undefined
      })
    )

    const result = await getLatestAsOfUtc({
      reportName,
      currentAsOfUtc,
      signal: new AbortController().signal
    })

    expect(result.latestAsOfUtc).toBe(currentAsOfUtc)
  })
})
