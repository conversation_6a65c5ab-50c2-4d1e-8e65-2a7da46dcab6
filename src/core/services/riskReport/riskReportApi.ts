import { withErrorHandling } from '@/core/utils/api/withErrorHandling'
import {
  getTimeTravelUiBackendClient,
  getUiBackendClient
} from '@/core/services/http/uiBackendClient'
import { riskReportApiEndpoints as apiEndpoints } from './apiEndpoints'
import {
  IDistinctColumnValuesRequest,
  IDistinctColumnValuesResponse,
  IGetReportDetailsResponse,
  IGetTradingAreasResponse,
  IMetaDataRequest,
  IMetaDataResponse,
  ISubsetRequest,
  ISubsetResponse,
  ISummaryRequest,
  ISummaryResponse,
  KnowledgeDatesAsOfRequest,
  KnowledgeDatesAsOfResponse
} from './types'

export const getRiskReportApi = (isLiveMode: boolean) => {
  const uiBackendClient = getUiBackendClient(isLiveMode)
  const timetravelClient = getTimeTravelUiBackendClient()

  return {
    getDistinctColumnValues: ({
      reportName,
      ...payload
    }: IDistinctColumnValuesRequest & {
      reportName: string
    }) =>
      withErrorHandling<IDistinctColumnValuesResponse>(
        () =>
          uiBackendClient.post(
            apiEndpoints.GetDistinctColumnValues(reportName),
            payload
          ),
        `Unable to fetch distinct column values for the ${reportName} report with the provided payload: `,
        payload
      ),
    getSubset: (reportName: string, payload: ISubsetRequest) =>
      withErrorHandling<ISubsetResponse>(
        () => uiBackendClient.post(apiEndpoints.GetSubset(reportName), payload),
        `Unable to fetch subset for the ${reportName} report.`,
        payload
      ),
    getTradingAreas: (
      reportName: string,
      payload: IDistinctColumnValuesRequest
    ) =>
      withErrorHandling<IGetTradingAreasResponse>(
        () =>
          uiBackendClient.post(
            apiEndpoints.GetTradingAreas(reportName),
            payload
          ),
        `Unable to fetch trading areas for the ${reportName} report`,
        payload
      ),
    getMetadata: (reportName: string, payload: IMetaDataRequest) =>
      withErrorHandling<IMetaDataResponse>(
        () =>
          uiBackendClient.post(apiEndpoints.GetMetadata(reportName), payload),
        `Unable to fetch metadata from the ${reportName} report`,
        payload
      ),
    getReportDetails: (reportName: string) =>
      withErrorHandling<IGetReportDetailsResponse>(
        () => uiBackendClient.get(apiEndpoints.getReportDetails(reportName)),
        `Can't get report details for ${reportName}`
      ),
    getSummary: (reportName: string, payload: ISummaryRequest) =>
      withErrorHandling<ISummaryResponse>(
        () =>
          uiBackendClient.post(apiEndpoints.GetSummary(reportName), payload),
        `Unable to fetch summary for ${reportName}`,
        payload
      ),
    getKnowledgeDatesAsOf: (
      reportName: string,
      payload: KnowledgeDatesAsOfRequest
    ) =>
      withErrorHandling<KnowledgeDatesAsOfResponse>(
        () =>
          timetravelClient.get(
            apiEndpoints.GetKnowledgeDatesAsOf(
              reportName,
              `?asOf=${payload.asOf}`
            )
          ),
        `Unable to fetch knowledge dates for ${reportName}`,
        payload
      )
  }
}
