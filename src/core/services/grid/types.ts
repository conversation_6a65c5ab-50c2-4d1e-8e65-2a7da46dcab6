import { ValueType } from '@/core/services/riskReport'

export type TableRow = Record<string, ValueType>

export enum GridFilter {
  AgTextColumnFilter = 'agTextColumnFilter',
  AgSetColumnFilter = 'agSetColumnFilter',
  AgDateColumnFilter = 'agDateColumnFilter',
  AgNumberColumnFilter = 'agNumberColumnFilter'
}

export interface AgGridFilterModel {
  filterType: string
  type: AgGridFilterOperator
  filter: string | number | boolean
  filterTo: string | number | boolean
  dateFrom?: string
  dateTo?: string
  operator?: AgGridLogicalFilterOperator
  conditions?: AgGridFilterModel[]
  values: (string | number | boolean)[]
}

export type AgGridLogicalFilterOperator = 'OR' | 'AND'

/**
 * Represents a column id as a key and a grid filter model as a value. Eg:
 * {
 *     Team: AgGridFilterModel,
 *     TradingArea: AgGridFilterModel
 * }
 */
export type AgGridFilterModels = Record<string, AgGridFilterModel>

export enum AgGridFilterOperator {
  Equals = 'equals',
  NotEqual = 'notEqual',
  Contains = 'contains',
  StartsWith = 'startsWith',
  EndsWith = 'endsWith',
  GreaterThan = 'greaterThan',
  LessThan = 'lessThan',
  Blank = 'blank',
  NotBlank = 'notBlank',
  InRange = 'inRange',
  Set = 'set',
  LessThanOrEqual = 'lessThanOrEqual',
  GreaterThanOrEqual = 'greaterThanOrEqual'
}
