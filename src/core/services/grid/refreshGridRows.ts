import {
  getCachedBlockCount,
  getCachedBlockStatusSummary
} from '@/core/utils/grid'
import { LogLevel, logger } from '@/core/utils/logger'
import { GridApi } from '@ag-grid-community/core'
import { isEqual } from 'lodash'
import { getGlue } from '@/core/services/glue'

export const refreshGridRows = async (gridApi: GridApi) => {
  // The refreshServerSide({ purge: false }) method only refreshes the data that is currently stored in the grid's data source.
  // If the grid is empty, there's no data to refresh, so the `getRows` function is not called.
  // In such case, we need to reload the entire grid using refreshServerSide({ purge: true }).
  if (gridApi.getDisplayedRowCount() === 0) {
    return gridApi.refreshServerSide({ purge: true })
  }

  const purgeGroupKeys: string[][] = []
  const refreshGroupKeys: string[][] = [
    // The empty array is the top-level route that should always be updated.
    []
  ]

  const purgeCollapsed = await getPurgeCollapsedFlag()

  const rowModel = gridApi.getModel()

  // Find ALL groups that the row model currently has loaded, including not visible but still cached.
  // All visible groups must be refreshed to ensure a consistent set of data is displayed.
  // Invisible groups need to be purged explicitly to save memory, and avoid unnecessary calls to the backend.
  // Without explicit purging, ag-grid seems to hang on to cached invisible groups forever.
  rowModel.forEachNode((rowNode) => {
    // Note the definition of "route" in the refreshServerSide argument as per ag-grid documentation:
    //   List of group keys, pointing to the level to refresh.
    //   For example, to purge two levels down ***under*** 'Canada'and then '2002', pass in the string array ['Canada','2002'].
    //   If no route is passed, or an empty array, then the top level is refreshed.
    // Thus, the "route" we need to pass is not rowNode.getRoute(), which would return e.g. ['Canada','2002','Gold'],
    // but parentRoute = rowNode.getGroupKeys(excludeSelf: true), which returns ['Canada','2002'] for the same row.
    // This is because calling refreshServerSide( { route: ['Canada','2002'] }) does not refresh the row showing values for
    // the group with ['Canada','2002'], but it refreshes all rows below (whose keys aren't known yet,
    // and may have changed since the last refresh.)
    const parentRoute = rowNode.getGroupKeys(true)

    if (parentRoute) {
      const isVisible = rowNode.displayed
      if (isVisible || !purgeCollapsed) {
        addUnique(refreshGroupKeys, parentRoute)
      } else {
        addUnique(purgeGroupKeys, parentRoute)
      }
    }
  })

  logger.info(
    `refreshGridRows is purging ${purgeGroupKeys.length} groups and refreshing ${refreshGroupKeys.length} groups`,
    {
      purgeCollapsed,
      purgeGroupCount: purgeGroupKeys.length,
      refreshGroupCount: refreshGroupKeys.length,
      cachedBlockCount: getCachedBlockCount(gridApi),
      cachedBlockStatusSummary: getCachedBlockStatusSummary(gridApi)
    }
  )

  refreshGridGroups(gridApi, true, purgeGroupKeys)
  refreshGridGroups(gridApi, false, refreshGroupKeys)
}

/**
 * Fixes Time Travel issue where some group was expanded and disappeared and later appeared during time travel
 * then is broken, we cannot see details, it looks like expanded but we don't see subgroups etc.
 *
 * https://prdjira.saccap.int/browse/MACROUIUX-2108
 *
 * @param gridApi
 */
export const purgeEmptyCache = ({ api }: { api: GridApi }) => {
  const purgeGroupKeys: string[][] = []
  const rowModel = api.getModel()

  rowModel.forEachNode((rowNode) => {
    const parentRoute = rowNode.getGroupKeys(true)

    if (parentRoute) {
      if ((rowNode as any)?.childStore?.cache?.numberOfRows == 0) {
        logger.debug(
          `RefreshGridRows found empty childStore: ${rowNode.displayed}, ${rowNode.expanded}`,
          { rowNode, parentRoute }
        )

        addUnique(purgeGroupKeys, parentRoute)
      }
    }
  })

  refreshGridGroups(api, true, purgeGroupKeys)
}

const refreshGridGroups = (
  gridApi: GridApi,
  purge: boolean,
  groupKeys: string[][]
) => {
  groupKeys.forEach((groupKey) => {
    logger.debug(`RefreshServerSide Route: ${groupKey}`)
    gridApi.refreshServerSide({ route: groupKey, purge })
  })
}

function addUnique(set: string[][], newKey: string[]): void {
  if (!set.some((key) => isEqual(key, newKey))) set.push(newKey)
}

// Quick and dirty implementation to detect changes of the feature flag in the glue context,
// so that in a workspace with many app windows open, it is sufficient to change the flag in a single one.
// We have one user with 12+ app windows open in EPIC, so this will make adjusting the flag managable.
// Clean this up in MACRUIUX-1774
async function getPurgeCollapsedFlag(): Promise<boolean> {
  const glue = getGlue()
  const windowValue = (window as any).purgeCollapsed === true

  if (!glue) {
    return windowValue
  }

  const windowValueInGlue = (window as any).purgeCollapsedInGlue === true
  const glueFlags = await glue?.contexts.get('m72-feature-flags')
  const glueValue = glueFlags.purgeCollapsed

  const valueNotSetInGlue = glueValue !== true && glueValue !== false
  const valueChangedInWindow = windowValue !== windowValueInGlue
  const valueChangedInGlue = glueValue !== windowValueInGlue

  if (valueNotSetInGlue || valueChangedInWindow) {
    await glue?.contexts.set('m72-feature-flags', {
      purgeCollapsed: windowValue
    })
    ;(window as any).purgeCollapsedInGlue = windowValue
    return windowValue
  } else if (valueChangedInGlue) {
    // eslint-disable-next-line @typescript-eslint/no-extra-semi
    ;(window as any).purgeCollapsedInGlue = glueValue
    ;(window as any).purgeCollapsed = glueValue
    return glueValue
  }

  return windowValue
}

export const refreshInconsistentRows = (
  gridApi: GridApi,
  currentAsOfUtc: string
) => {
  const rowModel = gridApi.getModel()
  const inconsistentGroupKeys: string[][] = []
  let someRowDataIsOutOfSync = false

  rowModel.forEachNode((rowNode) => {
    const parentRoute = rowNode.getGroupKeys(true)

    const rowAsOfMatchesCurrent = currentAsOfUtc == rowNode.data?.asOfUtc

    logger.log(
      rowAsOfMatchesCurrent ? LogLevel.Debug : LogLevel.Warn,
      `RefreshInconsistentRows checking CurrentAsOf: ${currentAsOfUtc}, RowAsOf: ${
        rowNode.data?.asOfUtc
      }, RowRoute: ${rowNode.getRoute()}, ParentRoute: ${parentRoute}`
    )

    if (!rowAsOfMatchesCurrent && parentRoute) {
      someRowDataIsOutOfSync = true
      addUnique(inconsistentGroupKeys, parentRoute)
    }
  })

  if (someRowDataIsOutOfSync) {
    logger.warn(
      'Inconsistent rows found in these parent groups, refresh them once more:',
      inconsistentGroupKeys
    )
    refreshGridGroups(gridApi, false, inconsistentGroupKeys)
  } else {
    logger.debug('No inconsistent rows found')
  }
}
