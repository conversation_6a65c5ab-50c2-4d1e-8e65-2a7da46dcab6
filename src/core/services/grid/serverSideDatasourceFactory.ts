import { addLoader, removeLoader } from '@/core/redux/features/notifications'
import { getSubsetThunk } from '@/core/redux/features/report/getSubsetThunk'
import { TableRow } from '@/core/services/grid'
import {
  IServerSideDatasource,
  IServerSideGetRowsRequest
} from '@ag-grid-community/core'
import { Action } from '@reduxjs/toolkit'
import { ThunkDispatch } from 'redux-thunk'

export interface IGetServerSideDataResponse {
  success: boolean
  rows: {
    data: TableRow[]
    totalRowCount: number
  }
}

export type IGetServerSideData = (
  request: IServerSideGetRowsRequest
) => Promise<IGetServerSideDataResponse>

export const serverSideDatasourceFactory = (
  dispatch: ThunkDispatch<unknown, unknown, Action>
): IServerSideDatasource => {
  return {
    // Called by the grid when more rows are required.
    getRows: async (params) => {
      const id = `grid:${params.request.startRow}-${params.request.endRow}`
      dispatch(addLoader(id))
      await dispatch(getSubsetThunk(params))
      dispatch(removeLoader(id))
    }
  }
}
