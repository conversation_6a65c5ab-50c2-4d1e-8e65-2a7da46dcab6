import { serverSideDatasourceFactory } from '@/core/services/grid'
import { vi } from 'vitest'
import { serverSideGetRowsParamsMock } from '@/core/testing/mocks'
import { setupStore } from '@/core/redux/store'

describe('serverSideDatasourceFactory', () => {
  // const server = setupMockServer(...allHandlers)
  it('should dispatch getSubsetThunk when `getRows` is executed', async () => {
    // given
    const getRowsParams = serverSideGetRowsParamsMock as any
    const dispatchMock = vi.fn()
    const datasource = serverSideDatasourceFactory(dispatchMock)
    // when
    await datasource.getRows(getRowsParams)

    // then
    expect(dispatchMock).toHaveBeenCalled()
  })

  it('getRows should call params callback', async () => {
    // given
    const store = setupStore()
    const getRowsParams = serverSideGetRowsParamsMock as any
    const datasource = serverSideDatasourceFactory(store.dispatch)
    // when
    await datasource.getRows(getRowsParams)

    // then
    expect(getRowsParams.fail).toHaveBeenCalled()
  })
})
