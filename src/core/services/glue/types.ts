import {
  DrillThroughContext,
  OriginatingContext
} from '@/core/redux/features/report'
import { IFilterModel, IMetaDataResponse } from '@/core/services/riskReport'

export const enum SyncDataEventType {
  InitializeApp = 'initialize-app',
  SelectedTradingAreasChanged = 'selected-trading-areas-changed',
  LiveModeChanged = 'live-mode-changed',
  GridRowSelected = 'grid-row-selected',
  GridFilterChanged = 'grid-filter-changed',
  MetadataUpdate = 'metadata-update',
  ChannelListenerConnected = 'channel-listener-connected',
  OriginatingReportAsOfChanged = 'originating-report-as-of-changed'
}

export type InitializeAppEvent = {
  type: SyncDataEventType.InitializeApp
  reportName: string
  drillThroughContext: DrillThroughContext
}

export type SelectedTradingAreasChangedEvent = {
  type: SyncDataEventType.SelectedTradingAreasChanged
  reportName: string
  selectedTradingAreas: string[]
}

export type LiveModeChangedEvent = {
  type: SyncDataEventType.LiveModeChanged
  reportName: string
  isLiveModeEnabled: boolean
}

export type GridRowSelectedEvent = {
  type: SyncDataEventType.GridRowSelected
  reportName: string
  originatingContext: OriginatingContext
}

export type GridFilterChangedEvent = {
  type: SyncDataEventType.GridFilterChanged
  reportName: string
  filters: IFilterModel[]
}

export type MetadataUpdateEvent = {
  type: SyncDataEventType.MetadataUpdate
  metadata: IMetaDataResponse
}

export type ChannelListenerConnectedEvent = {
  type: SyncDataEventType.ChannelListenerConnected
}

export type OriginatingReportAsOfChangedEvent = {
  type: SyncDataEventType.OriginatingReportAsOfChanged
  reportName: string
  originatingReportAsOfUtc: string
}

export type SyncDataEvent =
  | InitializeAppEvent
  | SelectedTradingAreasChangedEvent
  | LiveModeChangedEvent
  | GridRowSelectedEvent
  | GridFilterChangedEvent
  | MetadataUpdateEvent
  | ChannelListenerConnectedEvent
  | OriginatingReportAsOfChangedEvent

export const enum InitializationStep {
  notStarted = 'notStarted',
  notGlue = 'notGlue',
  initialized = 'initialized',
  error = 'error'
}
