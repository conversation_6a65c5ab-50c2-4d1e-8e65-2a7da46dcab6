import { logger } from '@/core/utils/logger'
import Glue, { Glue42 } from '@glue42/desktop'
import GlueWorkspaces from '@glue42/workspaces-api'
import {
  DrillThroughContext,
  OriginatingContext,
  setDrillThroughViewLiveUpdateAsOfUtcMetadataOrigin,
  setLatestAsOfUtcResponse,
  setOriginatingContextExceptReportAsOf,
  setSelectedTradingAreas,
  startLatestAsOfUtcListener,
  stopLatestAsOfUtcListener
} from '@/core/redux/features/report'
import { getGlueInstance, isAppInGlueEnvironment } from './utils/glueUtils'
import { Dispatch } from '@reduxjs/toolkit'
import { getLatestAsOfUtc, getMetaData } from '../riskReport'

export const getGlue = getGlueInstance

export const initializeGlue = async (dispatch: Dispatch): Promise<void> => {
  if (isAppInGlueEnvironment() && !getGlue()) {
    try {
      if (!window.glue) {
        window.glue = await Glue({
          channels: true,
          bus: true,
          appManager: 'full',
          layouts: {
            mode: 'full',
            autoSaveWindowContext: ['Global', 'Workspace']
          },
          libraries: [GlueWorkspaces]
        })
      }

      const drillThroughContext = getDrillThroughContext()

      if (drillThroughContext) {
        if (
          drillThroughContext.isLiveModeEnabled ||
          drillThroughContext.channel
        ) {
          const {
            originatingContext: { reportName },
            drillThroughViewLiveUpdateAsOfUtcMetadataOrigin
          } = drillThroughContext

          const { latestAsOfUtc } = await getLatestAsOfUtc({
            reportName
          })

          const metadataResponse = await getMetaData({
            isLiveModeEnabled: drillThroughContext.isLiveModeEnabled,
            asOfUtc: latestAsOfUtc,
            reportName
          })

          dispatch(
            setLatestAsOfUtcResponse({
              latestAsOfUtc:
                metadataResponse.data.metadata[
                  drillThroughViewLiveUpdateAsOfUtcMetadataOrigin
                ],
              originatingReportLatestAsOfUtc: latestAsOfUtc
            })
          )

          dispatch(
            setOriginatingContextExceptReportAsOf({
              ...drillThroughContext.originatingContext,
              reportAsOfUtc: latestAsOfUtc
            })
          )
        } else {
          dispatch(
            setLatestAsOfUtcResponse({
              latestAsOfUtc: drillThroughContext.currentAsOf || '',
              originatingReportLatestAsOfUtc:
                drillThroughContext.originatingContext.reportAsOfUtc
            })
          )

          dispatch(
            setOriginatingContextExceptReportAsOf(
              drillThroughContext.originatingContext
            )
          )
        }

        dispatch(
          setDrillThroughViewLiveUpdateAsOfUtcMetadataOrigin(
            drillThroughContext.drillThroughViewLiveUpdateAsOfUtcMetadataOrigin
          )
        )

        if (drillThroughContext.isLiveModeEnabled) {
          dispatch(startLatestAsOfUtcListener())
        } else {
          dispatch(stopLatestAsOfUtcListener())
        }

        dispatch(
          setSelectedTradingAreas(
            drillThroughContext.selectedTradingAreas || []
          )
        )
      }

      logger.debug('Glue initialized')
    } catch (error) {
      logger.warn(
        "Can't initialize glue",
        { component: 'InitializeGlue', navigator },
        error as any
      )
      throw error
    }
  } else {
    throw new Error('Not running in Glue environment')
  }
}

export const getCurrentChannel = () => {
  const glue = getGlue()

  if (!glue) {
    return undefined
  }

  return glue?.channels.my()
}

export const findFreeChannel = async (): Promise<
  Glue42.ChannelContext | undefined
> => {
  const glue = getGlue()
  const channels = await glue.channels.list()
  const freeChannel = channels.find(
    (channel) => JSON.stringify(channel.data) === '{}'
  )

  if (!freeChannel) {
    Promise.reject('No free channel found')
  }

  return freeChannel
}

export const publishToChannel = (data: any) => {
  const glue = getGlue()
  const channelName = getCurrentChannel()

  if (!channelName) return

  glue.channels.publish(data)
}

export const subscribeToChannel = (callback: (data: any) => void) => {
  const channelName = getCurrentChannel()
  const glue = getGlue()

  if (!channelName)
    return () => {
      // empty unsubscribe function
    }

  return glue.channels.subscribe(callback)
}

export const startApplication = async (
  appName: string,
  originatingContext: OriginatingContext
) => {
  let channel = getCurrentChannel()

  if (!channel) {
    const freeChannel = await findFreeChannel()
    channel = freeChannel?.name
    channel && (await getGlue().channels.join(channel))
  }

  await getGlue().appManager.application(appName).start({
    drillThroughContext: {
      originatingContext,
      channel
    }
  })
}

/**
 * Returns the current Glue42 window object. It provides information and methods related to the current window
 */
const getGlueWindow = () => window.glue?.windows.my()

export const getGlueContext = () => {
  try {
    const glueWindow = getGlueWindow()
    return glueWindow?.context
  } catch (error) {
    logger.error(
      'Unable to get Glue Content details using getGlueContext method: ',
      {
        component: 'getGlueContext'
      },
      error as Error
    )
    return undefined
  }
}

export const getApplicationTitle = () => {
  return window.glue?.windows.my().application?.title ?? 'M72'
}

export const getDrillThroughContext = () =>
  getGlueContext()?.drillThroughContext as DrillThroughContext | undefined
