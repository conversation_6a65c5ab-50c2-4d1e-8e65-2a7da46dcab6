import {
  ContextActions,
  ContextActionsTypes,
  currentAsOfUtcSelector,
  OriginatingContext,
  reportNameSelector,
  setLastOriginatingContext
} from '@/core/redux/features/report'
import {
  GetContextMenuItemsParams,
  GridApi,
  MenuItemDef
} from '@ag-grid-community/core'
import { getGlue, startApplication } from '@/core/services/glue'
import {
  mapAgGridFilterModelsToFilterModel,
  refineFilterModel
} from '@/core/utils/grid'
import { AppDispatch, RootState } from '@/core/redux/store'

/**
 * It is used for EPIC app launcher only and disabled for the browser.
 */
export const getContextMenuItems = (
  params: GetContextMenuItemsParams,
  getState: () => RootState,
  dispatch: AppDispatch
) => {
  let items: (MenuItemDef | string)[] = params.defaultItems ?? []

  const row = params.node?.data
  const contextActions: ContextActions | undefined = row['ContextActions']

  if (!contextActions) {
    return items
  }

  items = [
    ...items,
    ...contextActions.Actions.filter(
      // so far ignore all actions except drill through
      (action) => action.Type === ContextActionsTypes.openDrillThroughView
      // ignore all actions which doesn't have app instance in glue
    )
      .filter((action) =>
        Boolean(
          getGlue()?.appManager.application(action.Params.Glue42ViewAppName)
        )
      )
      .map((action) => ({
        name: action.Description,
        action: () => {
          const originatingContext = buildDrillThroughContext({
            gridApi: params.api,
            originatingGroupKey: action.Params.OriginatingGroupKey,
            state: getState()
          })
          dispatch(setLastOriginatingContext(originatingContext))

          startApplication(action.Params.Glue42ViewAppName, originatingContext)
        }
      }))
  ]

  return items
}

interface BuildDrillThroughContextParams {
  gridApi: GridApi<any>
  originatingGroupKey: string
  state: RootState
}

export const buildDrillThroughContext = ({
  gridApi,
  originatingGroupKey,
  state
}: BuildDrillThroughContextParams): OriginatingContext => {
  const refinedFilterModel = refineFilterModel(gridApi.getFilterModel())
  const filters = mapAgGridFilterModelsToFilterModel(refinedFilterModel)
  const groupKey = JSON.parse(originatingGroupKey)
  const reportName = reportNameSelector(state)
  const currentAsOfUtc = currentAsOfUtcSelector(state)

  const context: OriginatingContext = {
    reportName,
    groupKey,
    filters,
    reportAsOfUtc: currentAsOfUtc
  }

  return context
}
