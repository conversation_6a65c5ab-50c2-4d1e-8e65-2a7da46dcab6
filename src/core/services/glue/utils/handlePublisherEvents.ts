import { logger } from '@/core/utils/logger'
import { SyncDataEvent, SyncDataEventType } from '../types'
import { publishSelectedTradingAreasThunk } from '@/core/redux/features/context/thunk/publishSelectedTradingAreasThunk'
import { AppDispatch } from '@/core/redux/store'
import { replayPublishGridRowSelectedThunk } from '@/core/redux/features/context/thunk/replayPublishGridRowSelectedThunk'
import { publishLiveModeChangedThunk } from '@/core/redux/features/context/thunk/publishLiveModeChangedThunk'
import { replayPublishMetadataThunk } from '@/core/redux/features/context/thunk/replayPublishMetadataThunk'
import { publishAsOfChangedThunk } from '@/core/redux/features/context/thunk/publishAsOfChangedThunk'

export const handlePublisherEvents =
  (dispatch: AppDispatch) => async (event: SyncDataEvent) => {
    logger.info('Incoming drill through event: ', event)

    switch (event.type) {
      case SyncDataEventType.ChannelListenerConnected:
        logger.debug('Handling listener connected event: ', event)

        dispatch(publishSelectedTradingAreasThunk())
        dispatch(replayPublishGridRowSelectedThunk())
        dispatch(replayPublishMetadataThunk())
        dispatch(publishLiveModeChangedThunk())
        dispatch(publishAsOfChangedThunk())
        break

      default:
        break
    }
  }
