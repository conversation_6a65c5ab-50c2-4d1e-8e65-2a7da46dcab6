import { handlePublisherEvents } from '../handlePublisherEvents'
import { SyncDataEventType } from '../../types'
import { publishSelectedTradingAreasThunk } from '@/core/redux/features/context/thunk/publishSelectedTradingAreasThunk'
import { replayPublishGridRowSelectedThunk } from '@/core/redux/features/context/thunk/replayPublishGridRowSelectedThunk'
import { publishLiveModeChangedThunk } from '@/core/redux/features/context/thunk/publishLiveModeChangedThunk'
import { replayPublishMetadataThunk } from '@/core/redux/features/context/thunk/replayPublishMetadataThunk'
import { publishAsOfChangedThunk } from '@/core/redux/features/context/thunk/publishAsOfChangedThunk'
import configureMockStore from 'redux-mock-store'
import thunk from 'redux-thunk'

const middlewares = [thunk]
const mockStore = configureMockStore(middlewares)

describe('handlePublisherEvents', () => {
  let store: ReturnType<typeof mockStore>

  beforeEach(() => {
    store = mockStore({})
  })

  it('should dispatch all thunks on ChannelListenerConnected event', async () => {
    const event = { type: SyncDataEventType.ChannelListenerConnected }
    const dispatch = store.dispatch

    await handlePublisherEvents(dispatch)(event)

    // method provided by redux-mock-store that returns an array of all the actions that have been dispatched to the mock store
    const actions = store.getActions()

    expect(actions.map((action) => action.type)).toEqual([
      'report/publishSelectedTradingAreasThunk/pending',
      'report/replayPublishGridRowSelectedThunk/pending',
      'report/replayPublishMetadataThunk/pending',
      'report/publishLiveModeChangedThunk/pending',
      'report/publishAsOfChangedThunk/pending'
    ])
  })
})
