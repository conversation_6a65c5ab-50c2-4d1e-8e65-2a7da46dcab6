import { act, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
// import { handleDrillThroughContextUpdateEvent } from './handleDrillThroughContextUpdateEvent'
import { SyncDataEventType } from '@/core/services/glue/types'

vi.mock('redux', () => ({
  dispatch: vi.fn()
}))

vi.mock('@/core/services/riskReport', () => ({
  getLatestAsOfUtc: vi.fn().mockResolvedValue({
    latestAsOfUtc: new Date().toISOString()
  }),
  getMetaData: vi.fn().mockResolvedValue({
    data: {
      metadata: {}
    }
  })
}))

// TODO GM: (test) refactor to test handle subscriber events
describe.skip('handleDrillThroughContextUpdateEvent', () => {
  let dispatch: any
  let event: any

  beforeEach(() => {
    dispatch = vi.fn()
    event = {
      type: '',
      payload: {}
    }
  })

  it('should handle InitializeApp event', async () => {
    event = {
      type: SyncDataEventType.InitializeApp,
      reportName: '...',
      drillThroughContext: {
        currentAsOf: '',
        originatingContext: {},
        drillThroughViewLiveUpdateAsOfUtcMetadataOrigin: '',
        isLiveModeEnabled: true,
        selectedTradingAreas: []
      }
    }

    await act(
      async () => await handleDrillThroughContextUpdateEvent(event, dispatch)
    )

    await waitFor(() => expect(dispatch).toHaveBeenCalledTimes(5))
  })

  it('should handle GridRowSelected event', () => {
    event = {
      type: SyncDataEventType.GridRowSelected,
      reportName: '...',
      originatingContext: {
        groupKey: {}
      }
    }

    handleDrillThroughContextUpdateEvent(event, dispatch)

    expect(dispatch).toHaveBeenCalledTimes(1)
  })

  it('should handle SelectedTradingAreasChanged event', () => {
    event = {
      type: SyncDataEventType.SelectedTradingAreasChanged,
      reportName: '...',
      selectedTradingAreas: []
    }

    handleDrillThroughContextUpdateEvent(event, dispatch)

    expect(dispatch).toHaveBeenCalledTimes(1)
  })

  it('should handle LiveModeChanged event', () => {
    event = {
      type: SyncDataEventType.LiveModeChanged,
      reportName: '...',
      isLiveModeEnabled: true
    }

    handleDrillThroughContextUpdateEvent(event, dispatch)

    expect(dispatch).toHaveBeenCalledTimes(1)
  })

  it('should handle GridFilterChanged event', () => {
    event = {
      type: SyncDataEventType.GridFilterChanged,
      reportName: '...',
      filters: []
    }

    handleDrillThroughContextUpdateEvent(event, dispatch)

    expect(dispatch).toHaveBeenCalledTimes(1)
  })
})
