import { logger } from '@/core/utils/logger'
import { SyncDataEvent, SyncDataEventType } from '../types'
import {
  setLatestAsOfUtcFromGlue,
  setOriginatingContextExceptReportAsOf,
  setOriginatingContextFilters,
  setOriginatingReportAsOf,
  setSelectedTradingAreas,
  stopLatestAsOfUtcListener
} from '@/core/redux/features/report'
import {
  setIsConnectedSubscriber,
  setParentLiveModeEnabled
} from '@/core/redux/features/context/contextSlice'
import { setSubscriberMetadataThunk } from '@/core/redux/features/report/reportMetadata/thunk/setSubscriberMetadataThunk'
import { AppDispatch } from '@/core/redux/store'

export const handleSubscriberEvents =
  (dispatch: AppDispatch) => async (event: SyncDataEvent) => {
    logger.info('Incoming glue event: ', event)

    switch (event.type) {
      case SyncDataEventType.MetadataUpdate: {
        logger.debug(`Handling MetadataUpdate event with metadata:`, event)

        dispatch(setLatestAsOfUtcFromGlue(event.metadata))
        dispatch(setSubscriberMetadataThunk(event.metadata))

        break
      }

      case SyncDataEventType.GridRowSelected: {
        const publishedRowRoute = event.originatingContext.groupKey
          ? Object.values(event.originatingContext.groupKey)
          : '(root)'
        logger.debug(
          `Handling GridRowSelectedEvent event for row "${publishedRowRoute}"`,
          event
        )
        dispatch(
          setOriginatingContextExceptReportAsOf(event.originatingContext)
        )
        break
      }

      case SyncDataEventType.SelectedTradingAreasChanged:
        logger.debug(
          `Handling SelectedTradingAreasChanged event for trading areas ${event.selectedTradingAreas}`,
          event
        )
        dispatch(setSelectedTradingAreas(event.selectedTradingAreas || []))
        break

      case SyncDataEventType.LiveModeChanged:
        logger.debug(`Handling LiveModeChanged event`, event)
        // disable live mode for subscriber
        dispatch(stopLatestAsOfUtcListener())

        dispatch(setParentLiveModeEnabled(event.isLiveModeEnabled))
        dispatch(setIsConnectedSubscriber(true))
        break

      case SyncDataEventType.GridFilterChanged: {
        let filteredColumns = event.filters.map(
          (filterModel) => filterModel.column
        )
        if (filteredColumns.length == 0) filteredColumns = ['(none)']
        logger.debug(
          `Handling GridFilterChanged event for columns ${filteredColumns}`,
          event
        )

        // Note that we don't set the filters we received from the originating window on the receiving window's grid,
        // but pass them to the backend within the originating context, so the appropriate mapping into the receiving window's
        // row set can be made there.
        dispatch(setOriginatingContextFilters(event.filters))
        break
      }

      case SyncDataEventType.OriginatingReportAsOfChanged: {
        logger.debug(`Handling OriginatingReportAsOfChanged event`, event)
        dispatch(setOriginatingReportAsOf(event.originatingReportAsOfUtc))
        break
      }

      default:
        break
    }
  }
