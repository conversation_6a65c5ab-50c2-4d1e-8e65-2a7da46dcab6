import { logger } from '@/core/utils/logger'
import { saveMyApplicationWindowState, getAppContext } from '@epic/window'
import { getGlue } from './glue'

/* Get Glue Application Context */
export const getApplicationContext = async () => {
  try {
    const applicationContext = await getAppContext()
    return applicationContext || undefined
  } catch (error) {
    logger.warn(
      'getApplicationContext: Unable to get Glue Application context. Error: ',
      {},
      error as Error
    )
    return undefined
  }
}
/* Get Glue Application Context */

/* Set window UUID in Glue Application Context */
export const setWorkspaceWindowUUID = async (windowUUID: string) => {
  try {
    const glue = getGlue()
    const inWorkSpace = await inWorkspace()
    if (glue && inWorkSpace) {
      let currentApplicationContext = (await getApplicationContext()) || {}
      currentApplicationContext = {
        ...currentApplicationContext,
        windowUUID
      }

      saveMyApplicationWindowState(currentApplicationContext)
      logger.info(
        'Updated Glue Context details with current Unique Identifier of Tab',
        { currentApplicationContext }
      )
    }
  } catch (error) {
    logger.error(
      'Unable to update Glue Context details using setWorkspaceWindowUUID method. here are the provided error details: ',
      {},
      error as Error
    )
  }
}

export const inWorkspace = async () =>
  await window.glue?.workspaces?.inWorkspace()

/* Get Glue Window Specific Unique Id for Opened Tab */
export const getWindowUUID = async () => {
  const inWorkSpace = await inWorkspace()
  const glue = getGlue()
  const isGlueWorkspaceWindow = glue && inWorkSpace
  const appWindowContext: { windowUUID?: string } | undefined =
    await getApplicationContext()

  let windowUUID = ''
  if (isGlueWorkspaceWindow && appWindowContext?.windowUUID) {
    windowUUID = appWindowContext.windowUUID
  } else if (
    isGlueWorkspaceWindow &&
    appWindowContext?.windowUUID === undefined
  ) {
    logger.warn('Unable to obtain uuid from glue app window context')
  }
  return windowUUID
}
