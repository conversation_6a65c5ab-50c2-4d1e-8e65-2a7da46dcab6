import { createListenerMiddleware } from '@reduxjs/toolkit'
import { addLatestAsOfUtcListeners } from '../features/report/listeners/latestAsOfUtcListener'
import { addReportMetadataListeners } from '../features/report/reportMetadata/reportMetadaListener'
import { addSaveVisibilityPreferencesListener } from '../features/users/listeners/visibilityPrefencesListener'
import { RootState } from '../store'
import { addCurrentAsOfListener } from '../features/report/listeners/currentAsOfUtcListener'
import { addLiveModeEnabledListener } from '../features/report/listeners/isLiveModeEnabledListener'
import { addKnowledgeAsOfValuesListener } from '../features/report/listeners/knowledgeAsOfValuesListener'

export const listenerMiddlewareInstance = createListenerMiddleware<RootState>()
export type AppStartListening = typeof listenerMiddlewareInstance.startListening

addLatestAsOfUtcListeners(listenerMiddlewareInstance.startListening)
addReportMetadataListeners(listenerMiddlewareInstance.startListening)
addSaveVisibilityPreferencesListener(listenerMiddlewareInstance.startListening)
addCurrentAsOfListener(listenerMiddlewareInstance.startListening)
addLiveModeEnabledListener(listenerMiddlewareInstance.startListening)
addKnowledgeAsOfValuesListener(listenerMiddlewareInstance.startListening)
