import { useAppDispatch } from '@/core/redux/hooks/useAppDispatch'
import {
  removeNotification,
  uiSlice
} from '@/core/redux/features/notifications'
import { useCallback } from 'react'
import { env } from '@/core/config'

export const useNotifications = () => {
  const dispatch = useAppDispatch()
  const { addNotification } = uiSlice.actions

  const addDangerNotification = useCallback(
    (text: string) => {
      const notificationId = Date.now().toString()
      dispatch(
        addNotification({
          text,
          intent: 'danger',
          id: notificationId
        })
      )

      setTimeout(() => {
        dispatch(removeNotification(notificationId))
      }, env.toastDisplayTimeMs)
    },
    [dispatch, addNotification, removeNotification]
  )

  return {
    addDangerNotification
  }
}
