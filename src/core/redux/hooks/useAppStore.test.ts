import { renderHook } from '@testing-library/react'
import { useAppStore } from './useAppStore'
import { ReduxWrapper } from '@/core/testing'

describe('useAppStore', () => {
  it('should be defined', () => {
    expect(useAppStore).toBeDefined()
  })

  it('should return store', () => {
    const expandedGroupsStub = ['test']
    const { result } = renderHook(() => useAppStore(), {
      wrapper: ReduxWrapper({
        grid: {
          expandedGroupsIds: expandedGroupsStub
        }
      })
    })

    const state = result.current.getState()

    expect(state.grid.expandedGroupsIds).toStrictEqual(expandedGroupsStub)
  })
})
