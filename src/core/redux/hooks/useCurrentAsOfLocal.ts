import {
  formatDateToLocal,
  formatDateToUtc,
  formatTimestampToLocalWithMilliseconds,
  formatTimestampToUTCWithMilliseconds
} from '@/core/utils/date'
import { useAppDispatch, useAppSelect } from '.'
import {
  setCurrentAsOfLocal as setCurrentAsOfLocalAction,
  setRegionExpand,
  stopLatestAsOfUtcListener
} from '../features/report'
import { isKnowledgeAsOfUIVisibleSelector } from '../features/report/reportMetadata/selectors'
import { getEODCloseDateTime } from '@/components/TopBar/CloseTimeButton/closeTimeUtils'

export const useCurrentAsOfLocal = () => {
  const currentAsOfUtc = useAppSelect((state) => state.report.currentAsOfUtc)
  const dispatch = useAppDispatch()
  const isKnowledgeAsOfUIVisible = useAppSelect(
    isKnowledgeAsOfUIVisibleSelector
  )

  const changeCurrentAsOfDate = (date: Date) => {
    const formattedDate = formatDateToLocal(date)
    dispatch(setCurrentAsOfLocalAction(formattedDate))
    dispatch(stopLatestAsOfUtcListener())
  }

  const handleDateChange = (dateObject: Date) => {
    const newDate = isKnowledgeAsOfUIVisible
      ? getEODCloseDateTime(dateObject.toISOString())
      : dateObject
    changeCurrentAsOfDate(newDate)
    dispatch(setRegionExpand(false))
  }

  return {
    currentAsOfLocal: formatDateToLocal(new Date(currentAsOfUtc)),
    currentAsOfLocalPrecise:
      formatTimestampToLocalWithMilliseconds(currentAsOfUtc),
    currentAsOfUtc: formatDateToUtc(new Date(currentAsOfUtc)),
    currentAsOfUtcPrecise: formatTimestampToUTCWithMilliseconds(currentAsOfUtc),
    handleDateChange,
    changeCurrentAsOfDate
  }
}
