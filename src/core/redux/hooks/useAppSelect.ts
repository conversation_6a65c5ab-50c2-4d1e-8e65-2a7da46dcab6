import { TypedUseSelectorHook, useSelector } from 'react-redux'
import { RootState } from '@/core/redux/store'

/**
 * Use throughout the app instead of plain `useSelector`.
 * @example
 * const expandedGroups = useAppSelect(state => state.grid.expandedGroups)
 * @description
 * The state inside `useAppSelect` is fully typed as `RootState`.
 */
export const useAppSelect: TypedUseSelectorHook<RootState> = useSelector
