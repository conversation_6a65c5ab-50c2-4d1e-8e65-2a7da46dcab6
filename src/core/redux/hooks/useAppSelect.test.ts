import { renderHook } from '@testing-library/react'
import { useAppSelect } from '.'
import { ReduxWrapper } from '../../testing'

describe('useAppSelect', () => {
  it('should be defined', () => {
    expect(useAppSelect).toBeDefined()
  })

  it('should return state', () => {
    const expandedGroupsStub = ['test']
    const { result } = renderHook(() => useAppSelect((state) => state.grid), {
      wrapper: ReduxWrapper({
        grid: {
          expandedGroups: expandedGroupsStub
        }
      })
    })

    const grid = result.current

    expect(grid).toBeDefined()
    expect(grid.expandedGroups).toStrictEqual(expandedGroupsStub)
  })
})
