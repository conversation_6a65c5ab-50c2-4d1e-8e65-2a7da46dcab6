import { renderHook } from '@testing-library/react'
import { useAppDispatch } from '.'
import { ReduxWrapper } from '@/core/testing'

describe('useAppDispatch', () => {
  it('should be defined', () => {
    expect(useAppDispatch).toBeDefined()
  })

  it('should return dispatch function', () => {
    // use testing-library to get current result from hook
    const { result } = renderHook(() => useAppDispatch(), {
      wrapper: ReduxWrapper()
    })
    const dispatch = result.current

    expect(typeof dispatch).toBe('function')
  })

  it('should dispatch action', () => {
    const { result } = renderHook(() => useAppDispatch(), {
      wrapper: ReduxWrapper()
    })
    const dispatch = result.current

    const action = dispatch({ type: 'test' })

    expect(action).toStrictEqual({ type: 'test' })
  })
})
