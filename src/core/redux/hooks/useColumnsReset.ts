import { useAppDispatch, useAppSelect } from '@/core/redux/hooks'
import { setResetColumns as resetColumn } from '@/core/redux/features/grid'

export function useColumnsReset() {
  const resetColumns = useAppSelect((state) => state.grid.resetColumns)
  const dispatch = useAppDispatch()

  const setResetColumns = (resetState: boolean) => {
    dispatch(resetColumn(resetState))
  }

  return {
    resetColumns,
    setResetColumns
  }
}
