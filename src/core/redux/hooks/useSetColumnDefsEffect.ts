import { useGridApi } from '@/components/Grid/hooks'
import { useAppSelect, useColumnsReset } from '@/core/redux/hooks'
import { useAppStore } from '@/core/redux/hooks/useAppStore'
import {
  addOpenByDefaultPropertyToChildren,
  mapColumnInfoToColumnDefs,
  resetColumnState,
  updateColumnsHeaderNames
} from '@/core/utils/grid'
import { LogLevel, logger } from '@/core/utils/logger'
import {
  ColDef,
  ISetFilterParams,
  SetFilterValuesFuncParams,
  ValueFormatterParams
} from '@ag-grid-community/core'
import { useCallback, useEffect, useMemo } from 'react'
import { EXECUTION_TIME } from '../features/quickFilters/executionTimeFilter/constants'
import { executionTimeFilterSelector } from '../features/quickFilters/executionTimeFilter/selectors/executionTimeFilterSelector'
import { ExecutionTimeFilterState } from '../features/quickFilters/executionTimeFilter/types'
import { lastUsedPresetIdSelector } from '../features/users'
import { userPreferencesSelector } from '../features/users/selectors/userPreferencesSelector'
import { presetsSelector } from '../selectors'

export const enum AgGridFilterType {
  SET = 'agSetColumnFilter',
  DATE = 'agDateColumnFilter'
}

export const useSetColumnDefsEffect = () => {
  const store = useAppStore()
  const columnInfo = useAppSelect((state) => state.report.columnInfo)
  const columnsHeaderNames = useAppSelect(
    (state) => state.report.columnsHeaderNames
  )
  const { resetColumns, setResetColumns } = useColumnsReset()
  const { gridApi, columnApi } = useGridApi()
  const executionTimeFilter = useAppSelect(executionTimeFilterSelector)
  const { componentVisibility } = useAppSelect(userPreferencesSelector)
  const lastUsedPresetId = useAppSelect(lastUsedPresetIdSelector)
  const presets = useAppSelect(presetsSelector)

  const getDistinctColumnValues = useCallback(
    (colId: string): string[] => {
      //  We have to get the current state values directly from the store to create a closure.
      //  Otherwise, these values will retain the initial values throughout the lifecycle of the app.
      const { distinctColumnValues } = store.getState().report
      return distinctColumnValues[colId] as string[]
    },
    [store]
  )

  const addColumnFiltersParams = useCallback(
    (columnDefs: ColDef[]): ColDef[] => {
      return columnDefs.map((item) =>
        getItemWithFilterParams({
          item,
          executionTimeFilter,
          getDistinctColumnValues
        })
      )
    },
    [getDistinctColumnValues, executionTimeFilter.selectedFilterOption]
  )

  const columnDefs = useMemo(() => {
    const currentPreset = [
      ...presets.sharedPresets,
      ...presets.userPresets
    ].find((preset) => preset.Id === lastUsedPresetId)

    let colDefs = mapColumnInfoToColumnDefs({
      columnInfo,
      presetColumnsState: currentPreset?.Data?.state?.columnState
    })

    colDefs = updateColumnsHeaderNames(colDefs, columnsHeaderNames)
    colDefs = addColumnFiltersParams(colDefs)
    return colDefs
  }, [
    columnInfo,
    columnsHeaderNames,
    addColumnFiltersParams,
    componentVisibility?.isFloatingFiltersBarVisible,
    executionTimeFilter.selectedFilterOption
  ])

  useEffect(() => {
    logger.log(LogLevel.Info, 'useSetColumnDefsEffect', { columnDefs })

    addOpenByDefaultPropertyToChildren(columnDefs)

    gridApi?.setColumnDefs(columnDefs)
    // setColumnDefs is expensive, so we need to choose dependencies very carefully.
    // Turns out that equivalent columnDef arrays don't compare equal, so a change in array reference (?)
    // without a change in array content retriggers the effect. The columnDef contents should never change,
    // so we remove this dependency.
  }, [gridApi])

  useEffect(() => {
    const columnState = columnApi?.getColumnState()

    columnDefs.forEach((colDef: ColDef) => {
      const column = columnState?.find((c) => c.colId === colDef.field)

      if (column) {
        columnApi?.setColumnVisible(column.colId, !colDef.hide)
      }
    })

    gridApi?.refreshToolPanel()
  }, [columnDefs])

  useEffect(() => {
    if (resetColumns) {
      columnApi?.setRowGroupColumns([])
      const columnState = columnApi?.getColumnState()

      if (columnState) {
        const updatedColumnState = resetColumnState(
          columnInfo.map((c) => c.column),
          columnState
        )

        columnApi?.applyColumnState({
          state: updatedColumnState,
          applyOrder: true
        })
      }

      // Reset pivot mode and columns
      columnApi?.setPivotMode(false)
      columnApi?.setPivotColumns([])
      columnApi?.setValueColumns([])
      columnApi?.setPivotResultColumns([])

      setResetColumns(false)
    }
  }, [resetColumns])

  return { columnDefs }
}

const getItemWithFilterParams = (args: {
  item: ColDef
  executionTimeFilter: ExecutionTimeFilterState
  getDistinctColumnValues: (colId: string) => string[]
}) => {
  const { item, executionTimeFilter, getDistinctColumnValues } = args
  const { field, filter, headerName } = item
  const filterPlaceholderText =
    filter === AgGridFilterType.DATE ? 'MMM-DD-YYYY' : `Filter ${headerName}`

  if (field && filter === AgGridFilterType.SET) {
    let filterParams: ISetFilterParams = {}
    filterParams = {
      values: (params: SetFilterValuesFuncParams) =>
        params.success(getDistinctColumnValues(field)),
      refreshValuesOnOpen: true,
      valueFormatter: (params: ValueFormatterParams) =>
        params.value !== null ? String(params.value) : ''
    }

    return {
      ...item,
      filterParams
    }
  }

  // in case when executionTimeFilter is selected and filter is date filter
  // we need to set filterParams to filter only in range
  if (
    field === EXECUTION_TIME &&
    executionTimeFilter.selectedFilterOption !== 'all'
  ) {
    return {
      ...item,
      filterParams: {
        filterOptions: ['inRange'],
        maxNumConditions: 1,
        filterPlaceholder: filterPlaceholderText
      }
    }
  }

  return {
    ...item,
    filterParams: {
      ...item.filterParams,
      filterPlaceholder: filterPlaceholderText
    }
  }
}
