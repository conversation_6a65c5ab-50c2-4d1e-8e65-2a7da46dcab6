import { setColumnsHeaderNames } from '@/core/redux/features/report'
import { useSetColumnDefsEffect } from '@/core/redux/hooks'
import { ColumnType } from '@/core/services/riskReport'
import {
  apiPaths,
  combineWrappers,
  createHand<PERSON>,
  getInitializeAppWrapper,
  getInitializeDataWrapper,
  getReduxWrapper,
  handlers,
  setupMockServer,
  wrapWithUrl
} from '@/core/testing'
import {
  getColumnInfoDummy,
  getDistinctColumnValuesResponseDummy,
  getReportDetailsResponseDummy
} from '@/core/testing/dummies'
import { act, renderHook, waitFor } from '@testing-library/react'
import { expect, vi } from 'vitest'
import { EXECUTION_TIME } from '../features/quickFilters/executionTimeFilter/constants'
import { FilterOptions } from '../features/quickFilters/executionTimeFilter/types'
import * as gridUtils from '@/core/utils/grid'
import { setResetColumns } from '../features/grid'
import * as gridHooks from '@/components/Grid/hooks'

vi.mock('@/components/Grid/hooks')

const server = setupMockServer(
  handlers.getUserPreference,
  handlers.latestAsOf,
  handlers.getMetadata,
  handlers.getSummary,
  handlers.getUserProfile,
  handlers.getAllPresets,
  handlers.getUsersByRoles,
  handlers.getClientDetails,
  handlers.connectAggregatorWebSocket,
  handlers.getDistinctColumnValues,
  handlers.getReportDetails
)

// TODO: Flaky test. Should be fixed asap.
describe.skip('useSetColumnDefsEffect', () => {
  const url = '/reports/pnl-live'

  // given
  beforeEach(() => {
    server.use(
      createHandler({
        method: 'post',
        path: apiPaths.getDistinctColumnValues,
        response: getDistinctColumnValuesResponseDummy({
          All: ['All'],
          TradingArea: ['ARNO', 'BLUM']
        })
      }),
      createHandler({
        method: 'get',
        path: apiPaths.getReportDetails,
        response: getReportDetailsResponseDummy({
          columnInfo: [
            getColumnInfoDummy({ column: 'All' }),
            getColumnInfoDummy({ column: 'TradingArea' }),
            getColumnInfoDummy({ column: 'Team' }),
            getColumnInfoDummy({
              column: EXECUTION_TIME,
              type: ColumnType.DateTime,
              filterType: 'Date'
            })
          ]
        })
      })
    )
  })

  it('should build column defs with filter params', async () => {
    // when
    const { result } = renderHook(
      useSetColumnDefsEffect,
      getInitializeAppWrapper(url)
    )

    // then
    await waitFor(() => expect(result.current.columnDefs).toHaveLength(4))

    expect(result.current.columnDefs[0]).toHaveProperty('filterParams')
    expect(result.current.columnDefs[1]).toHaveProperty('filterParams')
    expect(result.current.columnDefs[2]).toHaveProperty('filterParams')
  })

  it('should update columns headers names when columns are rebuilt', async () => {
    // given
    const { wrapper: reduxWrapper, store } = getReduxWrapper({
      report: {
        currentAsOfUtc: '2023-11-15T10:48:22.2649764Z',
        columnsHeaderNames: {
          All: 'All 1',
          Team: 'Team 1',
          TradingArea: 'Trading Area 1'
        },
        selectedTradingAreas: [],
        textSetColumnNames: [],
        preProcessingParamSpecs: [],
        lastAsOfUtcMetadata: {
          receivedTime: null,
          lastDataUpdateTime: null,
          error: null
        }
      }
    })
    const { wrapper: initializeDataWrapper } = getInitializeDataWrapper()
    const wrapper = combineWrappers(
      wrapWithUrl(url),
      reduxWrapper,
      initializeDataWrapper
    )
    const { result } = renderHook(useSetColumnDefsEffect, {
      wrapper
    })

    // then
    await waitFor(() =>
      expect(result.current.columnDefs[0]).toMatchObject({
        field: 'All',
        headerName: 'All 1'
      })
    )
    expect(result.current.columnDefs[1]).toMatchObject({
      field: 'TradingArea',
      headerName: 'Trading Area 1'
    })
    expect(result.current.columnDefs[2]).toMatchObject({
      field: 'Team',
      headerName: 'Team 1'
    })

    // when
    store.dispatch(
      setColumnsHeaderNames({
        All: 'All 2',
        TradingArea: 'Trading Area 2'
      })
    )

    // then
    await waitFor(() =>
      expect(result.current.columnDefs[0]).toMatchObject({
        field: 'All',
        headerName: 'All 2'
      })
    )
    expect(result.current.columnDefs[1]).toMatchObject({
      field: 'TradingArea',
      headerName: 'Trading Area 2'
    })
  })

  it('should set filterParams for date filter when executionTimeFilter is selected', () => {
    // given
    const { wrapper: reduxWrapper } = getReduxWrapper({
      report: {
        columnInfo: [],
        currentAsOfUtc: '2023-11-15T10:48:22.2649764Z',
        columnsHeaderNames: {
          All: 'All 1',
          Team: 'Team 1',
          TradingArea: 'Trading Area 1'
        },
        selectedTradingAreas: [],
        textSetColumnNames: [],
        preProcessingParamSpecs: [],
        lastAsOfUtcMetadata: {
          receivedTime: null,
          lastDataUpdateTime: null,
          error: null
        }
      },
      executionTimeFilter: {
        selectedFilterOption: FilterOptions.T1
      }
    })
    const wrapper = combineWrappers(wrapWithUrl(url), reduxWrapper)
    const { result } = renderHook(useSetColumnDefsEffect, {
      wrapper
    })

    // then
    waitFor(() =>
      expect(result.current.columnDefs[3]).toMatchObject({
        field: EXECUTION_TIME,
        filterParams: {
          filterOptions: ['inRange'],
          maxNumConditions: 1,
          filterPlaceholder: 'MMM-DD-YYYY'
        }
      })
    )
  })

  it('should reset columns when resetColumns is true', async () => {
    vi.spyOn(gridUtils, 'resetColumnState').mockImplementation(vi.fn())
    const setPivotModeSpy = vi.fn()
    const setPivotColumnsSpy = vi.fn()
    const setValueColumnsSpy = vi.fn()
    const setPivotResultColumnsSpy = vi.fn()
    const setRowGroupColumnsSpy = vi.fn()
    const applyColumnStateSpy = vi.fn()
    const getColumnStateSpy = vi.fn().mockReturnValue([
      {
        colId: 'test'
      }
    ])

    const useGridApi = vi.fn().mockReturnValue({
      columnApi: {
        setPivotMode: setPivotModeSpy,
        setPivotColumns: setPivotColumnsSpy,
        setValueColumns: setValueColumnsSpy,
        setPivotResultColumns: setPivotResultColumnsSpy,
        applyColumnState: applyColumnStateSpy,
        getColumnState: getColumnStateSpy,
        setRowGroupColumns: setRowGroupColumnsSpy
      }
    })

    vi.spyOn(gridHooks, 'useGridApi').mockImplementation(useGridApi)

    // given
    const { wrapper: reduxWrapper, store } = getReduxWrapper()
    const { wrapper: initializeDataWrapper } = getInitializeDataWrapper()
    const wrapper = combineWrappers(
      wrapWithUrl(url),
      reduxWrapper,
      initializeDataWrapper
    )

    renderHook(useSetColumnDefsEffect, {
      wrapper
    })

    act(() => {
      store.dispatch(setResetColumns(true))
    })

    await waitFor(() => {
      expect(setPivotModeSpy).toHaveBeenCalledWith(false)
      expect(setPivotColumnsSpy).toHaveBeenCalledWith([])
      expect(setValueColumnsSpy).toHaveBeenCalledWith([])
      expect(setPivotResultColumnsSpy).toHaveBeenCalledWith([])
      expect(setRowGroupColumnsSpy).toHaveBeenCalledWith([])
      expect(applyColumnStateSpy).toHaveBeenCalledWith({
        state: [
          {
            colId: 'test'
          }
        ],
        applyOrder: true
      })
    })
  })

  it('should call addOpenByDefaultPropertyToChildren with columnDefs', async () => {
    const addOpenByDefaultPropertyToChildrenSpy = vi.spyOn(
      gridUtils,
      'addOpenByDefaultPropertyToChildren'
    )
    const useGridApi = vi.fn().mockReturnValue({
      gridApi: {
        setColumnDefs: vi.fn(),
        refreshToolPanel: vi.fn()
      },
      columnApi: {
        getColumnState: vi.fn().mockReturnValue([]),
        setColumnVisible: vi.fn()
      }
    })

    vi.spyOn(gridHooks, 'useGridApi').mockImplementation(useGridApi)

    // Setup wrappers and render the hook
    const { wrapper: reduxWrapper } = getReduxWrapper()
    const { wrapper: initializeDataWrapper } = getInitializeDataWrapper()
    const wrapper = combineWrappers(
      wrapWithUrl(url),
      reduxWrapper,
      initializeDataWrapper
    )

    const { result } = renderHook(useSetColumnDefsEffect, {
      wrapper
    })

    // Wait for the hook to execute and verify the spy was called
    await waitFor(() => {
      expect(addOpenByDefaultPropertyToChildrenSpy).toHaveBeenCalledWith(
        result.current.columnDefs
      )
    })
  })

  it('should not call addOpenByDefaultPropertyToChildren if columnDefs is empty', async () => {
    const addOpenByDefaultPropertyToChildrenSpy = vi.spyOn(
      gridUtils,
      'addOpenByDefaultPropertyToChildren'
    )
    const useGridApi = vi.fn().mockReturnValue({
      gridApi: {
        setColumnDefs: vi.fn(),
        refreshToolPanel: vi.fn()
      },
      columnApi: {
        getColumnState: vi.fn().mockReturnValue([]),
        setColumnVisible: vi.fn()
      }
    })

    vi.spyOn(gridHooks, 'useGridApi').mockImplementation(useGridApi)

    const { wrapper: reduxWrapper } = getReduxWrapper({
      report: {
        columnInfo: [] // Empty columnInfo
      }
    })
    const { wrapper: initializeDataWrapper } = getInitializeDataWrapper()
    const wrapper = combineWrappers(
      wrapWithUrl(url),
      reduxWrapper,
      initializeDataWrapper
    )

    renderHook(useSetColumnDefsEffect, {
      wrapper
    })

    // Wait for the hook to execute and verify the spy was not called
    await waitFor(() => {
      expect(addOpenByDefaultPropertyToChildrenSpy).not.toHaveBeenCalled()
    })
  })
})
