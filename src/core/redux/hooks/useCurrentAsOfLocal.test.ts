import { getReduxWrapper } from '@/core/testing'
import { renderHook } from '@testing-library/react'
import { useCurrentAsOfLocal } from './useCurrentAsOfLocal'

describe('useCurrentAsOfLocal', () => {
  it('should return currentAsOfLocal and currentAsOfUtc', () => {
    // given
    const { wrapper } = getReduxWrapper({
      report: {
        currentAsOfUtc: '2021-07-20T12:00:00.0000000Z'
      }
    })

    const { result } = renderHook(useCurrentAsOfLocal, {
      wrapper
    })

    // then
    expect(result.current.currentAsOfLocal).toEqual('07/20/2021, 2:00:00 PM')
    expect(result.current.currentAsOfUtc).toEqual('07/20/2021, 12:00:00 PM')
  })
})
