import { useDispatch } from 'react-redux'
import { AppDispatch } from '@/core/redux/store'

/**
 * Use throughout the app instead of plain `useDispatch`.
 * @example
 * const dispatch = useAppDispatch()
 * dispatch({ type: 'test' })
 * @description
 * The dispatch inside `useAppDispatch` is fully typed as `AppDispatch`.
 */
export const useAppDispatch: () => AppDispatch = () =>
  useDispatch<AppDispatch>()
