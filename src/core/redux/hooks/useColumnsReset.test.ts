import { ReduxWrapper } from '@/core/testing'
import { renderHook } from '@testing-library/react'
import { vi } from 'vitest'
import { useColumnsReset } from './useColumnsReset'

const renderWithRedux = (isResetColumns: boolean) =>
  renderHook(() => useColumnsReset(), {
    wrapper: ReduxWrapper({
      grid: {
        resetColumns: isResetColumns
      }
    })
  })

describe('useColumnsReset', () => {
  afterEach(() => {
    vi.clearAllMocks()
  })

  it('should Initialize resetcolumns to false and trigger SetResetColumns to true ', async () => {
    // Render the hook with initial value of false
    const { result } = renderWithRedux(false)

    await (() => {
      expect(result.current.resetColumns).toBe(false)
      expect(result.current.setResetColumns(true)).toHaveBeenCalled()
      expect(result.current.resetColumns).toBe(true)
    })
  })

  it('should Initialize resetcolumns to true and trigger SetResetColumns to false ', async () => {
    // Render the hook with initial value of true
    const { result } = renderWithRedux(true)

    await (() => {
      expect(result.current.resetColumns).toBe(true)
      expect(result.current.setResetColumns(false)).toHaveBeenCalled()
      expect(result.current.resetColumns).toBe(false)
    })
  })
})
