import { useAppDispatch, useAppSelect } from '@/core/redux/hooks'
import { setColumnToRename as setColumn } from '@/core/redux/features/grid'

export function useColumnToRename() {
  const columnToRename = useAppSelect((state) => state.grid.columnToRename)
  const dispatch = useAppDispatch()

  const setColumnToRename = (columnId: string) => {
    dispatch(setColumn(columnId))
  }

  const cancelColumnRenaming = () => {
    dispatch(setColumn(null))
  }

  return {
    columnToRename,
    setColumnToRename,
    cancelColumnRenaming
  }
}
