import { PayloadAction, createSlice } from '@reduxjs/toolkit'
import { IColumnInfo } from '@/core/services/riskReport'

/**
 * `null` when there is no active editing.
 */
type ColumnIdToRename = string | null

export interface ToggleGroupExpansionPayload {
  groupId: string
  isExpanded: boolean
}

export interface GridState {
  columnInfo: IColumnInfo[]
  keyColumnNames: string[]
  columnToRename: ColumnIdToRename
  expandedGroupsIds: string[]
  resetColumns: boolean
  isPivotModeEnabled: boolean
  pivotResultsColumnsOrdering: string[] | null
}

export const initialGridState: GridState = {
  columnInfo: [],
  keyColumnNames: [],
  columnToRename: null,
  expandedGroupsIds: [],
  resetColumns: false,
  isPivotModeEnabled: false,
  pivotResultsColumnsOrdering: null
}

const setColumnToRenameReducer = (
  state: GridState,
  { payload }: PayloadAction<ColumnIdToRename>
) => {
  state.columnToRename = payload
}

const setResetColumnsReducer = (
  state: GridState,
  { payload }: PayloadAction<boolean>
) => {
  state.resetColumns = payload
}

const toggleGroupExpansionReducer = (
  state: GridState,
  { payload }: PayloadAction<ToggleGroupExpansionPayload>
) => {
  const { groupId, isExpanded } = payload

  const expandedGroupIds = new Set(state.expandedGroupsIds)

  if (expandedGroupIds.has(groupId) && !isExpanded) {
    expandedGroupIds.delete(groupId)
  } else if (!expandedGroupIds.has(groupId) && isExpanded) {
    expandedGroupIds.add(groupId)
  }

  state.expandedGroupsIds = Array.from(expandedGroupIds)
}

const setExpandedGroupsIdsReducer = (
  state: GridState,
  { payload }: PayloadAction<string[]>
) => {
  state.expandedGroupsIds = payload
}

export const gridSlice = createSlice({
  name: 'grid',
  initialState: initialGridState,
  reducers: {
    setResetColumns: setResetColumnsReducer,
    setColumnToRename: setColumnToRenameReducer,
    setExpandedGroupsIds: setExpandedGroupsIdsReducer,
    toggleGroupExpansion: toggleGroupExpansionReducer,
    setIsPivotModeEnabled: (state, { payload }: PayloadAction<boolean>) => {
      state.isPivotModeEnabled = payload
    },
    setPivotResultsColumnsOrdering: (
      state,
      { payload }: PayloadAction<string[] | null>
    ) => {
      state.pivotResultsColumnsOrdering = payload
    }
  }
})

export const {
  setResetColumns,
  setColumnToRename,
  setExpandedGroupsIds,
  toggleGroupExpansion,
  setIsPivotModeEnabled,
  setPivotResultsColumnsOrdering
} = gridSlice.actions
export const gridReducer = gridSlice.reducer
