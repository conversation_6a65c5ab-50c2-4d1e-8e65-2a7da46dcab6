import { AgGridFilterOperator } from '@/core/services/grid'
import { EXECUTION_TIME, defaultDateFrom, defaultDateTo } from '../../constants'
import { FilterOptions } from '../../types'
import { executionTimeFilterSelector } from '../executionTimeFilterSelector'
import { RootState } from '@/core/redux/store'

describe('executionTimeFilterSelector', () => {
  test('should return the execution time filter', () => {
    const state = {
      executionTimeFilter: {
        columnName: EXECUTION_TIME,
        selectedValue: FilterOptions.ALL,
        filter: {
          type: AgGridFilterOperator.InRange,
          dateFrom: defaultDateFrom,
          dateTo: defaultDateTo
        }
      }
    } as unknown as RootState

    const result = executionTimeFilterSelector(state)

    expect(result).toEqual({
      columnName: EXECUTION_TIME,
      selectedValue: FilterOptions.ALL,
      filter: {
        type: AgGridFilterOperator.InRange,
        dateFrom: defaultDateFrom,
        dateTo: defaultDateTo
      }
    })
  })

  test('should return undefined if the execution time filter is not set', () => {
    const state = {} as unknown as RootState
    const result = executionTimeFilterSelector(state)
    expect(result).toBeUndefined()
  })
})
