import { ReportState } from '@/core/redux/features/report/reportSlice.js'
import { setupStore } from '@/core/redux/store'
import { AgGridFilterOperator } from '@/core/services/grid/types'
import { waitFor } from '@testing-library/react'
import { ExecutionTimeFilterState } from '../../types'
import { onExecutionTimeDatesChangesThunk } from '../onExecutionTimeDatesChangedThunk'
import { getLocalMidnight } from '@/core/utils/date'

describe('onExecutionTimeDatesChangesThunk', () => {
  const store = setupStore({
    report: {
      currentAsOfUtc: '2022-01-01T00:00:00.000Z'
    } as unknown as ReportState
  })

  test('should set the correct value', async () => {
    const currentLocalMidnight = getLocalMidnight('2022-01-01T00:00:00.000Z')
    const dateFrom = '2022-01-01T00:00:00.000Z'
    const dateTo = '2022-12-31T23:59:59.999Z'

    store.dispatch(
      onExecutionTimeDatesChangesThunk({
        dateFrom,
        dateTo
      })
    )

    await waitFor(() => {
      expect(store.getState().executionTimeFilter).toStrictEqual({
        currentLocalMidnight,
        filter: {
          type: AgGridFilterOperator.InRange,
          dateFrom,
          dateTo
        }
      } as ExecutionTimeFilterState)
    })
  })
})
