import { ReportState } from '@/core/redux/features/report'
import { setupStore } from '@/core/redux/store'
import { AgGridFilterOperator } from '@/core/services/grid'
import { waitFor } from '@testing-library/react'
import { FilterOptions } from '../../types'
import { onExecutionTimeFilterRestoredFromPresetThunk } from '../onExecutionTImeFilterRestoredFromPresetThunk'
import { getLocalMidnight } from '@/core/utils/date'

describe('onExecutionTImeFilterRestoredFromPresetThunk', () => {
  const store = setupStore({
    report: {
      currentAsOfUtc: '2022-01-01T00:00:00.000Z'
    } as unknown as ReportState
  })

  test('should set the correct value', async () => {
    const currentLocalMidnight = getLocalMidnight('2022-01-01T00:00:00.000Z')

    const filterState = {
      currentLocalMidnight,
      selectedFilterOption: FilterOptions.DATES,
      filter: {
        type: AgGridFilterOperator.InRange,
        dateFrom: '2022-01-01T00:00:00.000Z',
        dateTo: '2022-12-31T23:59:59.999Z'
      }
    }

    store.dispatch(onExecutionTimeFilterRestoredFromPresetThunk(filterState))

    await waitFor(() => {
      expect(store.getState().executionTimeFilter).toStrictEqual(filterState)
    })
  })

  test('should set the correct value with different dates', async () => {
    const currentLocalMidnight = getLocalMidnight('2022-01-01T00:00:00.000Z')

    const filterState = {
      currentLocalMidnight,
      selectedFilterOption: FilterOptions.DATES,
      filter: {
        type: AgGridFilterOperator.InRange,
        dateFrom: '2022-01-01T00:00:00.000Z',
        dateTo: '2022-12-31T23:59:59.999Z'
      }
    }

    store.dispatch(onExecutionTimeFilterRestoredFromPresetThunk(filterState))

    await waitFor(() => {
      expect(store.getState().executionTimeFilter).toStrictEqual(filterState)
    })
  })

  test('should set the correct value with different dates and selectedFilterOption', async () => {
    const currentLocalMidnight = getLocalMidnight('2022-01-01T00:00:00.000Z')

    const filterState = {
      currentLocalMidnight,
      selectedFilterOption: FilterOptions.ALL,
      filter: {
        type: AgGridFilterOperator.InRange,
        dateFrom: '2022-01-01T00:00:00.000Z',
        dateTo: '2022-12-31T23:59:59.999Z'
      }
    }

    store.dispatch(onExecutionTimeFilterRestoredFromPresetThunk(filterState))

    await waitFor(() => {
      expect(store.getState().executionTimeFilter).toStrictEqual(filterState)
    })
  })

  test('should set the correct value with different dates and selectedFilterOption', async () => {
    const currentLocalMidnight = getLocalMidnight('2022-01-01T00:00:00.000Z')

    const filterState = {
      currentLocalMidnight,
      selectedFilterOption: FilterOptions.ALL,
      filter: {
        type: AgGridFilterOperator.InRange,
        dateFrom: '2022-01-01T00:00:00.000Z',
        dateTo: '2022-12-31T23:59:59.999Z'
      }
    }

    store.dispatch(onExecutionTimeFilterRestoredFromPresetThunk(filterState))

    await waitFor(() => {
      expect(store.getState().executionTimeFilter).toStrictEqual(filterState)
    })
  })
})
