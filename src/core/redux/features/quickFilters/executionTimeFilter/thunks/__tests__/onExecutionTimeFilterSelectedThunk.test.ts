import { ReportState } from '@/core/redux/features/report'
import { setupStore } from '@/core/redux/store'
import { AgGridFilterOperator } from '@/core/services/grid'
import { getLocalMidnight } from '@/core/utils/date'
import { waitFor } from '@testing-library/react'
import { defaultDateFrom, defaultDateTo } from '../../constants'
import { FilterOptions } from '../../types'
import { onExecutionTimeFilterSelectedThunk } from '../onExecutionTimeFilterSelectedThunk'

describe('onExecutionTimeFilterSelectedThunk', () => {
  const store = setupStore({
    report: {
      currentAsOfUtc: '2022-01-01T00:00:00.000Z'
    } as unknown as ReportState
  })

  test('should set today', async () => {
    const selectedFilterOption = FilterOptions.TODAY
    store.dispatch(onExecutionTimeFilterSelectedThunk(selectedFilterOption))

    await waitFor(() => {
      const result = store.getState().executionTimeFilter
      const expectedDateTo = new Date(defaultDateTo)

      const currentLocalMidnight = getLocalMidnight(
        store.getState().report.currentAsOfUtc
      )

      expect(result).toEqual({
        currentLocalMidnight,
        selectedFilterOption,
        filter: {
          type: AgGridFilterOperator.InRange,
          dateFrom: currentLocalMidnight,
          dateTo: expectedDateTo.toISOString()
        }
      })
    })
  })

  test('should set T1', async () => {
    const selectedFilterOption = FilterOptions.T1
    store.dispatch(onExecutionTimeFilterSelectedThunk(selectedFilterOption))

    await waitFor(() => {
      const result = store.getState().executionTimeFilter

      const currentLocalMidnight = getLocalMidnight(
        store.getState().report.currentAsOfUtc
      )

      const expectedDateFrom = new Date(store.getState().report.currentAsOfUtc)
      expectedDateFrom.setHours(0, 0, 0, 0)
      expectedDateFrom.setDate(expectedDateFrom.getDate() - 1)

      if (expectedDateFrom.getDay() === 0) {
        expectedDateFrom.setDate(expectedDateFrom.getDate() - 2)
      }

      if (expectedDateFrom.getDay() === 6) {
        expectedDateFrom.setDate(expectedDateFrom.getDate() - 1)
      }

      const expectedDateTo = new Date(expectedDateFrom)
      expectedDateTo.setHours(23, 59, 59, 999)

      expect(result).toEqual({
        currentLocalMidnight,
        selectedFilterOption,
        filter: {
          type: AgGridFilterOperator.InRange,
          dateFrom: expectedDateFrom.toISOString(),
          dateTo: expectedDateTo.toISOString()
        }
      })
    })
  })

  test('should set all', async () => {
    const selectedFilterOption = FilterOptions.ALL

    const currentLocalMidnight = getLocalMidnight(
      store.getState().report.currentAsOfUtc
    )

    store.dispatch(onExecutionTimeFilterSelectedThunk(selectedFilterOption))

    await waitFor(() => {
      const result = store.getState().executionTimeFilter

      expect(result).toEqual({
        currentLocalMidnight,
        selectedFilterOption,
        filter: {
          type: AgGridFilterOperator.InRange,
          dateFrom: new Date(defaultDateFrom).toISOString(),
          dateTo: new Date(defaultDateTo).toISOString()
        }
      })
    })
  })
})
