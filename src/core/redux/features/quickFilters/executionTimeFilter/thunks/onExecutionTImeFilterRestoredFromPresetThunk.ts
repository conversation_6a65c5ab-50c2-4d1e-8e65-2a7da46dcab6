import { RootState } from '@/core/redux/store'
import { createAsyncThunk } from '@reduxjs/toolkit'
import { setExecutionTimeFilterState } from '../executionTimeFilterSlice'
import { ExecutionTimeFilterState, FilterOptions } from '../types'
import { onExecutionTimeDatesChangesThunk } from './onExecutionTimeDatesChangedThunk'
import { onExecutionTimeFilterSelectedThunk } from './onExecutionTimeFilterSelectedThunk'
import { getLocalMidnight } from '@/core/utils/date'

export const onExecutionTimeFilterRestoredFromPresetThunk = createAsyncThunk(
  'executionTimeFilter/onFilterRestoreFromPreset',
  (filterState: ExecutionTimeFilterState, { getState, dispatch }) => {
    const currentAsOfUtc = (getState() as ReturnType<() => RootState>).report
      .currentAsOfUtc
    const currentLocalMidnight = getLocalMidnight(currentAsOfUtc)

    if (filterState.currentLocalMidnight === currentLocalMidnight) {
      dispatch(setExecutionTimeFilterState(filterState))
      return
    }

    if (
      filterState.currentLocalMidnight !== currentLocalMidnight &&
      filterState.selectedFilterOption === FilterOptions.DATES
    ) {
      dispatch(
        onExecutionTimeDatesChangesThunk({
          dateFrom: filterState.filter?.dateFrom,
          dateTo: filterState.filter?.dateTo,
          selectedFilter: FilterOptions.DATES
        })
      )

      return
    }

    if (
      filterState.currentLocalMidnight !== currentLocalMidnight &&
      filterState.selectedFilterOption
    ) {
      // Delay the dispatch to ensure the filter state is set before QF is applied
      setTimeout(() => {
        dispatch(
          onExecutionTimeFilterSelectedThunk(
            filterState.selectedFilterOption ?? FilterOptions.ALL
          )
        )
      }, 0)

      return
    }
  }
)
