import { RootState } from '@/core/redux/store'
import { createAsyncThunk } from '@reduxjs/toolkit'
import { FilterOptions } from '../types'

export const onExecutionTimeFilterSelectedThunk = createAsyncThunk(
  'onExecutionTimFilterSelectThunk',
  async (selectedFilter: FilterOptions, { getState }) => {
    const currentAsOfUtc = (getState() as RootState).report.currentAsOfUtc

    return { currentAsOfUtc, selectedFilter }
  }
)
