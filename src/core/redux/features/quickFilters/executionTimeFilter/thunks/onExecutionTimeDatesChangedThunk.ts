import { RootState } from '@/core/redux/store'
import { createAsyncThunk } from '@reduxjs/toolkit'
import { FilterOptions } from '../types'

type Args = {
  dateFrom?: string | undefined
  dateTo?: string | undefined
  selectedFilter?: FilterOptions | undefined
}

export const onExecutionTimeDatesChangesThunk = createAsyncThunk(
  'onExecutionTimeDatesChangesThunk',
  ({ dateFrom, dateTo, selectedFilter }: Args, { getState }) => {
    const currentAsOfUtc = (getState() as RootState).report.currentAsOfUtc

    return {
      dateFrom,
      dateTo,
      currentAsOfUtc,
      selectedFilter
    }
  }
)
