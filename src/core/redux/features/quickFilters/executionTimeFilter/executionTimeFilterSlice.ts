import { PayloadAction, createSlice } from '@reduxjs/toolkit'
import { ExecutionTimeFilterState, FilterOptions } from './types'
import { onExecutionTimeFilterSelectedThunk } from './thunks/onExecutionTimeFilterSelectedThunk'
import { onExecutionTimeDatesChangesThunk } from './thunks/onExecutionTimeDatesChangedThunk'
import { getLocalMidnight } from '@/core/utils/date'
import { AgGridFilterOperator } from '@/core/services/grid'
import { defaultDateFrom, defaultDateTo } from './constants'
import { parseISO } from 'date-fns'

export const executionTimeFilterSlice = createSlice({
  name: 'executionTimeFilter',
  initialState: {} as ExecutionTimeFilterState,
  reducers: {
    setExecutionTimeFilterState: setStateReducer
  },
  extraReducers: (builder) => {
    builder.addCase(
      onExecutionTimeFilterSelectedThunk.fulfilled,
      executionTimeFilterSelectedReducer
    )
    builder.addCase(
      onExecutionTimeDatesChangesThunk.fulfilled,
      executionTimeDatesChangesReducer
    )
  }
})

function executionTimeDatesChangesReducer(
  state: ExecutionTimeFilterState,
  {
    payload
  }: PayloadAction<{
    currentAsOfUtc: string
    dateFrom?: string
    dateTo?: string
    selectedFilter: FilterOptions | undefined
  }>
) {
  const { dateFrom, dateTo, currentAsOfUtc, selectedFilter } = payload

  state.currentLocalMidnight = getLocalMidnight(currentAsOfUtc)
  state.filter = {
    type: AgGridFilterOperator.InRange,
    dateFrom: dateFrom ?? state.filter?.dateFrom,
    dateTo: dateTo ?? state.filter?.dateTo
  }
  selectedFilter && (state.selectedFilterOption = selectedFilter)
}

function executionTimeFilterSelectedReducer(
  state: ExecutionTimeFilterState,
  {
    payload
  }: PayloadAction<{
    selectedFilter: FilterOptions
    currentAsOfUtc: string
  }>
) {
  const { selectedFilter, currentAsOfUtc } = payload
  let filter = {}

  if (selectedFilter === FilterOptions.ALL) {
    filter = {
      type: AgGridFilterOperator.InRange,
      dateFrom: new Date(defaultDateFrom).toISOString(),
      dateTo: new Date(defaultDateTo).toISOString()
    }
  }

  if (selectedFilter === FilterOptions.TODAY) {
    const startDate = parseISO(currentAsOfUtc)
    startDate.setHours(0, 0, 0, 0)

    filter = {
      type: AgGridFilterOperator.InRange,
      dateFrom: startDate.toISOString(),
      dateTo: new Date(defaultDateTo).toISOString()
    }
  }

  // Here we should get the date before the currentAsOfUtc date
  if (selectedFilter === FilterOptions.T1) {
    const startDate = parseISO(currentAsOfUtc)
    startDate.setHours(0, 0, 0, 0)
    startDate.setDate(startDate.getDate() - 1)

    // If the date is Sunday(0), we should get the date from Friday
    if (startDate.getDay() === 0) {
      startDate.setDate(startDate.getDate() - 2)
    }

    // If the date is Saturday, we should get the date from Friday
    if (startDate.getDay() === 6) {
      startDate.setDate(startDate.getDate() - 1)
    }

    const endDate = new Date(startDate)
    endDate.setHours(23, 59, 59, 999)
    endDate.setMilliseconds(999)

    filter = {
      type: AgGridFilterOperator.InRange,
      dateFrom: startDate.toISOString(),
      dateTo: endDate.toISOString()
    }
  }

  state.currentLocalMidnight = getLocalMidnight(currentAsOfUtc)
  state.filter = filter
  state.selectedFilterOption = selectedFilter
}

function setStateReducer(
  state: ExecutionTimeFilterState,
  { payload }: PayloadAction<ExecutionTimeFilterState>
) {
  state.currentLocalMidnight = payload.currentLocalMidnight
  state.filter = payload.filter
  state.selectedFilterOption = payload.selectedFilterOption
}

export const executionTimeFilterReducer = executionTimeFilterSlice.reducer
export const { setExecutionTimeFilterState } = executionTimeFilterSlice.actions
