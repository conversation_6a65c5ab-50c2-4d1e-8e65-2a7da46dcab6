import { env } from '@/core/config'
import { addPivotResultCols } from '@/core/redux/features/report/utils/pivot.build'
import { ITableSubsetRequest } from '@/core/services/riskReport'
import { getRiskReportApi } from '@/core/services/riskReport/riskReportApi'
import {
  filterOutEmptyColumnsVO,
  getRowRange,
  mapAgGridFilterModelsToFilterModel,
  mapColumnsToTableRows,
  mapColumnsVOToColumnsAggregation,
  mapColumnsVOToGroupingColumns,
  mapGridSortModelToApiSortModel
} from '@/core/utils/grid'
import { logger } from '@/core/utils/logger'
import { IServerSideGetRowsParams } from '@ag-grid-community/core'
import { Action, createAsyncThunk, ThunkDispatch } from '@reduxjs/toolkit'
import { AxiosError } from 'axios'
import { RootState } from '../../store'
import {
  addNotification,
  removeNotification,
  showErrorModal
} from '../notifications'
import { EXECUTION_TIME } from '../quickFilters/executionTimeFilter/constants'
import { FilterOptions } from '../quickFilters/executionTimeFilter/types'
import { PIVOT_SEPARATOR } from './types'
import { recordBackendColumnOrder, setColumnsStatus } from './reportSlice'
import { applyPinnedColumnsThunk } from '../presets/thunk/applyPinnedColumnsThunk'
import {
  columnsStatusSelector,
  getSnapshot,
  isLiveModeOrParentLiveMode,
  reportPathSelector
} from './selectors'
import {} from './reportMetadata/selectors'

/**
 * This thunk is dispatched on AG Grid's getRows event.
 * It is responsible for fetching the data from the API and
 * returning it to the grid.
 */
export const getSubsetThunk = createAsyncThunk(
  'riskReport/getSubset',
  async (
    params: IServerSideGetRowsParams<unknown, unknown>,
    { getState, dispatch }
  ) => {
    const state: RootState = getState() as RootState

    const {
      startRow,
      endRow,
      valueCols,
      sortModel,
      rowGroupCols,
      groupKeys,
      filterModel,
      pivotMode,
      pivotCols
    } = params.request

    try {
      const { currentAsOfUtc, selectedTradingAreas } = state.report
      const tableSubsetRequest: ITableSubsetRequest = {
        ...getRowRange(startRow, endRow),
        aggregationColumns: mapColumnsVOToColumnsAggregation(
          filterOutEmptyColumnsVO(valueCols)
        ),
        sortBy: mapGridSortModelToApiSortModel(sortModel),
        groupingColumns: mapColumnsVOToGroupingColumns(
          filterOutEmptyColumnsVO(rowGroupCols)
        ),
        groupingKey: groupKeys.map(String),
        filters: mapAgGridFilterModelsToFilterModel(filterModel, {
          [EXECUTION_TIME]:
            state.executionTimeFilter.selectedFilterOption !==
            FilterOptions.AG_GRID_FILTER
              ? state.executionTimeFilter.filter
              : undefined
        }),
        pivotMode,
        pivotResultFieldSeparator: pivotMode ? PIVOT_SEPARATOR : undefined,
        pivotColumns: pivotMode
          ? pivotCols.map((column) => column.id)
          : undefined
      }

      const preProcessingParams = state.report.preProcessingParams
      const snapshot = getSnapshot(state)

      const payload = {
        snapshot,
        tableSubsetRequest,
        tradingAreas: selectedTradingAreas || [],
        preProcessingParams
      }

      // EOD request goes to uibackend-timetravel), and reportName for the route is `eodReportType` key from `reportDetails` endpoint
      const reportPath = reportPathSelector(state)

      const riskReportApi = getRiskReportApi(isLiveModeOrParentLiveMode(state))
      const response = await riskReportApi.getSubset(reportPath, payload)

      if (response) {
        const { data, pivotResultColumns, totalRowCount, columnStatus } =
          response
        const currentColumnsStatus = columnsStatusSelector(state)

        // compare the current column status with the new column status
        if (
          columnStatus &&
          Object.keys(columnStatus).length > 0 &&
          JSON.stringify(columnStatus) !== JSON.stringify(currentColumnsStatus)
        ) {
          dispatch(setColumnsStatus(columnStatus))
        }

        const rowData = mapColumnsToTableRows(data.Columns).map((row) => ({
          ...row,
          asOfUtc: currentAsOfUtc // adding asOfUtc to each row for refresh consistency check
        }))

        if (tableSubsetRequest.pivotMode === false) {
          const columns = data.Columns?.map((obj) => {
            return obj.Name
          })
          dispatch(recordBackendColumnOrder(columns))
        } else if (
          (tableSubsetRequest.groupingKey === undefined ||
            tableSubsetRequest.groupingKey.length == 0) && // Only record pivoting result column order when we receive the full set, i.e. for the GetSubset request of the root group (with no empty grouping key)
          pivotResultColumns !== undefined &&
          pivotResultColumns.length > 0
        ) {
          dispatch(recordBackendColumnOrder(pivotResultColumns))
        }

        if (state.report.supportsPivotMode) {
          /*
              @Tristan, Faber
              https://point72.slack.com/archives/C05AB6PH928/p1718783969867579?thread_ts=1718698701.970379&cid=C05AB6PH92

              I had some more thoughts on how/when to use setPivotResultColumns.
              I think when the request is for the empty groupingKey=[], you use setPivotResultColumns
              to replace the current set of result columns with whatever the request returns.
              That is because the empty groupingKey corresponds to the top level request,
              which always contains the aggregates of all rows, and thus has always the entire set of pivot result columns.

              When the request is for a non-empty groupingKey, you use getPivotResultColumns to get
              the current set of result columns, then merge this with the result columns from the request,
              and setPivotResultColumns the merged set of result columns. Otherwise, when the empty groupingKey request
              returns last in a set of concurrent requests, or not at all when expanding a group,
              columns would be removed as we observe right now.
              In the latter case, it is important that the merging of columns happens with the correct column ordering.
              The ordering of columns in the backend response must be observed. The merging needs to be done
              in a way that preserves both the column ordering of the previous column set obtained by getPivotResultColumns,
              and the ordering of columns from the latest backend response. Otherwise, the column order jumps around
              on the screen during a refresh. Note that the backend still needs a change to guarantee consistency
              of pivot result column ordering between requests.
          */

          // setTimeout is used to ensure that the pivot result columns are added after the grid has finished rendering
          // to make it more performant
          setTimeout(() => {
            addPivotResultCols({
              request: params.request,
              response: response,
              api: params.columnApi,
              columnsInfo: state.report.columnInfo,
              pivotResultsColumnsOrdering:
                state.grid.pivotResultsColumnsOrdering
            })

            dispatch(applyPinnedColumnsThunk(params.columnApi))
          })
        }

        params.success({
          rowData,
          rowCount: totalRowCount
        })
      }
    } catch (err) {
      params.fail()

      const context = {
        service: 'riskReport',
        function: 'getSubsetThunk',
        request: params.request,
        state
      }

      if (err instanceof Error) {
        let errorMessage = 'Failed to fetch data'
        let isUserActionRequired = false

        if (err instanceof AxiosError) {
          errorMessage = err.response?.data as string

          if (err.code === AxiosError.ERR_BAD_REQUEST) {
            // display modal
            isUserActionRequired = true
            logger.warn(
              'Bad request triggered through getSubsetThunk',
              context,
              err
            )
          } else if (err.code === AxiosError.ERR_NETWORK) {
            logger.error(
              'Unable to get the grid data which got triggered through getSubsetThunk call. Data fetch call failed with a message Failed to fetch. Parameters and error details are as follows: ',
              context,
              err
            )
          } else {
            logger.error(
              'Unable to get the grid data which got triggered through getSubsetThunk call. Data Fetch call failed with parameters that are used in API. Parameters and error details are as follows: ',
              context,
              err
            )
          }
        } else {
          errorMessage = err.message
        }

        if (isUserActionRequired) {
          dispatch(showErrorModal(errorMessage))
        } else {
          showNotification(errorMessage, dispatch)
        }
      }
    }
  }
)

const showNotification = (
  errorMessage: string,
  dispatch: ThunkDispatch<unknown, unknown, Action>
) => {
  const notificationId = Date.now().toString()

  dispatch(
    addNotification({
      text: errorMessage,
      intent: 'danger',
      id: notificationId
    })
  )

  setTimeout(() => {
    dispatch(removeNotification(notificationId))
  }, env.toastDisplayTimeMs)
}
