import { IPreProcessingParamName } from '@/core/services/riskReport'

export type PreProcessingReportParams = Record<IPreProcessingParamName, string>

export type PreProcessingParams = {
  originatingContext?: OriginatingContext
  reportParams?: PreProcessingReportParams
}

export type DrillThroughContext = {
  originatingContext: OriginatingContext
  selectedTradingAreas: string[]
  currentAsOf: string | null
  isLiveModeEnabled: boolean
  drillThroughViewLiveUpdateAsOfUtcMetadataOrigin: string
  channel: string
}

export type ContextAction = {
  Description: string
  Type: string
  Params: {
    Glue42ViewAppName: string
    ViewURL: string
    OriginatingGroupKey: string
    DrillThroughViewAsOfUtc: string
    DrillThroughViewLiveUpdateAsOfUtcMetadataOrigin: string
  }
}

export type ContextActions = {
  Actions: ContextAction[]
}

export type OriginatingContext = {
  reportName: string
  groupKey: Record<string, string>
  filters: {
    column: string
    logicalOperator: 'Or' | 'And'
    filters: { operator: string; value: string }[]
  }[]
  reportAsOfUtc: string
}

export enum ContextActionsTypes {
  openDrillThroughView = 'openDrillThroughView'
}

export type LiveUpdateDelayType = 'warning' | 'error' | undefined

export type ColumnsHeaderNames = Record<string, string>

export const PIVOT_SEPARATOR = '%'
