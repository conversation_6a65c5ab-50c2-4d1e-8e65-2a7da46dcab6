import { AppStartListening } from '@/core/redux/middleware/listenerMiddleware'
import { currentAsOfUtcSelector } from '../selectors'
import { getKnowledgeAsOfValuesThunk } from '../reportMetadata/thunk/getKnowledgeAsOfValuesThunk'
import { isKnowledgeAsOfUIVisibleSelector } from '../reportMetadata/selectors'

export const addKnowledgeAsOfValuesListener = (
  startListening: AppStartListening
) => {
  startListening({
    predicate: (_, currentState, previousState) => {
      const currentValue = currentAsOfUtcSelector(currentState)
      const isValueChanged =
        currentValue !== currentAsOfUtcSelector(previousState)
      const isKnowledgeAsOfUIVisible =
        isKnowledgeAsOfUIVisibleSelector(currentState)

      return Boolean(currentValue) && isValueChanged && isKnowledgeAsOfUIVisible
    },
    effect: async (_, { dispatch }) => {
      dispatch(getKnowledgeAsOfValuesThunk())
    }
  })
}
