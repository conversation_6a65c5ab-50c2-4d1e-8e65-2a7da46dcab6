import { ThunkDispatch } from '@reduxjs/toolkit'
import {
  isLiveModeEnabledSelector,
  setLastAsOfUtcError,
  startLatestAsOfUtcListener
} from '..'
import {
  POLLING_INTERVAL_SECONDS,
  startAsOfUtc<PERSON>ongPoll,
  stopAsOfUtcLongPoll
} from '../thunk/asOfUtcLongPolling'
import {
  longPollGetLatestAsOfUtcThunk,
  GetLatestAsOfUtcThunkRejectResponse
} from '../thunk/longPollGetLatestAsOfUtcThunk'
import { RootState } from '@/core/redux/store'
import { AppStartListening } from '@/core/redux/middleware/listenerMiddleware'

let retryAfterSeconds = POLLING_INTERVAL_SECONDS

export const addLatestAsOfUtcListeners = (
  startListening: AppStartListening
) => {
  // Start long polling
  startListening({
    actionCreator: startLatestAsOfUtcListener,
    effect: async (_, { dispatch }) => {
      retryAfterSeconds = POLLING_INTERVAL_SECONDS
      startAsOfUtcLongPoll(dispatch)
    }
  })

  // Stop long polling
  startListening({
    predicate: (_, currentState, previousState) => {
      const isLiveModeEnabled = isLiveModeEnabledSelector(currentState)
      const isChanged =
        isLiveModeEnabled !== isLiveModeEnabledSelector(previousState)

      return Boolean(!isLiveModeEnabled) && isChanged
    },
    effect: async () => {
      stopAsOfUtcLongPoll()
    }
  })

  // Handle retry logic
  startListening({
    actionCreator: longPollGetLatestAsOfUtcThunk.rejected,
    effect: async ({ payload }, { dispatch, getState, delay }) => {
      if (!isRejectedLatestAsOfPayload(payload)) return

      const isCancelled = payload.requestWasCancelled
      const isLiveModeDisabled = !getState().report.isLiveModeEnabled

      if (isCancelled || isLiveModeDisabled) {
        return
      }

      retryAfterSeconds = getRetryInterval(payload)
      const { report } = getState()

      handleSubscriptionErrors(payload, dispatch)

      if (report.isLiveModeEnabled) {
        await delay(retryAfterSeconds * 1000)
        startAsOfUtcLongPoll(dispatch)
      }
    }
  })

  // Reset retry interval
  startListening({
    actionCreator: longPollGetLatestAsOfUtcThunk.fulfilled,
    effect: async () => {
      retryAfterSeconds = POLLING_INTERVAL_SECONDS
    }
  })

  // Handle long poll interval
  startListening({
    actionCreator: longPollGetLatestAsOfUtcThunk.fulfilled,
    effect: async (_, { dispatch, getState }) => {
      const { report } = getState()

      // prevent long polling in test environment
      const isTestEnvironment = process.env.NODE_ENV === 'test'

      if (report.isLiveModeEnabled && !isTestEnvironment) {
        startAsOfUtcLongPoll(dispatch)
      }
    }
  })
}

const isRejectedLatestAsOfPayload = (
  payload: unknown
): payload is GetLatestAsOfUtcThunkRejectResponse => {
  return (
    typeof payload === 'object' &&
    payload !== null &&
    'retryAfterSecondsFromHeader' in payload &&
    'statusCode' in payload &&
    'requestWasCancelled' in payload
  )
}

const handleSubscriptionErrors = (
  payload: GetLatestAsOfUtcThunkRejectResponse,
  dispatch: ThunkDispatch<RootState, unknown, any>
) => {
  const { retryAfterSecondsFromHeader, statusCode } = payload

  dispatch(
    setLastAsOfUtcError({
      retryAfterSeconds,
      hasRetryAfterHeader: !!retryAfterSecondsFromHeader,
      statusCode
    })
  )
}

const getRetryInterval = (payload: GetLatestAsOfUtcThunkRejectResponse) => {
  const { retryAfterSecondsFromHeader } = payload

  return retryAfterSecondsFromHeader || Math.min(retryAfterSeconds * 2, 60)
}
