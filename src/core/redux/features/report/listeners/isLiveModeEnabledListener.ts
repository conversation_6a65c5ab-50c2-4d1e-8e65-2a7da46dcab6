import { AppStartListening } from '@/core/redux/middleware/listenerMiddleware'
import { isLiveModeEnabledSelector } from '../selectors'
import { setKnowledgeAsOfUIVisible } from '../reportMetadata'

export const addLiveModeEnabledListener = (
  startListening: AppStartListening
) => {
  startListening({
    predicate: (_, currentState, previousState) => {
      const isLiveModeEnabled = isLiveModeEnabledSelector(currentState)
      const isChanged =
        isLiveModeEnabled !== isLiveModeEnabledSelector(previousState)

      return Boolean(isLiveModeEnabled) && isChanged
    },
    effect: async (_, { dispatch }) => {
      dispatch(setKnowledgeAsOfUIVisible(false))
    }
  })
}
