import { AppStartListening } from '@/core/redux/middleware/listenerMiddleware'
import {
  currentAsOfUtcSelector,
  isSyncDataPublisherSelector
} from '../selectors'
import { publishAsOfChangedThunk } from '../../context/thunk/publishAsOfChangedThunk'

export const addCurrentAsOfListener = (startListening: AppStartListening) => {
  startListening({
    predicate: (_, currentState, previousState) => {
      const currentValue = currentAsOfUtcSelector(currentState)
      const isValueChanged =
        currentValue !== currentAsOfUtcSelector(previousState)
      const isPublisher = isSyncDataPublisherSelector(currentState)

      return isPublisher && Boolean(currentValue) && isValueChanged
    },
    effect: async (_, { dispatch }) => {
      dispatch(publishAsOfChangedThunk())
    }
  })
}
