import { reportSlice, setReportDetails } from '../reportSlice'
import { expect, describe, it } from 'vitest'
import { getTextSetColumnNames, getKeyColumnNames } from '@/core/utils/grid'
import { reportMetadataSlice } from '../reportMetadata'

describe('setReportDetailsReducer', () => {
  it('should correctly set eodReportType and other properties from payload', () => {
    // Arrange
    const initialState = reportSlice.getInitialState()
    expect(initialState.eodReportType).toBeUndefined()

    const mockColumnInfo = [
      { name: 'column1', type: 'string', canPivotBy: false },
      { name: 'column2', type: 'number', canPivotBy: true }
    ]

    const mockPayload = {
      columnInfo: mockColumnInfo,
      reportType: 'dailyRiskReport',
      publishSyncData: true,
      eodReportType: 'EOD_REPORT',
      preProcessingParamSpecs: [{ name: 'param1', values: ['value1'] }]
    }

    // Act
    const nextState = reportSlice.reducer(
      initialState,
      setReportDetails(mockPayload)
    )

    // Assert
    expect(nextState.eodReportType).toBe('EOD_REPORT')
    expect(nextState.reportType).toBe('dailyRiskReport')
    expect(nextState.publishSyncData).toBe(true)
    expect(nextState.columnInfo).toEqual(mockColumnInfo)
    expect(nextState.textSetColumnNames).toEqual(
      getTextSetColumnNames(mockColumnInfo)
    )
    expect(nextState.keyColumnNames).toEqual(getKeyColumnNames(mockColumnInfo))
    expect(nextState.preProcessingParamSpecs).toEqual([
      { name: 'param1', values: ['value1'] }
    ])
    expect(nextState.supportsPivotMode).toBe(true)
  })

  it('should not modify state when payload is undefined', () => {
    // Arrange
    const initialState = {
      ...reportSlice.getInitialState(),
      eodReportType: 'EXISTING_EOD_REPORT',
      reportType: 'existingReport'
    }

    // Act
    const nextState = reportSlice.reducer(
      initialState,
      setReportDetails(undefined)
    )

    // Assert
    expect(nextState).toEqual(initialState)
  })
})

// 'reportMetadata/setKnowledgeAsOfUIVisible' dispatched should set isLiveModeEnabled
describe('reportMetadata/setKnowledgeAsOfUIVisible', () => {
  it('should set isLiveModeEnabled to false when action payload is true', () => {
    // Arrange
    const initialState = {
      ...reportSlice.getInitialState(),
      isLiveModeEnabled: true
    }

    // Act
    const nextState = reportSlice.reducer(
      initialState,
      reportMetadataSlice.actions.setKnowledgeAsOfUIVisible(true)
    )

    // Assert
    expect(nextState.isLiveModeEnabled).toBe(false)
  })

  it('should not change isLiveModeEnabled to false when action payload is false', () => {
    // Arrange
    const initialState = {
      ...reportSlice.getInitialState(),
      isLiveModeEnabled: true
    }

    // Act
    const nextState = reportSlice.reducer(
      initialState,
      reportMetadataSlice.actions.setKnowledgeAsOfUIVisible(false)
    )

    // Assert
    expect(nextState.isLiveModeEnabled).toBe(true)
  })
  it('should not change isLiveModeEnabled to true when action payload is false', () => {
    // Arrange
    const initialState = {
      ...reportSlice.getInitialState(),
      isLiveModeEnabled: false
    }

    // Act
    const nextState = reportSlice.reducer(
      initialState,
      reportMetadataSlice.actions.setKnowledgeAsOfUIVisible(false)
    )

    // Assert
    expect(nextState.isLiveModeEnabled).toBe(false)
  })
})
