import { addPivotResultCols } from '@/core/redux/features/report/utils/pivot.build'
import { sortPivotResultsColumns } from '@/core/redux/features/report/utils/pivot.sorting'
import { IColumnInfo, ISubsetResponse } from '@/core/services/riskReport'
import {
  ColGroupDef,
  Column,
  ColumnApi,
  IServerSideGetRowsRequest
} from '@ag-grid-community/core'
import { Mocked, vi } from 'vitest'

describe('addPivotResultCols', () => {
  let mockApi: Mocked<ColumnApi>
  let request: IServerSideGetRowsRequest
  let response: ISubsetResponse
  let columnsInfo: IColumnInfo[]
  let pivotResultsColumnsOrdering: string[] | null

  beforeEach(() => {
    // Mock ColumnApi
    mockApi = {
      setPivotResultColumns: vi.fn(),
      getPivotResultColumns: vi.fn(),
      getPivotColumns: vi.fn().mockReturnValue([])
    } as unknown as Mocked<ColumnApi>

    // Default request
    request = {
      groupKeys: [],
      pivotMode: true,
      pivotCols: [{ id: 'country', displayName: 'Country', field: 'country' }],
      valueCols: [{ id: 'sales', displayName: 'Sales', field: 'sales' }],
      rowGroupCols: [],
      filterModel: {},
      sortModel: [],
      startRow: 0,
      endRow: 100
    }

    // Default response
    response = {
      pivotResultColumns: ['country%sales'],
      data: {
        WellKnownType: 'Table',
        Columns: []
      },
      totalRowCount: 1
    }

    // Default columnsInfo
    columnsInfo = [
      {
        column: 'sales',
        type: 'number',
        numberFormat: '#,##0.00'
      } as unknown as IColumnInfo
    ]

    // Default ordering
    pivotResultsColumnsOrdering = null
  })

  test('should set pivot result columns when groupKeys is empty', () => {
    addPivotResultCols({
      request,
      response,
      api: mockApi,
      columnsInfo,
      pivotResultsColumnsOrdering
    })

    expect(mockApi.setPivotResultColumns).toHaveBeenCalled()
  })

  test('should not add pivot result columns when pivotCols is empty', () => {
    request.pivotCols = []

    addPivotResultCols({
      request,
      response,
      api: mockApi,
      columnsInfo,
      pivotResultsColumnsOrdering
    })

    // Expect `setPivotResultColumns` to be called with an empty array
    expect(mockApi.setPivotResultColumns).toHaveBeenCalledWith([])
  })

  test('should handle empty pivotFields in response', () => {
    response.pivotResultColumns = undefined

    addPivotResultCols({
      request,
      response,
      api: mockApi,
      columnsInfo,
      pivotResultsColumnsOrdering
    })

    // Expect `setPivotResultColumns` to be called with an empty array
    expect(mockApi.setPivotResultColumns).toHaveBeenCalledWith([])
  })

  test('should recursively build column definitions with nested parts', () => {
    response.pivotResultColumns = ['country%region%sales']

    addPivotResultCols({
      request,
      response,
      api: mockApi,
      columnsInfo,
      pivotResultsColumnsOrdering
    })

    // Capture the first call and its arguments
    const callArgs = mockApi.setPivotResultColumns.mock.calls[0][0]

    expect(callArgs).toMatchObject([
      {
        groupId: 'country',
        headerName: 'country',
        children: [
          {
            groupId: 'region',
            headerName: 'region',
            children: [
              {
                colId: 'country%region%sales',
                headerName: 'Sales',
                field: 'country%region%sales'
              }
            ]
          }
        ]
      }
    ])
  })

  test('should set new pivot result columns when groupKeys is not empty and columns change', () => {
    request.groupKeys = ['USA']

    response = {
      pivotResultColumns: ['country%sales', 'region%sales'],
      data: {
        WellKnownType: 'Table',
        Columns: []
      },
      totalRowCount: 9
    }

    // Mock existing pivot result columns
    mockApi.getPivotResultColumns.mockReturnValue([
      {
        getColGroupDef: () => ({ groupId: 'country' }),
        getParent: vi.fn().mockReturnValue(null)
      } as unknown as Column,
      {
        getColGroupDef: () => ({ groupId: 'city' }),
        getParent: vi.fn().mockReturnValue(null)
      } as unknown as Column
    ])

    addPivotResultCols({
      request,
      response,
      api: mockApi,
      columnsInfo,
      pivotResultsColumnsOrdering
    })

    expect(mockApi.setPivotResultColumns).toHaveBeenCalledWith(
      expect.any(Array)
    )
  })

  test('should handle missing column info in columnsInfo gracefully', () => {
    // Simulate missing column info for 'region'
    response.pivotResultColumns = ['region%sales']

    addPivotResultCols({
      request,
      response,
      api: mockApi,
      columnsInfo: [], // Empty columnsInfo
      pivotResultsColumnsOrdering
    })

    const callArgs = mockApi.setPivotResultColumns.mock.calls[0][0]

    expect(callArgs).toMatchObject([
      {
        groupId: 'region',
        headerName: 'region',
        children: [
          {
            colId: 'region%sales',
            headerName: 'Sales',
            field: 'region%sales',
            // Match these as functions without requiring exact implementation
            tooltipComponent: expect.any(Function),
            tooltipValueGetter: expect.any(Function),
            valueFormatter: expect.any(Function)
            // We don't expect cellClass or cellClassRules in this case
          }
        ]
      }
    ])
  })

  test('should handle empty pivotFields in response', () => {
    response.pivotResultColumns = undefined

    addPivotResultCols({
      request,
      response,
      api: mockApi,
      columnsInfo,
      pivotResultsColumnsOrdering
    })

    expect(mockApi.setPivotResultColumns).toHaveBeenCalledWith([])
  })

  test('should sort pivot result columns when ordering is provided', () => {
    pivotResultsColumnsOrdering = ['sales', 'country']

    addPivotResultCols({
      request,
      response,
      api: mockApi,
      columnsInfo,
      pivotResultsColumnsOrdering
    })

    const callArgs = mockApi.setPivotResultColumns.mock.calls[0][0]

    expect(callArgs).toMatchObject([
      {
        groupId: 'country',
        headerName: 'country',
        children: [
          {
            colId: 'country%sales',
            headerName: 'Sales',
            field: 'country%sales',
            // Relax the checks for dynamic functions
            tooltipComponent: expect.any(Function),
            tooltipValueGetter: expect.any(Function),
            valueFormatter: expect.any(Function)
            // We're not checking for `cellClassRules` or `cellClass` in this test
          }
        ]
      }
    ])
  })

  test('should not modify pivot result columns when no change is detected', () => {
    // Set up mock to simulate no change
    const pivotResultColumns: ColGroupDef[] = [
      { groupId: 'country', headerName: 'Country', children: [] }
    ]
    mockApi.getPivotResultColumns.mockReturnValue([
      {
        getColGroupDef: () => pivotResultColumns[0],
        getParent: vi.fn().mockReturnValue(null)
      } as unknown as Column
    ])

    addPivotResultCols({
      request,
      response,
      api: mockApi,
      columnsInfo,
      pivotResultsColumnsOrdering
    })

    // From no change, setPivotResultColumns should not be called
    expect(mockApi.setPivotResultColumns).toHaveBeenCalled()
  })
})

describe('sortPivotResultsColumns', () => {
  const columns: ColGroupDef[] = [
    {
      groupId: 'group1',
      children: []
    },
    {
      groupId: 'group2',
      children: []
    },
    {
      groupId: 'group3',
      children: []
    }
  ]

  it('should return the same array when sortedColumnsIds is null', async () => {
    const result = sortPivotResultsColumns(null, columns)
    expect(result).toBe(columns)
  })

  it('should return the sorted array based on the sortedColumnsIds order', async () => {
    const sortedColumnsIds = ['group3', 'group1', 'group2']
    const result = sortPivotResultsColumns(sortedColumnsIds, columns)
    expect(result[0].groupId).toBe(sortedColumnsIds[0])
    expect(result[1].groupId).toBe(sortedColumnsIds[1])
    expect(result[2].groupId).toBe(sortedColumnsIds[2])
  })

  it('should return the array with original order when sortedColumnsIds does not match any groupId', async () => {
    const sortedColumnsIds = ['group4', 'group5', 'group6']
    const result = sortPivotResultsColumns(sortedColumnsIds, columns)
    expect(result).toEqual(columns)
  })
})
