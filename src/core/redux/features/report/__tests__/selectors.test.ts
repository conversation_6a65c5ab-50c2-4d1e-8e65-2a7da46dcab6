import { describe, it, expect } from 'vitest'
import {
  eodReportTypeSelector,
  isLiveModeOrParentLiveMode
} from '@/core/redux/features/report/selectors'

describe('isLiveModeOrParentLiveMode', () => {
  it('Given state with report.isLiveModeEnabled true, When selector is called, Then returns true', () => {
    // Given
    const state = {
      report: { isLiveModeEnabled: true },
      context: {
        isParentLiveModeEnabled: false,
        isConnectedToGlueChannel: false
      }
    } as any

    // When
    const result = isLiveModeOrParentLiveMode(state)

    // Then
    expect(result).toBe(true)
  })

  it('Given state with context.isParentLiveModeEnabled true and report.isLiveModeEnabled false, not connected to glue', () => {
    // Given
    const state = {
      report: { isLiveModeEnabled: false },
      context: {
        isParentLiveModeEnabled: true,
        isConnectedToGlueChannel: false,
        isConnectedSubscriber: false
      }
    } as any

    // When
    const result = isLiveModeOrParentLiveMode(state)

    // Then
    expect(result).toBe(false)
  })

  it('Given state with context.isParentLiveModeEnabled true and report.isLiveModeEnabled false, connected to glue as parent', () => {
    // Given
    const state = {
      report: { isLiveModeEnabled: false },
      context: {
        isParentLiveModeEnabled: true,
        isConnectedToGlueChannel: true,
        isConnectedSubscriber: false
      }
    } as any

    // When
    const result = isLiveModeOrParentLiveMode(state)

    // Then
    expect(result).toBe(false)
  })

  it('Given state with context.isParentLiveModeEnabled true and report.isLiveModeEnabled false, for connected subsriber', () => {
    // Given
    const state = {
      report: { isLiveModeEnabled: false },
      context: {
        isParentLiveModeEnabled: true,
        isConnectedToGlueChannel: true,
        isConnectedSubscriber: true
      }
    } as any

    // When
    const result = isLiveModeOrParentLiveMode(state)

    // Then
    expect(result).toBe(true)
  })

  it('Given state with both flags false, When selector is called, Then returns false', () => {
    // Given
    const state = {
      report: { isLiveModeEnabled: false },
      context: {
        isParentLiveModeEnabled: false,
        isConnectedToGlueChannel: false
      }
    } as any

    // When
    const result = isLiveModeOrParentLiveMode(state)

    // Then
    expect(result).toBe(false)
  })
})

describe('eodReportTypeSelector', () => {
  it('should return eodReportType from state', () => {
    // Arrange
    const mockState = {
      report: {
        eodReportType: 'EODReport'
      }
    }

    // Act
    const result = eodReportTypeSelector(mockState)

    // Assert
    expect(result).toEqual('EODReport')
  })

  it('should return undefined when eodReportType is not set', () => {
    // Arrange
    const mockState = {
      report: {}
    }

    // Act
    const result = eodReportTypeSelector(mockState)

    // Assert
    expect(result).toBeUndefined()
  })
})
