import { apiP<PERSON>s, createHandler, setupMockServer } from '@/core/testing'

import * as asOfUTCLongPollingMock from '../thunk/asOfUtcLongPolling'
import { vi } from 'vitest'
import { waitFor } from '@testing-library/react'
import { longPollGetLatestAsOfUtcThunk } from '../thunk/longPollGetLatestAsOfUtcThunk'
import {
  startLatestAsOfUtcListener,
  stopLatestAsOfUtcListener
} from '../../../features/report'
import { setupStore } from '../../../store'

const handlerBody = {
  method: 'get',
  path: apiPaths.latestAsOf,
  response: {
    latestAsOfUtc: '2099-09-19'
  },
  status: 200
} as const

describe('latestAsOfUtcListener', () => {
  const server = setupMockServer()

  beforeEach(() => {
    vi.resetAllMocks()
  })

  it('should start long polling', async () => {
    const store = setupStore()
    vi.spyOn(asOfUTCLongPollingMock, 'startAsOfUtcLongPoll')
    store.dispatch(startLatestAsOfUtcListener())

    // expect Start long poling when initial snapshot is ready
    expect(asOfUTCLongPollingMock.startAsOfUtcLongPoll).toHaveBeenCalledTimes(1)
  })

  it('should stop long polling ', async () => {
    vi.spyOn(asOfUTCLongPollingMock, 'stopAsOfUtcLongPoll')
    vi.spyOn(asOfUTCLongPollingMock, 'startAsOfUtcLongPoll')
    const store = setupStore()
    store.dispatch(stopLatestAsOfUtcListener())

    // expect Start long polling when initial snapshot is ready
    await waitFor(() => {
      expect(asOfUTCLongPollingMock.stopAsOfUtcLongPoll).toHaveBeenCalledTimes(
        1
      )
    })
  })

  it('should retry rejected subscription', async () => {
    vi.spyOn(asOfUTCLongPollingMock, 'startAsOfUtcLongPoll')
    server.use(
      createHandler({
        ...handlerBody,
        status: 500
      })
    )
    const store = setupStore()
    store.dispatch(startLatestAsOfUtcListener())

    store.dispatch(
      longPollGetLatestAsOfUtcThunk.rejected(
        expect.anything(),
        '1',
        expect.anything()
      )
    )

    await waitFor(() => {
      expect(asOfUTCLongPollingMock.startAsOfUtcLongPoll).toHaveBeenCalledTimes(
        1
      )
    })
  })

  it('should dispatch last as of utc error with `hasRetryAfterHeader` set to `true`', async () => {
    // given
    const store = setupStore()
    const abortController = new AbortController()
    server.use(
      createHandler({
        ...handlerBody,
        status: 401,
        headers: {
          'Retry-After': '45'
        }
      })
    )

    // when
    store.dispatch(
      longPollGetLatestAsOfUtcThunk({
        signal: abortController.signal
      })
    )
    // then
    await waitFor(() =>
      expect(
        store.getState().report.lastAsOfUtcMetadata.error?.retryAfterSeconds
      ).toBe(45)
    )

    abortController.abort()
  })
  it('should dispatch last as of utc error with `hasRetryAfterHeader` set to `false`', async () => {
    // given
    const store = setupStore()
    const abortController = new AbortController()
    server.use(
      createHandler({
        ...handlerBody,
        status: 405
      })
    )

    // when
    store.dispatch(
      longPollGetLatestAsOfUtcThunk({
        signal: abortController.signal
      })
    )

    // then
    await waitFor(() =>
      expect(
        store.getState().report.lastAsOfUtcMetadata.error?.statusCode
      ).toBe(405)
    )
    expect(
      store.getState().report.lastAsOfUtcMetadata.error?.hasRetryAfterHeader
    ).toBeFalsy()
    abortController.abort()
  })

  it('should retry rejected subscription', async () => {
    const abortController = new AbortController()
    vi.spyOn(asOfUTCLongPollingMock, 'startAsOfUtcLongPoll')
    server.use(
      createHandler({
        ...handlerBody,
        status: 401,
        headers: {
          'Retry-After': '0.1'
        }
      })
    )
    const store = setupStore()
    store.dispatch(
      longPollGetLatestAsOfUtcThunk({
        signal: abortController.signal
      })
    )

    await waitFor(() => {
      expect(asOfUTCLongPollingMock.startAsOfUtcLongPoll).toHaveBeenCalledTimes(
        1
      )
      abortController.abort()
    })
  })
})
