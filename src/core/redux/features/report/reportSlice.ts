import { longPollGetLatestAsOfUtcThunk } from '@/core/redux/features/report/thunk/longPollGetLatestAsOfUtcThunk'
import {
  InitializeReportData,
  initializeReportDataThunk
} from '@/core/redux/features/report/thunk/initializeReportDataThunk'
import { riskReportSummaryThunk } from '@/core/redux/features/report/thunk/riskReportSummaryThunk'
import { IUserPreferences } from '@/core/services/epic'
import {
  ColumnStatus as ColumnsStatus,
  IColumnInfo,
  IDistinctColumnValues,
  IFilterModel,
  IGetReportDetailsResponse,
  ILatestAsOfResponse,
  IMetaDataResponse,
  ISummaryResponse,
  PreProcessingParamSpec
} from '@/core/services/riskReport'
import {
  isDateOlderThanFifteenMinutes,
  isDateOlderThanFiveMinutes
} from '@/core/utils/date'
import {
  getKeyColumnNames,
  getTextSetColumnNames,
  mapHeadlineValuesToRiskMetrics
} from '@/core/utils/grid'
import { logger } from '@/core/utils/logger'
import { PayloadAction, createSlice } from '@reduxjs/toolkit'
import {
  ColumnsHeaderNames,
  LiveUpdateDelayType,
  OriginatingContext,
  PreProcessingParams
} from './types'

export interface RiskReportMetric {
  title: string
  value: number | string
}

export interface LastAsOfUtcError {
  statusCode?: number
  retryAfterSeconds: number
  hasRetryAfterHeader: boolean
}

export interface LastAsOfUtcMetadata {
  receivedTime: string | null
  /**
   * The last data update time can be different from the `receivedTime`.
   * When the response from the `lastAsOfUtc` indicates that the data has not changed,
   * we update the `receivedTime`, but the `lastDataUpdateTime` remains unchanged.
   */
  lastDataUpdateTime: string | null
  error: LastAsOfUtcError | null
}

export interface ReportState {
  reportName: string
  windowUUID: string
  /**
   * All reports have a columns schema, and some reports share the same column schema.
   * `reportType` allows the sharing of presets between reports with the same column schema.
   */
  currentAsOfUtc: string
  latestLongPollAsOfUtc: string
  lastAsOfUtcMetadata: LastAsOfUtcMetadata
  columnInfo: IColumnInfo[]
  textSetColumnNames: string[]
  keyColumnNames: string[]
  tradingAreas: string[]
  appliedColumnFilters: IFilterModel[]
  distinctColumnValues: IDistinctColumnValues
  selectedTradingAreas: string[] | null
  columnsHeaderNames: ColumnsHeaderNames
  riskReportSummary: RiskReportMetric[]
  isLiveModeEnabled: boolean
  isRegionExpanded: boolean
  liveUpdateDelayType?: LiveUpdateDelayType
  /**
   * All available params and all available values for them for a given report.
   */
  preProcessingParamSpecs: PreProcessingParamSpec[]
  reportType: string | null
  eodReportType?: string
  publishSyncData: boolean
  /**
   * Active params that are added to all relevant backend API requests.
   */
  preProcessingParams?: PreProcessingParams
  drillThroughViewLiveUpdateAsOfUtcMetadataOrigin?: string
  isInitialized: boolean
  supportsPivotMode?: boolean
  columns: string[]
  columnsStatus?: ColumnsStatus
  /**
   * used to replay grid row selected event, when new subscriber connects to the channel
   */
  lastOriginatingContext?: OriginatingContext
}

export const initialReportState: ReportState = {
  reportName: '',
  windowUUID: '',
  currentAsOfUtc: '',
  latestLongPollAsOfUtc: '',
  lastAsOfUtcMetadata: {
    receivedTime: null,
    lastDataUpdateTime: null,
    error: null
  },
  columnInfo: [],
  textSetColumnNames: [],
  keyColumnNames: [],
  preProcessingParamSpecs: [],
  reportType: null,
  eodReportType: undefined,
  publishSyncData: false,
  tradingAreas: [],
  appliedColumnFilters: [],
  distinctColumnValues: {},
  selectedTradingAreas: null,
  columnsHeaderNames: {},
  riskReportSummary: [],
  isLiveModeEnabled: true,
  isRegionExpanded: false,
  liveUpdateDelayType: undefined,
  isInitialized: false,
  columns: [],
  lastOriginatingContext: undefined
}

const setSelectedTradingAreasReducer = (
  state: ReportState,
  { payload }: PayloadAction<string[]>
) => {
  state.selectedTradingAreas = payload
}

const setAppliedColumnFiltersReducer = (
  state: ReportState,
  { payload }: PayloadAction<IFilterModel[]>
) => {
  state.appliedColumnFilters = payload
}

const setColumnsHeaderNamesReducer = (
  state: ReportState,
  { payload }: PayloadAction<ColumnsHeaderNames>
) => {
  state.columnsHeaderNames = payload
}

const setTradingAreasReducer = (
  state: ReportState,
  {
    payload
  }: PayloadAction<
    { tradingAreas: string[]; selectedTradingAreas: string[] } | undefined
  >
) => {
  if (!payload) {
    return
  }

  state.tradingAreas = payload.tradingAreas
  state.selectedTradingAreas = payload.selectedTradingAreas
}

const setDistinctColumnValuesReducer = (
  state: ReportState,
  { payload }: PayloadAction<IDistinctColumnValues>
) => {
  state.distinctColumnValues = payload
}

const longPollGetLatestAsOfUtcResponseReducer = (
  state: ReportState,
  { payload }: PayloadAction<ILatestAsOfResponse>
) => {
  if (payload.latestAsOfUtc > state.latestLongPollAsOfUtc) {
    state.latestLongPollAsOfUtc = payload.latestAsOfUtc
  }

  setLatestAsOfUtcResponseReducer(state, { payload, type: '' })
}

const setLatestAsOfUtcResponseReducer = (
  state: ReportState,
  { payload }: PayloadAction<ILatestAsOfResponse>
) => {
  const receivedTime = new Date().toISOString()

  state.lastAsOfUtcMetadata.receivedTime = receivedTime
  state.lastAsOfUtcMetadata.error = null
  state.liveUpdateDelayType = undefined

  // If the newest value has changed, it means that data is going to be updated.
  if (payload.latestAsOfUtc && payload.latestAsOfUtc !== state.currentAsOfUtc) {
    state.currentAsOfUtc = payload.latestAsOfUtc
    state.lastAsOfUtcMetadata.lastDataUpdateTime = receivedTime
  }

  if (!payload.latestAsOfUtc) {
    logger.error('Latest as of UTC date is empty.')
  }

  if (isDateOlderThanFifteenMinutes(receivedTime)) {
    state.liveUpdateDelayType = 'error'
  } else if (isDateOlderThanFiveMinutes(receivedTime)) {
    state.liveUpdateDelayType = 'warning'
  }

  // Only update preProcessingParams.originatingContext.reportAsOfUtc when relevant
  if (
    !payload.originatingReportLatestAsOfUtc ||
    !state.preProcessingParams ||
    !state.preProcessingParams.originatingContext ||
    payload.originatingReportLatestAsOfUtc ==
      state.preProcessingParams.originatingContext.reportAsOfUtc
  ) {
    return
  }

  state.preProcessingParams = {
    ...state.preProcessingParams,
    originatingContext: {
      ...state.preProcessingParams.originatingContext,
      reportAsOfUtc: payload.originatingReportLatestAsOfUtc
    }
  }
}

const setReportDetailsReducer = (
  state: ReportState,
  { payload }: PayloadAction<IGetReportDetailsResponse | undefined>
) => {
  if (payload) {
    state.supportsPivotMode = payload.columnInfo.some(
      (column) => column.canPivotBy
    )

    state.columnInfo = payload.columnInfo
    state.textSetColumnNames = getTextSetColumnNames(payload.columnInfo)
    state.keyColumnNames = getKeyColumnNames(payload.columnInfo)
    state.preProcessingParamSpecs = payload.preProcessingParamSpecs || []
    state.reportType = payload.reportType
    state.publishSyncData = payload.publishSyncData
    state.eodReportType = payload.eodReportType
  }
}

const setLastAsOfUtcErrorReducer = (
  state: ReportState,
  { payload }: PayloadAction<LastAsOfUtcError>
) => {
  state.lastAsOfUtcMetadata.error = payload

  if (state.isLiveModeEnabled) {
    state.liveUpdateDelayType = 'warning'

    if (payload?.retryAfterSeconds && payload?.retryAfterSeconds > 32) {
      state.liveUpdateDelayType = 'error'
    }
  }
}

const setRegionExpandReducer = (
  state: ReportState,
  { payload }: PayloadAction<boolean>
) => {
  state.isRegionExpanded = payload
}

const setCurrentAsOfLocalReducer = (
  state: ReportState,
  { payload }: PayloadAction<string>
) => {
  // Make sure live mode is disabled when the user manually changes the date
  state.isLiveModeEnabled = false
  // convert payload local date to UTC
  state.currentAsOfUtc = new Date(payload).toISOString()
}

const setSummaryReducer = (
  state: ReportState,
  { payload }: PayloadAction<ISummaryResponse | undefined>
) => {
  if (payload) {
    state.riskReportSummary = mapHeadlineValuesToRiskMetrics(
      payload.headlineValues
    )
  }
}

const setPreProcessingParamsReducer = (
  state: ReportState,
  { payload }: PayloadAction<PreProcessingParams>
) => {
  state.preProcessingParams = { ...state.preProcessingParams, ...payload }
}

const setOriginatingContextExceptReportAsOfReducer = (
  state: ReportState,
  { payload }: PayloadAction<OriginatingContext>
) => {
  state.preProcessingParams = {
    ...state.preProcessingParams,
    originatingContext: {
      ...payload,
      reportAsOfUtc:
        state.preProcessingParams?.originatingContext?.reportAsOfUtc ??
        payload.reportAsOfUtc
    }
  }
}

const setOriginatingContextFiltersReducer = (
  state: ReportState,
  { payload }: PayloadAction<IFilterModel[]>
) => {
  if (
    !state.preProcessingParams ||
    !state.preProcessingParams.originatingContext
  ) {
    return
  }

  state.preProcessingParams = {
    ...state.preProcessingParams,
    originatingContext: {
      ...state.preProcessingParams.originatingContext,
      filters: payload
    }
  }
}

// TODO rename to setUserPreferences
const setUserPreferencesReducer = (
  state: ReportState,
  { payload }: PayloadAction<IUserPreferences | null>
) => {
  if (payload === null) {
    return
  }
  const { preProcessingReportParams } = payload
  state.preProcessingParams = {
    ...state.preProcessingParams,
    reportParams: preProcessingReportParams
  }
}

const startLatestAsOfUtcListenerReducer = (state: ReportState) => {
  state.isLiveModeEnabled = true
}
const stopLatestAsOfUtcListenerReducer = (state: ReportState) => {
  state.isLiveModeEnabled = false
}

const recordBackendColumnOrderReducer = (
  state: ReportState,
  { payload }: PayloadAction<string[]>
) => {
  state.columns = payload
}

const setIsInitializedReducer = (
  state: ReportState,
  { payload }: PayloadAction<InitializeReportData>
) => {
  state.selectedTradingAreas = payload.selectedTradingAreas
  state.tradingAreas = payload.tradingAreas
  state.reportName = payload.reportName

  const receivedTime = new Date().toISOString()
  state.lastAsOfUtcMetadata.receivedTime = receivedTime
  state.lastAsOfUtcMetadata.error = null
  state.liveUpdateDelayType = undefined
  state.lastAsOfUtcMetadata.lastDataUpdateTime = receivedTime

  setLatestAsOfUtcResponseReducer(state, {
    payload: payload.latestAsOfUtcResponse,
    type: ''
  })

  state.isInitialized = true
}

const setOriginatingReportAsOfReducer = (
  state: ReportState,
  { payload }: PayloadAction<string>
) => {
  if (
    state.preProcessingParams &&
    state.preProcessingParams.originatingContext
  ) {
    state.preProcessingParams = {
      ...state.preProcessingParams,
      originatingContext: {
        ...state.preProcessingParams.originatingContext,
        reportAsOfUtc: payload
      }
    }
  }
}

const setDrillThroughViewLiveUpdateAsOfUtcMetadataOriginReducer = (
  state: ReportState,
  { payload }: PayloadAction<string | undefined>
) => {
  state.drillThroughViewLiveUpdateAsOfUtcMetadataOrigin = payload
}

// redux slice that holds reportName
export const reportSlice = createSlice({
  name: 'report',
  initialState: initialReportState,
  reducers: {
    // startLatestAsOfUtcListener is connected to the latestAsOfUtcListener middleware
    startLatestAsOfUtcListener: startLatestAsOfUtcListenerReducer,
    // stopLatestAsOfUtcListener is connected to the latestAsOfUtcListener middleware
    stopLatestAsOfUtcListener: stopLatestAsOfUtcListenerReducer,

    setReportName: (state, { payload }: PayloadAction<string>) => {
      state.reportName = payload
    },
    setWindowUUID: (state, { payload }: PayloadAction<string>) => {
      state.windowUUID = payload
    },
    setLatestAsOfUtcResponse: setLatestAsOfUtcResponseReducer,
    setCurrentAsOfLocal: setCurrentAsOfLocalReducer,
    setLastAsOfUtcError: setLastAsOfUtcErrorReducer,
    setRegionExpand: setRegionExpandReducer,
    setSelectedTradingAreas: setSelectedTradingAreasReducer,
    setAppliedColumnFilters: setAppliedColumnFiltersReducer,
    setDistinctColumnValues: setDistinctColumnValuesReducer,
    setColumnsHeaderNames: setColumnsHeaderNamesReducer,
    setPreProcessingParams: setPreProcessingParamsReducer,
    setOriginatingContextExceptReportAsOf:
      setOriginatingContextExceptReportAsOfReducer,
    setOriginatingContextFilters: setOriginatingContextFiltersReducer,
    setSummary: setSummaryReducer,
    setReportDetails: setReportDetailsReducer,
    setTradingAreas: setTradingAreasReducer,
    setUserPreProcessingParams: setUserPreferencesReducer,
    recordBackendColumnOrder: recordBackendColumnOrderReducer,
    setColumnsStatus: (
      state: ReportState,
      { payload }: PayloadAction<ColumnsStatus | undefined>
    ) => {
      state.columnsStatus = payload
    },
    setDrillThroughViewLiveUpdateAsOfUtcMetadataOrigin:
      setDrillThroughViewLiveUpdateAsOfUtcMetadataOriginReducer,
    setLatestAsOfUtcFromGlue: (
      state,
      { payload }: PayloadAction<IMetaDataResponse>
    ) => {
      state.currentAsOfUtc =
        payload.synchronizedReportAsOfUtcs[state.reportName]
    },
    setLastOriginatingContext: (
      state,
      { payload }: PayloadAction<OriginatingContext>
    ) => {
      state.lastOriginatingContext = payload
    },
    setOriginatingReportAsOf: setOriginatingReportAsOfReducer
  },
  extraReducers(builder) {
    builder.addCase(
      longPollGetLatestAsOfUtcThunk.fulfilled,
      longPollGetLatestAsOfUtcResponseReducer
    )
    builder.addCase(riskReportSummaryThunk.fulfilled, setSummaryReducer)
    builder.addCase(
      initializeReportDataThunk.fulfilled,
      setIsInitializedReducer
    )
    builder.addCase(initializeReportDataThunk.rejected, (state) => {
      state.isInitialized = true
      logger.debug('initializeReportDataThunk.rejected was triggered')
    })
    // diable live mode if setKnowledgeAsOfUIVisible
    builder.addMatcher(
      (action) => action.type === 'reportMetadata/setKnowledgeAsOfUIVisible',
      (state, { payload }: PayloadAction<boolean>) => {
        if (payload === true) {
          state.isLiveModeEnabled = false
        }
      }
    )
  }
})

export const {
  setReportName,
  setWindowUUID,
  setLatestAsOfUtcResponse,
  setCurrentAsOfLocal,
  setLastAsOfUtcError,
  setSelectedTradingAreas,
  setDistinctColumnValues,
  setAppliedColumnFilters,
  setColumnsHeaderNames,
  startLatestAsOfUtcListener,
  stopLatestAsOfUtcListener,
  setRegionExpand,
  setPreProcessingParams,
  setOriginatingContextExceptReportAsOf,
  setOriginatingContextFilters,
  setReportDetails,
  setDrillThroughViewLiveUpdateAsOfUtcMetadataOrigin,
  setTradingAreas,
  setSummary,
  setUserPreProcessingParams,
  recordBackendColumnOrder,
  setColumnsStatus,
  setLatestAsOfUtcFromGlue,
  setLastOriginatingContext,
  setOriginatingReportAsOf
} = reportSlice.actions

export const reportReducer = reportSlice.reducer
