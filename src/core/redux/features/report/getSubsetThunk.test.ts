import { setupStore } from '../../store'
import { getSubsetThunk } from './getSubsetThunk'
import { vi } from 'vitest'
import { allHandlers, apiPaths, setupMockServer } from '@/core/testing'
import { waitFor } from '@testing-library/react'
import { rest } from 'msw'

const store = setupStore()
const server = setupMockServer(...allHandlers)

describe('getSubsetThunk', () => {
  const mockParams = {
    request: {
      startRow: 0,
      endRow: 10,
      valueCols: [],
      sortModel: [],
      rowGroupCols: [],
      groupKeys: [],
      filterModel: {}
    },
    fail: vi.fn()
  } as any

  beforeEach(() => {
    vi.resetAllMocks()
  })

  it('should be defined', async () => {
    expect(getSubsetThunk).toBeDefined()
  })

  it('handles exeptions', async () => {
    // given
    // when
    store.dispatch(getSubsetThunk(mockParams))

    // then
    waitFor(() => expect(mockParams.fail).toHaveBeenCalled(), {
      timeout: 1000
    })
  })

  it('should handle fetch errors', () => {
    // given
    // setup 'Failed to fetch' error
    server.use(
      rest.get(apiPaths.getSubset, (_req, res, ctx) => {
        return res(ctx.status(500))
      })
    )
    // mock setTimeout
    vi.useFakeTimers()
    const setTimeoutSpy = vi.spyOn(global, 'setTimeout')

    // when
    store.dispatch(getSubsetThunk(mockParams))

    // then
    waitFor(() => expect(mockParams.fail).toBeCalled())
    // expect setTimeout to be called
    expect(setTimeoutSpy).toHaveBeenCalledTimes(1)
  })
})
