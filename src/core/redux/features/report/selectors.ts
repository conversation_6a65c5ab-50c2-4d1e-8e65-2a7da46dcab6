import { RootState } from '@/core/redux/store'
import { createSelector } from '@reduxjs/toolkit'
import {
  isConnectedSubscriberSelector,
  isParentLiveModeEnabledSelector
} from '../context/selectors'

export const reportNameSelector = (state: RootState) => state.report.reportName
export const windowUUIDSelector = (state: RootState) => state.report.windowUUID
export const currentAsOfUtcSelector = (state: RootState) =>
  state.report.currentAsOfUtc
export const latestLongPollAsOfUtcSelector = (state: RootState) =>
  state.report.latestLongPollAsOfUtc
export const preProcessingParamsSelector = (state: RootState) =>
  state.report.preProcessingParams
export const isTradesReportSelector = (state: RootState) =>
  state.report.reportName.toLowerCase() === 'trades'
export const columnsStatusSelector = createSelector(
  (state: RootState) => state.report.columnsStatus,
  (columnsStatus) => columnsStatus
)

export const columnInfo = (state: RootState) => state.report.columnInfo

export const selectedTradingAreasSelector = (state: RootState) =>
  state.report.selectedTradingAreas

export const isLiveModeEnabledSelector = (state: RootState) =>
  state.report.isLiveModeEnabled

export const isSyncDataPublisherSelector = (state: RootState) =>
  state.report.publishSyncData

export const lastOriginatingContextSelector = (state: RootState) =>
  state.report.lastOriginatingContext

export const isLiveModeOrParentLiveMode = (state: RootState) =>
  isConnectedSubscriberSelector(state)
    ? isParentLiveModeEnabledSelector(state)
    : isLiveModeEnabledSelector(state)

export const eodReportTypeSelector = (state: RootState) =>
  state.report.eodReportType

export const reportPathSelector = (state: RootState) => {
  const reportName = state.report.reportName
  const eodReportType = state.report.eodReportType ?? reportName
  const isKnowledgeAsOfUIVisible =
    state.reportMetadata.knowledgeAsOf.isKnowledgeAsOfUIVisible

  return isKnowledgeAsOfUIVisible ? eodReportType : reportName
}

export const getSnapshot = (state: RootState) => {
  const currentAsOfUtc = state.report.currentAsOfUtc
  const isKnowledgeAsOfUIVisible =
    state.reportMetadata.knowledgeAsOf.isKnowledgeAsOfUIVisible
  const currentKnowledgeAsOf =
    state.reportMetadata.knowledgeAsOf.currentKnowledgeAsOf

  return {
    asOfUtc: currentAsOfUtc,
    knowledgeDatesUtc: isKnowledgeAsOfUIVisible
      ? currentKnowledgeAsOf
      : undefined
  }
}
