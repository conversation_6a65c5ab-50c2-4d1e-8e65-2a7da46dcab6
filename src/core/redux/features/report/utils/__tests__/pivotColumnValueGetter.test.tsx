import { render, waitFor } from '@testing-library/react'
import { AgGridReact } from '@ag-grid-community/react'
import { describe, it, expect, vi } from 'vitest'

import { useGridApi } from '@/components/Grid/hooks'
import { pivotColumnValueGetter } from '../pivotColumnValueGetter'
import { ModuleRegistry } from '@ag-grid-community/core'

import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model'
import { RowGroupingModule } from '@ag-grid-enterprise/row-grouping'

ModuleRegistry.registerModules([RowGroupingModule])
ModuleRegistry.registerModules([ClientSideRowModelModule])

describe('pivotColumnValueGetter', () => {
  beforeEach(() => {
    vi.resetAllMocks()
  })

  it('renders pivot values correctly', async () => {
    const rowData = [
      { country: 'USA', year: '2020', 'gold.medal': 1, silver: 15, bronze: 12 },
      { country: 'USA', year: '2021', 'gold.medal': 1, silver: 18, bronze: 16 },
      {
        country: 'Canada',
        year: '2020',
        'gold.medal': 3,
        silver: 17,
        bronze: 13
      },
      {
        country: 'Canada',
        year: '2021',
        'gold.medal': 3,
        silver: 14,
        bronze: 15
      },
      { country: 'UK', year: '2020', 'gold.medal': 4, silver: 16, bronze: 14 },
      { country: 'UK', year: '2021', 'gold.medal': 4, silver: 15, bronze: 17 }
    ]

    const columnDefs = [
      { field: 'country', pivot: true },
      { field: 'year' },
      {
        field: 'gold.medal',
        aggFunc: 'sum',
        valueGetter: pivotColumnValueGetter
      },
      { field: 'silver', aggFunc: 'sum' },
      { field: 'bronze', aggFunc: 'sum' }
    ]

    const TestGrid = ({ onGridReady }: any) => {
      const { gridApiRef } = useGridApi()

      return (
        <div style={{ height: 500 }}>
          <AgGridReact
            ref={gridApiRef}
            gridOptions={{
              pivotMode: true
            }}
            columnDefs={columnDefs}
            rowData={rowData}
            onGridReady={onGridReady}
          />
        </div>
      )
    }

    const gridReadySpy = vi.fn()

    const { getByText, container } = render(
      <TestGrid
        onGridReady={() => {
          gridReadySpy()
        }}
      />
    )

    await waitFor(() => {
      expect(gridReadySpy).toHaveBeenCalled()
    })

    // Check if the values are rendered correctly
    expect(getByText('2')).toBeInTheDocument() // USA gold.medal
    expect(getByText('6')).toBeInTheDocument() // Canada gold.medal
    expect(getByText('8')).toBeInTheDocument() // UK gold.medal
  })
})
