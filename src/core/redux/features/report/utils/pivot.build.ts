import { Tooltip } from '@/components/Tooltip'
import { PIVOT_SEPARATOR } from '@/core/redux/features/report'
import { sortPivotResultsColumns } from '@/core/redux/features/report/utils/pivot.sorting'
import { IColumnInfo, ISubsetResponse } from '@/core/services/riskReport'
import {
  formatValue,
  getNumericCellClassRules,
  isNumericColumn,
  shouldContentBeAlignedToRight,
  tooltipValueFormat
} from '@/core/utils/grid'
import {
  ColDef,
  ColGroupDef,
  ColumnApi,
  IServerSideGetRowsRequest,
  ITooltipParams,
  ValueFormatterParams
} from '@ag-grid-community/core'
import { pivotColumnValueGetter } from './pivotColumnValueGetter'

function addPivotResultCols(args: {
  request: IServerSideGetRowsRequest
  response: ISubsetResponse
  api: ColumnApi
  columnsInfo: IColumnInfo[]
  pivotResultsColumnsOrdering: string[] | null
}) {
  const { request, response, api, columnsInfo, pivotResultsColumnsOrdering } =
    args

  // create pivot colDefs based of data returned from the server
  const pivotResultColumns = createPivotResultColumns({
    request,
    pivotFields: response.pivotResultColumns,
    columnsInfo
  })

  const { groupKeys } = request

  if (groupKeys.length === 0) {
    // replace current pivot result columns with the new ones
    api.setPivotResultColumns(
      sortPivotResultsColumns(pivotResultsColumnsOrdering, pivotResultColumns)
    )
    return
  }

  const currentPivotResultColumns = (api
    .getPivotResultColumns()
    ?.map((c) => {
      let level = 1
      let parent = c.getParent()

      while (level <= api.getPivotColumns().length && parent.getParent()) {
        level++
        parent = parent.getParent()
      }

      return parent || c
    })
    .map((c) => c.getColGroupDef()) ?? []) as ColGroupDef[]

  const { result, isChanged } = interleaveAndDeduplicate(
    pivotResultColumns,
    currentPivotResultColumns
  )

  if (isChanged) {
    // supply pivot result columns to the grid
    api.setPivotResultColumns(
      sortPivotResultsColumns(pivotResultsColumnsOrdering, result)
    )
  }
}

function createPivotResultColumns({
  request,
  pivotFields,
  columnsInfo
}: {
  request: IServerSideGetRowsRequest
  pivotFields: string[] | undefined
  columnsInfo: IColumnInfo[]
}): ColGroupDef[] {
  if (request.pivotMode && request.pivotCols.length > 0) {
    const pivotResultsColumns: ColGroupDef[] = []

    pivotFields?.forEach((field) =>
      addColDef({
        colId: field,
        parts: field.split(PIVOT_SEPARATOR),
        pivotResultsColumns,
        request,
        columnsInfo
      })
    )

    return pivotResultsColumns ?? []
  }

  return []
}

function addColDef(args: {
  colId: string
  parts: string[]
  pivotResultsColumns: (ColDef | ColGroupDef)[]
  request: IServerSideGetRowsRequest
  columnsInfo: IColumnInfo[]
}): (ColDef | ColGroupDef)[] {
  const { columnsInfo, colId, parts, pivotResultsColumns, request } = args

  if (parts.length === 0) {
    return []
  }

  const first = parts[0]

  const existing: ColGroupDef = pivotResultsColumns.find(
    (r: ColDef | ColGroupDef) => 'groupId' in r && r.groupId === first
  ) as ColGroupDef

  if (existing) {
    existing.children = addColDef({
      colId,
      parts: parts.slice(1),
      pivotResultsColumns: existing.children,
      request,
      columnsInfo
    })
  } else {
    const colDef: ColGroupDef | ColDef = {}
    const isGroup = parts.length > 1

    if (isGroup) {
      // eslint-disable-next-line @typescript-eslint/no-extra-semi
      ;(colDef as ColGroupDef).groupId = first
      colDef.headerName = first
    } else {
      const columnVO = request.valueCols.find((r) => r.field === first)
      const columnInfo = columnsInfo.find((col) => col.column === columnVO?.id)

      if (columnVO) {
        colDef.colId = colId
        colDef.headerName = columnVO.displayName
        colDef.field = colId
        // valueGetter is required to handle fields with "." in the name
        colDef.valueGetter = pivotColumnValueGetter
        colDef.valueFormatter = (params: ValueFormatterParams) => {
          return columnInfo
            ? formatValue(
                params.value,
                columnInfo?.type,
                columnInfo?.numberFormat
              )
            : params.value
        }
        colDef.tooltipValueGetter = (params: ITooltipParams) =>
          columnInfo
            ? tooltipValueFormat(
                params.value,
                columnInfo.type,
                columnInfo.numberFormat
              )
            : params.value
        colDef.tooltipComponent = Tooltip

        if (isNumericColumn(columnInfo?.type)) {
          colDef.cellClassRules = getNumericCellClassRules()
        }

        if (shouldContentBeAlignedToRight(columnInfo?.type)) {
          colDef.cellClass = 'align-right'
        }
      }
    }

    const children = addColDef({
      colId,
      parts: parts.slice(1),
      pivotResultsColumns: [],
      request,
      columnsInfo
    })

    if (children.length > 0) {
      // eslint-disable-next-line @typescript-eslint/no-extra-semi
      ;(colDef as ColGroupDef).children = children
    }

    pivotResultsColumns.push(colDef)
  }

  return pivotResultsColumns
}

function interleaveAndDeduplicate(
  pivotResultColumns: ColGroupDef[],
  currentPivotResultColumns: ColGroupDef[]
): { result: ColGroupDef[]; isChanged: boolean } {
  let isChanged = false
  const result: ColGroupDef[] = []
  const seenGroupIds = new Set<string>()

  // Helper function to add column if not already seen
  function addColumn(col: ColGroupDef) {
    if (!col.groupId || !seenGroupIds.has(col.groupId)) {
      result.push(col)
      if (col.groupId) {
        seenGroupIds.add(col.groupId)
      }
    }
  }

  // Create a map of the current columns by groupId for quick lookup
  const pivotColumnsMap = new Map<string, ColGroupDef>()
  pivotResultColumns.forEach((col) => {
    if (col.groupId) {
      pivotColumnsMap.set(col.groupId, col)
    }
  })

  // Iterate through currentPivotResultColumns, which is the reference order
  currentPivotResultColumns.forEach((col) => {
    const groupId = col.groupId

    if (groupId && pivotColumnsMap.has(groupId)) {
      const currentCol = pivotColumnsMap.get(groupId)

      if (!currentCol) {
        return
      }

      // Add the column from pivotResultColumns if it exists
      addColumn(currentCol)
      pivotColumnsMap.delete(groupId) // Remove it from the map to avoid duplicates
    } else {
      // Otherwise, add the column from currentPivotResultColumns
      addColumn(col)
    }
  })

  // Add remaining columns from pivotResultColumns that were not in currentPivotResultColumns
  pivotResultColumns.forEach((col) => {
    if (col.groupId && !seenGroupIds.has(col.groupId)) {
      addColumn(col)
      isChanged = true
    }
  })

  return { result, isChanged }
}

export { addPivotResultCols }
