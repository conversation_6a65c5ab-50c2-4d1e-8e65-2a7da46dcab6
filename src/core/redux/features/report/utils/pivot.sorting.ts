import { ColGroupDef } from '@ag-grid-community/core'

function sortPivotResultsColumns(
  sortedColumnsIds: string[] | null,
  columns: ColGroupDef[]
) {
  if (!sortedColumnsIds) {
    return columns
  }

  const pivotGroups = getPivotGroups(sortedColumnsIds)

  return columns.slice().sort((a, b) => {
    const indexA = pivotGroups.findIndex((id) => findByGroupId(id, a.groupId))
    const indexB = pivotGroups.findIndex((id) => findByGroupId(id, b.groupId))

    return indexA - indexB
  })
}

function getPivotGroups(sortedColumns: string[]) {
  return sortedColumns.map((c) => c.split('%'))
}

function findByGroupId(id: string[], groupId: string | undefined) {
  return id[0] === groupId
}

export { sortPivotResultsColumns }
