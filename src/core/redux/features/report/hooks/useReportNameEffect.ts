import { setReportName } from '@/core/redux/features/report'
import { useAppDispatch } from '@/core/redux/hooks/useAppDispatch'
import { useGetReportNameFromRoute } from '@/core/utils/router'
import { useEffect } from 'react'

export const useReportNameEffect = () => {
  const dispatch = useAppDispatch()
  const reportName = useGetReportNameFromRoute()

  useEffect(() => {
    if (reportName) {
      dispatch(setReportName(reportName))
    }
  }, [reportName, dispatch])
}
