import { useRepoNettingParamEffect } from '@/core/redux/features/report/hooks'
import { IPreProcessingParamName } from '@/core/services/riskReport'
import {
  allHandlers,
  apiPaths,
  combineWrappers,
  createHandler,
  deepObjectContaining,
  getInitializeDataWrapper,
  getReduxWrapper,
  setupMockServer,
  waitForCurrent,
  wrapWithUrl
} from '@/core/testing'
import {
  getPreProcessingParamSpecsDummy,
  getPreProcessingParamValueSpecDummy,
  getReportDetailsResponseDummy,
  getUserPreferenceResponseDummy
} from '@/core/testing/dummies'
import { renderHook, waitFor } from '@testing-library/react'
import { vi } from 'vitest'

describe('useRepoNettingParam', () => {
  const server = setupMockServer(...allHandlers)

  const { wrapper: reduxWrapper, resetStore } = getReduxWrapper()
  const { wrapper: initializeDataWrapper } = getInitializeDataWrapper()

  const wrapper = combineWrappers(
    wrapWithUrl('/reports/pnl-live'),
    reduxWrapper,
    initializeDataWrapper
  )

  afterEach(resetStore)

  it('should return display label according to the fetched data', async () => {
    // given
    server.use(
      createHandler({
        method: 'get',
        path: apiPaths.getReportDetails,
        response: getReportDetailsResponseDummy({
          preProcessingParamSpecs: [
            getPreProcessingParamSpecsDummy({
              paramName: IPreProcessingParamName.RepoNetting,
              displayLabel: 'Repos'
            })
          ]
        })
      })
    )

    const { result } = renderHook(useRepoNettingParamEffect, { wrapper })

    // then
    await waitForCurrent(result)

    expect(result.current.displayLabel).toBe('Repos')
  })

  it('should return values spec according to the fetched data', async () => {
    // given
    server.use(
      createHandler({
        method: 'get',
        path: apiPaths.getReportDetails,
        response: getReportDetailsResponseDummy({
          preProcessingParamSpecs: [
            getPreProcessingParamSpecsDummy({
              paramName: IPreProcessingParamName.RepoNetting,
              paramValues: [
                getPreProcessingParamValueSpecDummy({
                  value: 'None'
                }),
                getPreProcessingParamValueSpecDummy({
                  value: 'CollapseIntoSingleTicker'
                })
              ]
            })
          ]
        })
      })
    )

    const { result } = renderHook(useRepoNettingParamEffect, { wrapper })

    // then
    await waitForCurrent(result)

    expect(result.current.valuesSpec).toHaveLength(2)
    expect(result.current.valuesSpec).toMatchObject([
      { value: 'None' },
      { value: 'CollapseIntoSingleTicker' }
    ])
  })

  it('should return current value spec according to the value from user preferences', async () => {
    // given
    server.use(
      createHandler({
        method: 'get',
        path: apiPaths.getReportDetails,
        response: getReportDetailsResponseDummy({
          preProcessingParamSpecs: [
            getPreProcessingParamSpecsDummy({
              paramName: IPreProcessingParamName.RepoNetting,
              paramValues: [
                getPreProcessingParamValueSpecDummy({
                  value: 'MergeWithBondTicker'
                })
              ]
            })
          ]
        })
      })
    )
    server.use(
      createHandler({
        method: 'get',
        path: apiPaths.getUserPreference,
        response: getUserPreferenceResponseDummy({
          Data: {
            preProcessingReportParams: {
              [IPreProcessingParamName.RepoNetting]: 'MergeWithBondTicker'
            }
          }
        })
      })
    )

    const { result } = renderHook(useRepoNettingParamEffect, { wrapper })

    // then
    await waitForCurrent(result)

    expect(result.current.currentValueSpec).toMatchObject({
      value: 'MergeWithBondTicker'
    })
  })

  describe('changing value', () => {
    it('should change the current value spec when according to the passed value', async () => {
      // given
      server.use(
        createHandler({
          method: 'get',
          path: apiPaths.getReportDetails,
          response: getReportDetailsResponseDummy({
            preProcessingParamSpecs: [
              getPreProcessingParamSpecsDummy({
                paramName: IPreProcessingParamName.RepoNetting,
                paramValues: [
                  getPreProcessingParamValueSpecDummy({
                    value: 'None'
                  }),
                  getPreProcessingParamValueSpecDummy({
                    value: 'CollapseIntoSingleTicker'
                  })
                ]
              })
            ]
          })
        })
      )

      const { result } = renderHook(useRepoNettingParamEffect, { wrapper })

      // then
      await waitForCurrent(result)

      expect(result.current.currentValueSpec).toBe(undefined)

      // when
      result.current.changeParamValue('CollapseIntoSingleTicker')

      // then
      waitFor(() => {
        expect(result.current.currentValueSpec).toMatchObject({
          value: 'CollapseIntoSingleTicker'
        })
      })
    })

    it('should save the new value in user preferences', async () => {
      // given
      server.use(
        createHandler({
          method: 'get',
          path: apiPaths.getReportDetails,
          response: getReportDetailsResponseDummy({
            preProcessingParamSpecs: [
              getPreProcessingParamSpecsDummy({
                paramName: IPreProcessingParamName.RepoNetting,
                paramValues: [
                  getPreProcessingParamValueSpecDummy({
                    value: 'MergeWithBondTicker'
                  })
                ]
              })
            ]
          })
        })
      )

      const saveUserPreferenceReqSpy = vi.fn()

      server.use(
        createHandler({
          method: 'post',
          path: apiPaths.saveUserPreference,
          reqSpy: saveUserPreferenceReqSpy
        })
      )

      const { result } = renderHook(useRepoNettingParamEffect, { wrapper })

      // then

      await waitForCurrent(result)

      // when
      result.current.changeParamValue('CollapseIntoSingleTicker')

      // then
      await waitFor(() =>
        expect(saveUserPreferenceReqSpy).toHaveBeenCalledWith(
          deepObjectContaining({
            body: {
              Data: {
                preProcessingReportParams: {
                  RepoNetting: 'CollapseIntoSingleTicker'
                }
              }
            }
          })
        )
      )
    })
  })
})
