import { useColumnFilters } from '@/core/redux/features/report/hooks'
import {
  allHandlers,
  apiPaths,
  combineWrappers,
  createHandler,
  deepObjectContaining,
  getInitializeDataWrapper,
  getReduxWrapper,
  setupMockServer,
  waitForCurrent,
  wrapWithUrl
} from '@/core/testing'
import { renderHook, act, waitFor } from '@testing-library/react'
import { expect, vi } from 'vitest'
import {
  getColumnInfoDummy,
  getDistinctColumnValuesResponseDummy,
  getFilterModelDummy,
  getReportDetailsResponseDummy
} from '@/core/testing/dummies'
import { IFilterModel } from '@/core/services/riskReport'
import {
  setLatestAsOfUtcResponse,
  setReportName,
  setSelectedTradingAreas
} from '@/core/redux/features/report'
import { WrapperComponentType } from '@/core/utils/types'
import { setupStore } from '@/core/redux/store'

describe('useColumnFilters', () => {
  const server = setupMockServer(...allHandlers)

  const reqSpy = vi.fn()

  let store: ReturnType<typeof setupStore>
  let wrapper: WrapperComponentType

  beforeEach(() => {
    const { wrapper: reduxWrapper, store: reduxStore } = getReduxWrapper()
    const { wrapper: initializeDataWrapper } = getInitializeDataWrapper()

    store = reduxStore
    wrapper = combineWrappers(
      wrapWithUrl('/reports/pnl-live'),
      reduxWrapper,
      initializeDataWrapper
    )

    // The API call is mocked to get 4 columns with the "Set" filter type:
    // i.e. ["Team", "TradingArea", "Strategy", "ProductType"]
    server.use(
      createHandler({
        method: 'get',
        path: apiPaths.getReportDetails,
        response: getReportDetailsResponseDummy({
          columnInfo: [
            getColumnInfoDummy({ column: 'Team', filterType: 'TextSet' }),
            getColumnInfoDummy({
              column: 'TradingArea',
              filterType: 'TextSet'
            }),
            getColumnInfoDummy({ column: 'Strategy', filterType: 'TextSet' }),
            getColumnInfoDummy({ column: 'ProductType', filterType: 'TextSet' })
          ]
        })
      })
    )

    // The response is all the available filter values for all columns.
    // This means that for testing purposes, there are only three columns with a few filter values.
    server.use(
      createHandler({
        method: 'post',
        path: apiPaths.getDistinctColumnValues,
        response: getDistinctColumnValuesResponseDummy({
          Team: ['ARNO', 'BALT'],
          TradingArea: ['ARNO', 'BALT', 'ESPO'],
          Strategy: ['ARNO', 'AUDCAD', 'AUDCHF'],
          ProductType: ['AssetSwap', 'Bond', 'CDS']
        }),
        reqSpy
      })
    )
  })

  afterEach(vi.clearAllMocks)

  describe('when a filter changes', () => {
    it('should save current filters', async () => {
      // given
      const filters = [
        getFilterModelDummy({ column: 'Team' }),
        getFilterModelDummy({ column: 'TradingArea' }),
        getFilterModelDummy({ column: 'Strategy' })
      ]

      const { result } = renderHook(
        () => useColumnFilters({ onFiltersUpdated: vi.fn }),
        { wrapper }
      )

      // when
      await waitForCurrent(result)

      // then
      expect(store.getState().report.appliedColumnFilters).toStrictEqual([])

      // when
      await act(() =>
        result.current.updateFilters({
          filters,
          currentlyFilteredColumn: 'Team'
        })
      )

      // then
      const { appliedColumnFilters } = store.getState().report
      expect(appliedColumnFilters).toHaveLength(3)
      expect(appliedColumnFilters).toStrictEqual(filters)
    })

    it('should fetch filter values for unfiltered columns with applied filters in the payload', async () => {
      // given
      const filters: IFilterModel[] = [getFilterModelDummy({ column: 'Team' })]

      const { result } = renderHook(
        () => useColumnFilters({ onFiltersUpdated: vi.fn }),
        { wrapper }
      )

      // when
      await waitForCurrent(result)
      await act(() =>
        result.current.updateFilters({
          filters,
          currentlyFilteredColumn: 'Team'
        })
      )

      // then
      expect(reqSpy).toHaveBeenCalledWith(
        deepObjectContaining({
          body: {
            columns: ['TradingArea', 'Strategy', 'ProductType'],
            filters
          }
        })
      )
    })

    it('should individually fetch filter values for previously filtered columns with applied filters in their payload, excluding the one for which the request is being made', async () => {
      // given;
      const filters = [
        // Currently applied filter:
        getFilterModelDummy({ column: 'ProductType' }),
        // Previously applied filters:
        getFilterModelDummy({ column: 'Team' }),
        getFilterModelDummy({ column: 'TradingArea' }),
        getFilterModelDummy({ column: 'Strategy' })
      ]

      const { result } = renderHook(
        () => useColumnFilters({ onFiltersUpdated: vi.fn }),
        { wrapper }
      )

      // when
      await waitForCurrent(result)
      await act(() =>
        result.current.updateFilters({
          filters,
          currentlyFilteredColumn: 'ProductType'
        })
      )

      // then
      const [productType, team, tradingArea, strategy] = filters

      expect(reqSpy).toHaveBeenCalledWith(
        deepObjectContaining({
          body: {
            columns: ['Team'],
            filters: [productType, tradingArea, strategy]
          }
        })
      )
      expect(reqSpy).toHaveBeenCalledWith(
        deepObjectContaining({
          body: {
            columns: ['TradingArea'],
            filters: [productType, team, strategy]
          }
        })
      )
      expect(reqSpy).toHaveBeenCalledWith(
        deepObjectContaining({
          body: {
            columns: ['Strategy'],
            filters: [productType, tradingArea, team]
          }
        })
      )
    })

    it('should not fetch filter values for the currently filtered column', async () => {
      // given
      const { result } = renderHook(
        () => useColumnFilters({ onFiltersUpdated: vi.fn }),
        { wrapper }
      )

      // when
      await waitForCurrent(result)
      await act(() =>
        result.current.updateFilters({
          filters: [],
          currentlyFilteredColumn: 'Team'
        })
      )

      // then
      expect(reqSpy).toHaveBeenCalledWith(
        deepObjectContaining({
          body: {
            columns: ['TradingArea', 'Strategy', 'ProductType']
          }
        })
      )
    })

    it('should call onFiltersUpdated when filters values are fetched', async () => {
      // given
      const onFiltersUpdatedSpy = vi.fn()

      const { result } = renderHook(
        () => useColumnFilters({ onFiltersUpdated: onFiltersUpdatedSpy }),
        { wrapper }
      )

      // when
      await waitForCurrent(result)
      await act(() =>
        result.current.updateFilters({
          filters: [],
          currentlyFilteredColumn: 'Team'
        })
      )

      // then
      expect(onFiltersUpdatedSpy).toHaveBeenCalled()
    })
  })

  describe('when report name, snapshot, or selected trading areas change', () => {
    it('should fetch filter values with updated report name in the endpoint', async () => {
      // given
      const { result } = renderHook(
        () => useColumnFilters({ onFiltersUpdated: vi.fn }),
        { wrapper }
      )

      // when
      await waitForCurrent(result)
      await act(() => store.dispatch(setReportName('Report2')))

      // then
      await waitFor(() =>
        expect(reqSpy).toHaveBeenCalledWith(
          deepObjectContaining({
            body: {
              columns: ['Team', 'TradingArea', 'Strategy', 'ProductType']
            }
          })
        )
      )
    })

    it('should fetch filter values with updated trading areas in the payload', async () => {
      // given
      const { result } = renderHook(
        () => useColumnFilters({ onFiltersUpdated: vi.fn }),
        { wrapper }
      )

      // when
      await waitForCurrent(result)
      await act(() =>
        store.dispatch(setSelectedTradingAreas(['BUIS', 'CAMA', 'CHEJ']))
      )

      // then
      await waitFor(() =>
        expect(reqSpy).toHaveBeenCalledWith(
          deepObjectContaining({
            body: {
              columns: ['Team', 'TradingArea', 'Strategy', 'ProductType'],
              tradingAreas: ['BUIS', 'CAMA', 'CHEJ']
            }
          })
        )
      )
    })

    it('should fetch filter values with updated snapshot in the payload', async () => {
      // given
      const { result } = renderHook(
        () => useColumnFilters({ onFiltersUpdated: vi.fn }),
        { wrapper }
      )

      // when
      await waitForCurrent(result)
      await act(() =>
        store.dispatch(
          setLatestAsOfUtcResponse({
            latestAsOfUtc: '2023-11-15T10:48:22.2649764Z'
          })
        )
      )

      // then
      await waitFor(() =>
        expect(reqSpy).toHaveBeenCalledWith(
          deepObjectContaining({
            body: {
              columns: ['Team', 'TradingArea', 'Strategy', 'ProductType'],
              snapshot: {
                asOfUtc: '2023-11-15T10:48:22.2649764Z'
              }
            }
          })
        )
      )
    })
  })
})
