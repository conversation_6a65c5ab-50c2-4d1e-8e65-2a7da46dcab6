import { useReportNameEffect } from '@/core/redux/features/report/hooks/useReportNameEffect'
import { ReduxWrapper } from '@/core/testing'
import { renderHook, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import * as routerUtils from '@/core/utils/router'
import * as reportSlice from '@/core/redux/features/report'

describe('useReportNameMutation hook', () => {
  it('test getting report name from the path params', async () => {
    vi.spyOn(routerUtils, 'useGetReportNameFromRoute').mockReturnValue(
      'PnlLive'
    )

    const spyOnSetReportName = vi.spyOn(reportSlice, 'setReportName')

    renderHook(useReportNameEffect, {
      wrapper: ReduxWrapper()
    })

    await waitFor(() => {
      expect(spyOnSetReportName).toHaveBeenCalledWith('PnlLive')
    })
  })
})
