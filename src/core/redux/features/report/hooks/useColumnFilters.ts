import { useCallback, useEffect } from 'react'
import {
  useAppDispatch,
  useAppSelect,
  useNotifications
} from '@/core/redux/hooks'
import { getRiskReportApi } from '@/core/services/riskReport/riskReportApi'
import {
  getSnapshot,
  isLiveModeOrParentLiveMode,
  preProcessingParamsSelector,
  reportPathSelector,
  selectedTradingAreasSelector,
  setAppliedColumnFilters,
  setDistinctColumnValues
} from '@/core/redux/features/report'
import { mergeWith, merge, pick, union, without, reduce } from 'lodash'
import { IFilterModel } from '@/core/services/riskReport'
import { useAppStore } from '@/core/redux/hooks/useAppStore'

interface UpdateFiltersPayload {
  filters: IFilterModel[]
  currentlyFilteredColumn: string
}

interface UseColumnFiltersParams {
  onFiltersUpdated?: () => void
}

export const useColumnFilters = ({
  onFiltersUpdated
}: UseColumnFiltersParams) => {
  const store = useAppStore()
  const dispatch = useAppDispatch()
  const selectedTradingAreas = useAppSelect(
    (state) => state.report.selectedTradingAreas
  )
  const reportName = useAppSelect((state) => state.report.reportName)
  const currentAsOfUtc = useAppSelect((state) => state.report.currentAsOfUtc)
  const { addDangerNotification } = useNotifications()

  const getDistinctColumnValues = useCallback(
    async (columns: string[], filters: IFilterModel[]) => {
      const state = store.getState()
      const selectedTradingAreas = selectedTradingAreasSelector(state)
      const preProcessingParams = preProcessingParamsSelector(state)
      const riskReportApi = getRiskReportApi(isLiveModeOrParentLiveMode(state))
      const snapshot = getSnapshot(state)
      const reportPath = reportPathSelector(state)

      const response = await riskReportApi.getDistinctColumnValues({
        reportName: reportPath,
        columns,
        snapshot,
        tradingAreas: selectedTradingAreas || [],
        filters: filters,
        preProcessingParams
      })

      return response?.distinctColumnValues
    },
    [store, addDangerNotification]
  )

  const fetchFilterValues = useCallback(
    async (filters: IFilterModel[], currentlyFilteredColumn?: string) => {
      const { distinctColumnValues, textSetColumnNames } =
        store.getState().report

      const filteredColumns = filters?.map((model) => model.column) ?? []
      const previouslyFilteredColumns = currentlyFilteredColumn
        ? without(filteredColumns, currentlyFilteredColumn)
        : filteredColumns
      const unfilteredColumns = without(textSetColumnNames, ...filteredColumns)

      let newValues = await getDistinctColumnValues(unfilteredColumns, filters)

      if (previouslyFilteredColumns.length) {
        const promises = previouslyFilteredColumns.map((colId) => {
          const filtersAppliedToOtherColumns = filters.filter(
            (model) => model.column !== colId
          )
          return getDistinctColumnValues([colId], filtersAppliedToOtherColumns)
        })

        const filteredColumnsNewValues = reduce(
          await Promise.all(promises),
          merge,
          {}
        )

        const filteredColumnsPreviousValues = pick(
          distinctColumnValues,
          previouslyFilteredColumns
        )

        const filteredColumnsUpdatedValues = mergeWith(
          filteredColumnsPreviousValues,
          filteredColumnsNewValues,
          union
        )

        newValues = { ...newValues, ...filteredColumnsUpdatedValues }
      }

      dispatch(
        setDistinctColumnValues({
          ...distinctColumnValues,
          ...newValues
        })
      )
    },
    [dispatch, getDistinctColumnValues]
  )

  const updateFilters = useCallback(
    async ({ filters, currentlyFilteredColumn }: UpdateFiltersPayload) => {
      dispatch(setAppliedColumnFilters(filters))
      await fetchFilterValues(filters, currentlyFilteredColumn)
      onFiltersUpdated && onFiltersUpdated()
    },
    [dispatch, fetchFilterValues, onFiltersUpdated]
  )

  /**
   * Refetches all filter values every time reportName, currentAsOfUtc, or selectedTradingAreas changes.
   */
  useEffect(() => {
    const { appliedColumnFilters } = store.getState().report
    fetchFilterValues(appliedColumnFilters).then(
      () => onFiltersUpdated && onFiltersUpdated()
    )
  }, [
    store,
    fetchFilterValues,
    reportName,
    currentAsOfUtc,
    selectedTradingAreas
  ])
  return { updateFilters }
}
