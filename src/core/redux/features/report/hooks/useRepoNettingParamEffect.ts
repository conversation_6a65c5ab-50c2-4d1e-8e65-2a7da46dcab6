import { useAppSelect } from '@/core/redux/hooks'
import { IPreProcessingParamName } from '@/core/services/riskReport'
import { useDispatch } from 'react-redux'
import {
  setPreProcessingReportParams,
  setUserPreferencesChanged
} from '@/core/redux/features/users'
import { useMemo } from 'react'
import { setPreProcessingParams } from '@/core/redux/features/report'

export const useRepoNettingParamEffect = () => {
  const preProcessingReportParams = useAppSelect(
    (state) => state.users.userPreferences.preProcessingReportParams
  )

  const repoNettingValueFromPreference =
    preProcessingReportParams &&
    preProcessingReportParams[IPreProcessingParamName.RepoNetting]

  const preProcessingParamSpecs = useAppSelect(
    (state) => state.report.preProcessingParamSpecs
  )

  const dispatch = useDispatch()

  const repoNettingParam = preProcessingParamSpecs.find(
    (spec) => spec.paramName === IPreProcessingParamName.RepoNetting
  )

  const displayLabel = repoNettingParam?.displayLabel

  const valuesSpec = repoNettingParam?.paramValues

  const currentValueSpec = useMemo(() => {
    if (!valuesSpec) {
      return
    }

    return valuesSpec.find(
      (spec) => spec.value === repoNettingValueFromPreference
    )
  }, [valuesSpec, repoNettingValueFromPreference])

  const changeParamValue = (value: string) => {
    const reportParams = {
      [IPreProcessingParamName.RepoNetting]: value
    }

    dispatch(setPreProcessingReportParams(reportParams))
    dispatch(setUserPreferencesChanged(true))
    dispatch(setPreProcessingParams({ reportParams: reportParams }))
  }

  return { displayLabel, valuesSpec, currentValueSpec, changeParamValue }
}
