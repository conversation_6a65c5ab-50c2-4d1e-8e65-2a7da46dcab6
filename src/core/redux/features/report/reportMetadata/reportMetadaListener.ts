import { AppStartListening } from '@/core/redux/middleware/listenerMiddleware'
import {
  currentAsOfUtcSelector,
  isConnectedSubscriberSelector,
  preProcessingParamsSelector
} from '@/core/redux/selectors'
import { fetchMetadataThunk } from './thunk/fetchMetadataThunk'

export const addReportMetadataListeners = (
  startListening: AppStartListening
) => {
  startListening({
    predicate: (_, currentState, previousState) => {
      if (isConnectedSubscriberSelector(currentState)) {
        // connected subscriber gets metadata from Glue sync events
        return false
      }

      // predicate implemented in verbose way for clarity
      const currentAsOfUtc = currentAsOfUtcSelector(currentState)
      const currentAsOfUtcChanged =
        currentAsOfUtc !== currentAsOfUtcSelector(previousState)
      const preProcessingParamsChanged =
        preProcessingParamsSelector(currentState) !==
        preProcessingParamsSelector(previousState)

      return (
        Boolean(currentAsOfUtc) &&
        (currentAsOfUtcChanged || preProcessingParamsChanged)
      )
    },
    effect: async (_, { dispatch }) => {
      dispatch(fetchMetadataThunk())
    }
  })
}
