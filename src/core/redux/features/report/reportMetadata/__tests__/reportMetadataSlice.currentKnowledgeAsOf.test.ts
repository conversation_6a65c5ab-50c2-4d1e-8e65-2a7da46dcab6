import { reportMetadataSlice } from '../reportMetadataSlice'
import { getKnowledgeAsOfValuesThunk } from '../thunk/getKnowledgeAsOfValuesThunk'

describe('getKnowledgeAsOfValuesThunk', () => {
  it('should set currentKnowledgeAsOf to the first value in knowledgeAsOfValues', () => {
    const mockKnowledgeAsOfDates = ['2022-01-01', '2022-02-01', '2022-03-01']

    const fulfilledAction = {
      type: getKnowledgeAsOfValuesThunk.fulfilled.type,
      payload: { knowledgeAsOfDates: mockKnowledgeAsOfDates }
    }

    const initialState = reportMetadataSlice.getInitialState()
    const state = reportMetadataSlice.reducer(initialState, fulfilledAction)

    expect(state.knowledgeAsOf.currentKnowledgeAsOf).toBe(
      mockKnowledgeAsOfDates[0]
    )
  })
  it('should not fail and currentKnowledgeAsOf should be undefined if knowledgeAsOfValues is empty', () => {
    const fulfilledAction = {
      type: getKnowledgeAsOfValuesThunk.fulfilled.type,
      payload: { knowledgeAsOfDates: [] }
    }

    // Set currentKnowledgeAsOf to a value before dispatching the action
    const initialState = {
      ...reportMetadataSlice.getInitialState(),
      knowledgeAsOf: {
        ...reportMetadataSlice.getInitialState().knowledgeAsOf,
        currentKnowledgeAsOf: '2022-01-01'
      }
    }
    expect(initialState.knowledgeAsOf.currentKnowledgeAsOf).toBe('2022-01-01')

    const state = reportMetadataSlice.reducer(initialState, fulfilledAction)

    expect(state.knowledgeAsOf.knowledgeAsOfValues).toEqual([])
    expect(state.knowledgeAsOf.currentKnowledgeAsOf).toBeUndefined()
  })
})
