import { RootState, setupStore } from '@/core/redux/store'
import { api<PERSON><PERSON><PERSON>, create<PERSON><PERSON><PERSON>, setupMockServer } from '@/core/testing'
import { getMetadataResponseDummy } from '@/core/testing/dummies'
import {
  currentAsOfUtcSelector,
  setCurrentAsOfLocal
} from '@/core/redux/features/report'
import { waitFor } from '@testing-library/react'
import { vi } from 'vitest'

const metadataSelector = (state: RootState) => state.reportMetadata.metadata

describe('reportMetadaListener', () => {
  const server = setupMockServer()

  beforeEach(() => {
    vi.resetAllMocks()
  })
  it('should dispatch fetchMetadata thunk if currentAsOfUtc change ', async () => {
    const store = setupStore()
    const expectedTradesDate = '1900-01-05T07:33:30.4063813Z'
    server.use(
      createHandler({
        method: 'post',
        path: apiPaths.getMetadata,
        response: getMetadataResponseDummy({
          metadata: { Trades: expectedTradesDate },
          displayMetadata: ['Trades']
        } as any)
      })
    )

    store.dispatch(setCurrentAsOfLocal('2020-09-19'))

    await waitFor(() => {
      expect(metadataSelector(store.getState())!['Trades'].rawValue).toBe(
        expectedTradesDate
      )
    })
  })

  it('should not dispatch for connected subscriber ', async () => {
    const store = setupStore({
      context: {
        isConnectedToGlueChannel: true,
        isConnectedSubscriber: true,
        rawMetadata: {
          Trades: 'oryginal value'
        }
      },
      report: {
        publishSyncData: false
      } as any,
      reportMetadata: {
        metadata: {
          Trades: {
            rawValue: 'oryginal value'
          }
        }
      }
    })
    const expectedTradesDate = '1900-01-05T07:33:30.4063813Z'

    server.use(
      createHandler({
        method: 'post',
        path: apiPaths.getMetadata,
        response: getMetadataResponseDummy({
          metadata: { Trades: expectedTradesDate }
        } as any)
      })
    )

    store.dispatch(setCurrentAsOfLocal('2020-09-20'))

    await waitFor(() => {
      // waiting for store update to be sure that fetchMetadataThunk was not dispatched
      expect(currentAsOfUtcSelector(store.getState())).toBe(
        '2020-09-20T00:00:00.000Z'
      )
    })

    expect(metadataSelector(store.getState())!['Trades'].rawValue).toBe(
      'oryginal value'
    )
  })
})
