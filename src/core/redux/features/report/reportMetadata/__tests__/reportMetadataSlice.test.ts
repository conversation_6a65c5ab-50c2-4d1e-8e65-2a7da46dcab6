import { initializeReportDataThunk } from '@/core/redux/features/report/thunk/initializeReportDataThunk'
import { getTestDate } from '@/core/testing/date'
import {
  formatDateToLocal,
  formatDateToUtc,
  formatTimestampToLocalWithMilliseconds,
  formatTimestampToUTCWithMilliseconds
} from '@/core/utils/date'
import { expect } from 'vitest'
import {
  reportMetadataSlice,
  setKnowledgeAsOfUIVisible
} from '../reportMetadataSlice'
import { getKnowledgeAsOfValuesThunk } from '../thunk/getKnowledgeAsOfValuesThunk'

describe('getMetadata', () => {
  it('should set metadata on getMetadata matchFulfilled', async () => {
    const testDate1 = getTestDate(1)
    const testDate2 = getTestDate(2)
    const testDate3 = getTestDate(3)

    const mockPayload = {
      metadata: {
        'Execution Time': testDate1.toISOString(),
        'Market Data': testDate2.toISOString(),
        Trades: testDate3.toISOString(),
        'Fx Exposure': '2020-01-01T00:00:00.000Z'
      },
      displayMetadata: ['Execution Time', 'Market Data', 'Trades']
    }

    const fulfilledAction = {
      type: initializeReportDataThunk.fulfilled,
      payload: { metadata: mockPayload }
    }

    const state = reportMetadataSlice.reducer(undefined, fulfilledAction)

    expect(state.metadata).toStrictEqual({
      'Execution Time': {
        localDate: formatDateToLocal(testDate1),
        localDatePrecise: formatTimestampToLocalWithMilliseconds(
          testDate1.toISOString()
        ),
        utcString: formatDateToUtc(testDate1),
        utcStringPrecise: formatTimestampToUTCWithMilliseconds(
          testDate1.toISOString()
        ),
        rawValue: testDate1.toISOString()
      },
      'Market Data': {
        localDate: formatDateToLocal(testDate2),
        localDatePrecise: formatTimestampToLocalWithMilliseconds(
          testDate2.toISOString()
        ),
        utcString: formatDateToUtc(testDate2),
        utcStringPrecise: formatTimestampToUTCWithMilliseconds(
          testDate2.toISOString()
        ),
        rawValue: testDate2.toISOString()
      },
      Trades: {
        localDate: formatDateToLocal(testDate3),
        localDatePrecise: formatTimestampToLocalWithMilliseconds(
          testDate3.toISOString()
        ),
        utcString: formatDateToUtc(testDate3),
        utcStringPrecise: formatTimestampToUTCWithMilliseconds(
          testDate3.toISOString()
        ),
        rawValue: testDate3.toISOString()
      }
    })
  })
})
describe('getKnowledgeAsOfValuesThunk', () => {
  it('should update knowledgeAsOfValues when fulfilled', async () => {
    const mockKnowledgeAsOfDates = ['2022-01-01', '2022-02-01', '2022-03-01']

    const fulfilledAction = {
      type: getKnowledgeAsOfValuesThunk.fulfilled.type,
      payload: { knowledgeAsOfDates: mockKnowledgeAsOfDates }
    }

    const initialState = {
      ...reportMetadataSlice.getInitialState(),
      knowledgeAsOf: {
        ...reportMetadataSlice.getInitialState().knowledgeAsOf,
        knowledgeAsOfValues: []
      }
    }

    const state = reportMetadataSlice.reducer(initialState, fulfilledAction)

    expect(state.knowledgeAsOf.knowledgeAsOfValues).toEqual(
      mockKnowledgeAsOfDates
    )
  })
})

describe('setKnowledgeAsOfUIVisible', () => {
  it('should set isKnowledgeAsOfUIVisible to true', () => {
    const initialState = reportMetadataSlice.getInitialState()
    expect(initialState.knowledgeAsOf.isKnowledgeAsOfUIVisible).toBe(false)

    const state = reportMetadataSlice.reducer(
      initialState,
      setKnowledgeAsOfUIVisible(true)
    )

    expect(state.knowledgeAsOf.isKnowledgeAsOfUIVisible).toBe(true)
  })

  it('should set isKnowledgeAsOfUIVisible to false', () => {
    const initialState = {
      ...reportMetadataSlice.getInitialState(),
      knowledgeAsOf: {
        ...reportMetadataSlice.getInitialState().knowledgeAsOf,
        isKnowledgeAsOfUIVisible: true
      }
    }

    const state = reportMetadataSlice.reducer(
      initialState,
      setKnowledgeAsOfUIVisible(false)
    )

    expect(state.knowledgeAsOf.isKnowledgeAsOfUIVisible).toBe(false)
  })

  it('should not modify other state properties', () => {
    const initialState = {
      ...reportMetadataSlice.getInitialState(),
      metadata: { Test: { rawValue: '2022-01-01' } }
    }

    const state = reportMetadataSlice.reducer(
      initialState,
      setKnowledgeAsOfUIVisible(true)
    )

    expect(state.metadata).toEqual(initialState.metadata)
    expect(state.knowledgeAsOf.knowledgeAsOfValues).toEqual([])
    expect(state.knowledgeAsOf.currentKnowledgeAsOf).toBeUndefined()
  })
})
