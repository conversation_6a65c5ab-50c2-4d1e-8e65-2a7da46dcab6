import {
  isKnowledgeAsOfUIVisibleSelector,
  currentKnowledgeAsOfSelector,
  knowledgeAsOfValuesSelector
} from '../../selectors'
import { RootState } from '@/core/redux/store'

describe('reportMetadata selectors', () => {
  // Mock state for testing selectors
  const mockState = {
    reportMetadata: {
      knowledgeAsOf: {
        isKnowledgeAsOfUIVisible: true,
        currentKnowledgeAsOf: '2023-01-01',
        knowledgeAsOfValues: ['2023-01-01', '2023-02-01', '2023-03-01']
      }
    }
  } as RootState

  describe('isKnowledgeAsOfUIVisibleSelector', () => {
    it('should return the visibility state of knowledge as of UI', () => {
      const result = isKnowledgeAsOfUIVisibleSelector(mockState)
      expect(result).toBe(true)
    })
  })

  describe('currentKnowledgeAsOfSelector', () => {
    it('should return the current knowledge as of value', () => {
      const result = currentKnowledgeAsOfSelector(mockState)
      expect(result).toBe('2023-01-01')
    })
  })

  describe('knowledgeAsOfValuesSelector', () => {
    it('should return the array of knowledge as of values', () => {
      const result = knowledgeAsOfValuesSelector(mockState)
      expect(result).toEqual(['2023-01-01', '2023-02-01', '2023-03-01'])
    })
  })
})
