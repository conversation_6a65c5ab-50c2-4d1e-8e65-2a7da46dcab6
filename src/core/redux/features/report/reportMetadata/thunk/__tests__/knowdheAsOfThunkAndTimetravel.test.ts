import { describe, it, expect, vi } from 'vitest'
import { getKnowledgeAsOfValuesThunk } from '../getKnowledgeAsOfValuesThunk'
import * as uiBackendClientMock from '@/core/services/http/uiBackendClient'

const mockDispatch = vi.fn()

// Add mock for timeTravel module
vi.mock('@/core/services/http/uiBackendClient', () => ({
  getTimeTravelUiBackendClient: vi.fn(),
  getUiBackendClient: vi.fn()
}))

describe('getKnowledgeAsOfValuesThunk', () => {
  it('should use the timetravel client', async () => {
    // Mock state

    const mockState = {
      report: {
        currentAsOfUtc: '2023-01-01T00:00:00Z',
        reportName: 'testReport',
        eodReportType: 'eodReportType'
      }
    }

    // Setup the time travel client mock
    const timetravelGetMock = vi.fn()
    const timeTravelMock = {
      get: timetravelGetMock
    }
    ;(
      uiBackendClientMock.getTimeTravelUiBackendClient as jest.Mock
    ).mockReturnValue(timeTravelMock)

    // Execute thunk
    await getKnowledgeAsOfValuesThunk()(
      mockDispatch,
      () => mockState as any,
      undefined
    )

    // Verify the time travel client's get method was called with correct parameters
    expect(timetravelGetMock).toHaveBeenCalledWith(
      '/api/RiskReports/eodReportType/KnowledgeDatesAsOf?asOf=2023-01-01T22:59:59.000Z'
    )
  })
})
