import { describe, it, expect, vi, beforeEach } from 'vitest'
import { getKnowledgeAsOfValuesThunk } from '../getKnowledgeAsOfValuesThunk'
import { setupMockServer, createHandler, apiPaths } from '@/core/testing'
import { KnowledgeDatesAsOfResponse } from '@/core/services/riskReport/types'

// Setup mock server
const server = setupMockServer()

describe('getKnowledgeAsOfValuesThunk', () => {
  // Mock dispatch function
  const mockDispatch = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should call API with correct parameters when currentAsOfUtc is available', async () => {
    // Mock state with all required values
    const mockState = {
      report: {
        currentAsOfUtc: '2023-01-01T00:00:00Z',
        reportName: 'testReport',
        eodReportType: 'eodReportType'
      }
    }

    // Mock API response
    const apiResponse: KnowledgeDatesAsOfResponse = {
      knowledgeAsOfDates: ['2023-01-01T00:00:00Z']
    }

    // Spy on request to verify parameters
    const reqSpy = vi.fn()

    // Setup mock handler for knowledge dates API
    server.use(
      createHandler({
        method: 'get',
        path: apiPaths.getKnowledgeDatesAsOf,
        response: apiResponse,
        reqSpy
      })
    )

    // Execute thunk
    const result = await getKnowledgeAsOfValuesThunk()(
      mockDispatch,
      () => mockState as any,
      undefined
    )

    expect(reqSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        pathname: '/api/RiskReports/eodReportType/KnowledgeDatesAsOf',
        searchParams: expect.objectContaining({
          asOf: '2023-01-01T22:59:59.000Z'
        })
      })
    )
    // Check result payload
    expect(result.payload).toEqual(apiResponse)
  })

  it('should not call API and return empty array when currentAsOfUtc is missing', async () => {
    // Mock state without currentAsOfUtc
    const mockState = {
      report: {
        currentAsOfUtc: null,
        reportName: 'testReport',
        eodReportType: 'eodReportType'
      }
    }

    // Spy to verify API is not called
    const reqSpy = vi.fn()

    server.use(
      createHandler({
        method: 'get',
        path: apiPaths.getKnowledgeDatesAsOf,
        response: {},
        reqSpy
      })
    )

    // Execute thunk
    const result = await getKnowledgeAsOfValuesThunk()(
      mockDispatch,
      () => mockState as any,
      undefined
    )

    // Verify API wasn't called and we got empty array
    expect(reqSpy).not.toHaveBeenCalled()
    expect(result.payload).toEqual({ knowledgeAsOfDates: [] })
  })

  it('should use reportName when eodReportType is not available', async () => {
    // Mock state without eodReportType
    const mockState = {
      report: {
        currentAsOfUtc: '2023-01-01T00:00:00Z',
        reportName: 'testReport',
        eodReportType: null
      }
    }

    // Mock API response
    const apiResponse: KnowledgeDatesAsOfResponse = {
      knowledgeAsOfDates: ['2023-01-01T00:00:00Z']
    }

    // Spy on request to verify parameters
    const reqSpy = vi.fn()

    server.use(
      createHandler({
        method: 'get',
        path: apiPaths.getKnowledgeDatesAsOf,
        response: apiResponse,
        reqSpy
      })
    )

    // Execute thunk
    const result = await getKnowledgeAsOfValuesThunk()(
      mockDispatch,
      () => mockState as any,
      undefined
    )

    // Verify reportName was used as fallback
    expect(reqSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        pathname: '/api/RiskReports/testReport/KnowledgeDatesAsOf'
      })
    )

    expect(result.payload).toEqual(apiResponse)
  })

  it('should return empty array when API returns null', async () => {
    // Mock state
    const mockState = {
      report: {
        currentAsOfUtc: '2023-01-01T00:00:00Z',
        reportName: 'testReport',
        eodReportType: 'eodReportType'
      }
    }

    // Mock API returning null
    server.use(
      createHandler({
        method: 'get',
        path: apiPaths.getKnowledgeDatesAsOf,
        response: null
      })
    )

    // Execute thunk
    const result = await getKnowledgeAsOfValuesThunk()(
      mockDispatch,
      () => mockState as any,
      undefined
    )

    // Verify empty array is returned
    expect(result.payload).toEqual({ knowledgeAsOfDates: [] })
  })
})
