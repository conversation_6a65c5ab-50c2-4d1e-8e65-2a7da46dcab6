import { setupStore } from '@/core/redux/store'
import { fetchMetadataThunk } from '../fetchMetadataThunk'
import { allHandlers, setupMockServer } from '@/core/testing'
import { getMetadataResponseDummy } from '@/core/testing/dummies'

describe('fetchMetadataThunk', () => {
  // Setup the mock server with all the handlers
  const server = setupMockServer(...allHandlers)

  it('should call riskReportApi.getMetadata with correct arguments', async () => {
    // Arrange
    const store = setupStore()
    const metadataDummy = getMetadataResponseDummy()

    // Act
    await store.dispatch(fetchMetadataThunk())
    const state = store.getState()

    // Assert
    expect(state.reportMetadata.metadata['Execution Time'].rawValue).toEqual(
      metadataDummy.metadata['Execution Time']
    )
    expect(state.reportMetadata.metadata['Market Data'].rawValue).toEqual(
      metadataDummy.metadata['Market Data']
    )
  })
})
