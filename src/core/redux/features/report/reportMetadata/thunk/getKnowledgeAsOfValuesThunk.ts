// src/core/redux/features/report/reportMetadata/thunk/getKnowledgeAsOfValuesThunk.ts
import { createAsyncThunk } from '@reduxjs/toolkit'
import { RootState } from '@/core/redux/store'
import { getRiskReportApi } from '@/core/services/riskReport/riskReportApi'
import { KnowledgeDatesAsOfResponse } from '@/core/services/riskReport/types'
import {
  currentAsOfUtcSelector,
  eodReportTypeSelector,
  reportNameSelector
} from '../../selectors'
import { showErrorModal } from '../../../notifications'
import { AxiosError } from 'axios'
import { getEODCloseDateTime } from '@/components/TopBar/CloseTimeButton/closeTimeUtils'

export const getKnowledgeAsOfValuesThunk =
  createAsyncThunk<KnowledgeDatesAsOfResponse>(
    'reportMetadata/getKnowledgeAsOfValues',
    async (_, { getState, dispatch }) => {
      const state = getState() as RootState
      const currentAsOfUtc = currentAsOfUtcSelector(state)
      const reportName = reportNameSelector(state)
      const eodReportType = eodReportTypeSelector(state) || reportName
      const emptyResponse = {
        knowledgeAsOfDates: []
      }

      if (!currentAsOfUtc) {
        return emptyResponse
      }

      try {
        // Use the risk report API service
        // (live mode is not important, getKnowledgeAsOfDates is always from timetravelClient)
        const riskReportApi = getRiskReportApi(true)

        const result = await riskReportApi.getKnowledgeDatesAsOf(
          eodReportType,
          {
            asOf: getEODCloseDateTime(currentAsOfUtc).toISOString()
          }
        )

        if (!result?.knowledgeAsOfDates?.length) {
          const text = `No knowledge as of dates found for report "${reportName}" with as of date "${currentAsOfUtc}".`
          dispatch(showErrorModal(text))
          return emptyResponse
        }

        return result
      } catch (error: unknown) {
        let message = 'Failed to fetch knowledge as of dates.'
        if (
          error &&
          typeof error === 'object' &&
          'isAxiosError' in error &&
          (error as AxiosError).isAxiosError
        ) {
          const axiosError = error as AxiosError
          message = (axiosError.response?.data as string) || message
        }
        dispatch(showErrorModal(message))
      }
      return emptyResponse
    }
  )
