import { createAsyncThunk } from '@reduxjs/toolkit'
import { getRiskReportApi } from '@/core/services/riskReport/riskReportApi'
import {
  currentAsOfUtcSelector,
  getSnapshot,
  isConnectedSubscriberSelector,
  isLiveModeOrParentLiveMode,
  latestLongPollAsOfUtcSelector,
  preProcessingParamsSelector,
  reportPathSelector
} from '@/core/redux/selectors'
import { RootState } from '@/core/redux/store'
import { logger } from '@/core/utils/logger'
import { publishToChannel } from '@/core/services/glue'
import {
  MetadataUpdateEvent,
  SyncDataEventType
} from '@/core/services/glue/types'

export const fetchMetadataThunk = createAsyncThunk(
  'reportMetadata/fetchMetadataThunk',
  async (_, { getState }) => {
    const state = getState() as RootState
    const preProcessingParams = preProcessingParamsSelector(state)
    const currentAsOfUtc = currentAsOfUtcSelector(state)
    const latestLongPollAsOfUtc = latestLongPollAsOfUtcSelector(state)
    const publishSyncData = state.report.publishSyncData
    const snapshot = getSnapshot(state)
    const reportPath = reportPathSelector(state)

    if (isConnectedSubscriberSelector(state)) {
      return
    }

    const riskReportApi = getRiskReportApi(isLiveModeOrParentLiveMode(state))
    const metadataResponse = await riskReportApi.getMetadata(reportPath, {
      preProcessingParams,
      snapshot
    })

    if (publishSyncData) {
      publishToChannel({
        type: SyncDataEventType.MetadataUpdate,
        metadata: metadataResponse
      } as MetadataUpdateEvent)
    }

    if (currentAsOfUtc == latestLongPollAsOfUtc) {
      // The metadata response contains the timestamps of upstream events from the latest live notification,
      // not the metadata of some time travel request or from starting the app.
      // That means we can use it to report live update end-to-end latency metrics.

      const now = Date.now()

      const calcDiffMs = (metadataStr: string | number | undefined) => {
        const time = metadataStr ? new Date(metadataStr) : undefined
        return time ? now - time.getTime() : undefined
      }

      const calcLatencyMs = calcDiffMs(
        metadataResponse?.metadata['Execution Time']
      )
      const tradeLatencyMs = calcDiffMs(metadataResponse?.metadata['Trades'])
      const marketLatencyMs = calcDiffMs(
        metadataResponse?.metadata['Market Data']
      )

      const latencyObj = {
        Latency: {
          SinceCalcStartMs: calcLatencyMs,
          SinceTradeSnapshotMs: tradeLatencyMs,
          SinceMarketSnapshotMs: marketLatencyMs
        }
      }

      logger.info('measure latency', latencyObj)
    }

    return metadataResponse
  }
)
