import { createAsyncThunk } from '@reduxjs/toolkit'
import { reportNameSelector } from '@/core/redux/selectors'
import { RootState } from '@/core/redux/store'
import { IMetaDataResponse } from '@/core/services/riskReport'
import { setMetadataFromGlue } from '../reportMetadataSlice'

export const setSubscriberMetadataThunk = createAsyncThunk(
  'reportMetadata/setSubscriberMetadataThunk',
  async (metadata: IMetaDataResponse, { getState, dispatch }) => {
    const state = getState() as RootState
    const reportName = reportNameSelector(state)

    // Filter reportName from metadata.metadata
    const subscriberMetadata = metadata.metadata[reportName]

    // Dispatch the filtered metadata
    dispatch(
      setMetadataFromGlue({
        ...metadata,
        metadata: { [reportName]: subscriberMetadata }
      })
    )
  }
)
