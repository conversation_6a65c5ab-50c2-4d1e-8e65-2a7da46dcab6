import { RootState } from '@/core/redux/store'

export const isKnowledgeAsOfUIVisibleSelector = (state: RootState) =>
  state.reportMetadata.knowledgeAsOf.isKnowledgeAsOfUIVisible

export const currentKnowledgeAsOfSelector = (state: RootState) =>
  state.reportMetadata.knowledgeAsOf.currentKnowledgeAsOf

export const knowledgeAsOfValuesSelector = (state: RootState) =>
  state.reportMetadata.knowledgeAsOf.knowledgeAsOfValues
