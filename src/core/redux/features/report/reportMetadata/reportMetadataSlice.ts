import { IMetaDataResponse } from '@/core/services/riskReport'
import {
  formatDateToLocal,
  formatDateToUtc,
  formatTimestampToLocalWithMilliseconds,
  formatTimestampToUTCWithMilliseconds
} from '@/core/utils/date'
import { PayloadAction, createSlice } from '@reduxjs/toolkit'
import { isValid, parseISO } from 'date-fns'
import { mapValues, pickBy } from 'lodash'
import { IMetadata } from './types'
import { fetchMetadataThunk } from './thunk/fetchMetadataThunk'
import {
  InitializeReportData,
  initializeReportDataThunk
} from '../thunk/initializeReportDataThunk'
import { getKnowledgeAsOfValuesThunk } from './thunk/getKnowledgeAsOfValuesThunk'

export interface ReportMetadataState {
  knowledgeAsOf: {
    currentKnowledgeAsOf?: string
    knowledgeAsOfValues: string[]
    isKnowledgeAsOfUIVisible: boolean
  }
  metadata: IMetadata | null
  parentMetadata: IMetadata | null
}

export const initialState: ReportMetadataState = {
  knowledgeAsOf: {
    currentKnowledgeAsOf: undefined,
    knowledgeAsOfValues: [],
    isKnowledgeAsOfUIVisible: false
  },
  metadata: null,
  parentMetadata: null
}

const getValidMetadataFromPayload = (payload: IMetaDataResponse): IMetadata => {
  const { metadata, displayMetadata } = payload

  const isDisplayMetadata = (key: string) => displayMetadata?.includes(key)

  const isValidMetadata = (
    value: string | number,
    key: string
  ): value is string =>
    typeof value === 'string' &&
    isValid(parseISO(value)) &&
    isDisplayMetadata(key)

  const validMetadata = pickBy(metadata, isValidMetadata)

  return mapValues(validMetadata, (value: string) => ({
    localDate: formatDateToLocal(new Date(value)),
    localDatePrecise: formatTimestampToLocalWithMilliseconds(value),
    utcString: formatDateToUtc(new Date(value)),
    utcStringPrecise: formatTimestampToUTCWithMilliseconds(value),
    rawValue: value
  }))
}

const setMetadataReducer = (
  state: ReportMetadataState,
  { payload }: PayloadAction<IMetaDataResponse | undefined>
) => {
  if (payload) {
    const validMetadata = getValidMetadataFromPayload(payload)

    state.metadata = validMetadata
  }
}

const setMetadataFromGlueReducer = (
  state: ReportMetadataState,
  { payload }: PayloadAction<IMetaDataResponse>
) => {
  const validMetadata = getValidMetadataFromPayload(payload)

  state.parentMetadata = validMetadata
}

const setIsInitializedReducer = (
  state: ReportMetadataState,
  { payload }: PayloadAction<InitializeReportData>
) => {
  if (payload.metadata) {
    setMetadataReducer(state, {
      payload: payload.metadata,
      type: ''
    })
  }
}

// redux slice that holds report metadata
export const reportMetadataSlice = createSlice({
  name: 'reportMetadata',
  initialState,
  reducers: {
    setMetadataFromGlue: setMetadataFromGlueReducer,
    setKnowledgeAsOfUIVisible: (
      state: ReportMetadataState,
      { payload }: PayloadAction<boolean>
    ) => {
      state.knowledgeAsOf.isKnowledgeAsOfUIVisible = payload
    },
    setCurrentKnowledgeAsOf: (
      state: ReportMetadataState,
      { payload }: PayloadAction<string>
    ) => {
      const parsedDate = parseISO(payload)
      if (isValid(parsedDate)) {
        state.knowledgeAsOf.currentKnowledgeAsOf = payload
      }
    }
  },
  extraReducers(builder) {
    builder.addCase(
      initializeReportDataThunk.fulfilled,
      setIsInitializedReducer
    )
    builder.addCase(fetchMetadataThunk.fulfilled, setMetadataReducer)
    builder.addCase(getKnowledgeAsOfValuesThunk.fulfilled, (state, action) => {
      const values = action.payload.knowledgeAsOfDates

      state.knowledgeAsOf.knowledgeAsOfValues = values
      state.knowledgeAsOf.currentKnowledgeAsOf = values[0]
    })
  }
})

export const {
  setMetadataFromGlue,
  setKnowledgeAsOfUIVisible,
  setCurrentKnowledgeAsOf
} = reportMetadataSlice.actions
export const reportMetadataReducer = reportMetadataSlice.reducer
