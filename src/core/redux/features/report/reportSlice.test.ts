import {
  InitializeReportData,
  initializeReportDataThunk
} from '@/core/redux/features/report/thunk/initializeReportDataThunk'
import { getTestDate } from '@/core/testing/date'
import {
  formatDateToLocal,
  formatDateToUtc,
  formatTimestampToLocalWithMilliseconds,
  formatTimestampToUTCWithMilliseconds
} from '@/core/utils/date'
import { expect } from 'vitest'
import {
  reportSlice,
  ReportState,
  setCurrentAsOfLocal,
  setLatestAsOfUtcResponse,
  setReportName
} from './reportSlice'

describe('reportSlice', () => {
  let initialState: ReportState
  beforeEach(() => {
    initialState = {
      ...initialState,
      lastAsOfUtcMetadata: {
        receivedTime: null,
        lastDataUpdateTime: null,
        error: null
      },
      reportName: 'default'
    }
  })
  it('set Report Name', () => {
    const expectedState = {
      reportName: 'test'
    }
    expect(
      reportSlice.reducer(initialState, setReportName('test'))
    ).toMatchObject(expectedState)
  })

  it('set current as of utc for undefined payload', () => {
    const actual = reportSlice.reducer(
      initialState,
      setLatestAsOfUtcResponse({ latestAsOfUtc: '' })
    )
    expect(actual).not.toHaveProperty('currentAsOfUtc')
  })

  it('set current as of utc for defined payload', () => {
    const expectedState = {
      reportName: 'default',
      currentAsOfUtc: 'test'
    }
    expect(
      reportSlice.reducer(
        initialState,
        setLatestAsOfUtcResponse({ latestAsOfUtc: 'test' })
      )
    ).toMatchObject(expectedState)
  })

  // Tests that the state's currentAsOfUtc property is set to the ISO string representation of the payload date
  it('should set currentAsOfUtc to the ISO string representation of setCurrentAsOfLocal payload', () => {
    const testDate = getTestDate(1)
    const payload = testDate.toLocaleString('en-US')

    const newState = reportSlice.reducer(
      initialState,
      setCurrentAsOfLocal(payload)
    )
    expect(newState.currentAsOfUtc).toEqual(testDate.toISOString())
  })

  it('should correctly initialize report data when initializeReportDataThunk is fulfilled', async () => {
    const testDate1 = getTestDate(1)

    const mockPayload = {
      latestAsOfUtcResponse: { latestAsOfUtc: testDate1.toISOString() },
      tradingAreas: ['test1', 'test2'],
      selectedTradingAreas: ['test1'],
      reportName: 'test'
    } as InitializeReportData

    const fulfilledAction = {
      type: initializeReportDataThunk.fulfilled,
      payload: mockPayload
    }

    const state = reportSlice.reducer(undefined, fulfilledAction)

    expect(state.currentAsOfUtc).toBe(testDate1.toISOString())
    expect(state.tradingAreas).toStrictEqual(['test1', 'test2'])
    expect(state.selectedTradingAreas).toStrictEqual(['test1'])
    expect(state.reportName).toBe('test')
  })

  it('should disable live mode even if it was previously enabled', () => {
    // Given: an initial state with live mode enabled and a local date payload
    initialState.isLiveModeEnabled = true
    const localDate = new Date(2023, 5, 15, 8, 20, 0) // June 15, 2023 08:20:00 local time
    const payload = localDate.toLocaleString('en-US')

    // When: dispatching setCurrentAsOfLocal action
    const newState = reportSlice.reducer(
      {
        ...initialState,
        isLiveModeEnabled: true
      },
      setCurrentAsOfLocal(payload)
    )

    // Then: live mode should be false after setting a custom date
    expect(newState.isLiveModeEnabled).toBe(false)
  })
})
