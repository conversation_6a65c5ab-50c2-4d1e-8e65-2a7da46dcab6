import { longPollGetLatestAsOfUtcThunk } from './longPollGetLatestAsOfUtcThunk'
import { vi } from 'vitest'
import { apiPaths, createHandler, setupMockServer } from '@/core/testing'
import { setupStore } from '@/core/redux/store'

const server = setupMockServer()

describe('longPollGetLatestAsOfUtc', () => {
  const successResponse = {
    method: 'get',
    path: apiPaths.latestAsOf,
    response: {
      latestAsOfUtc: '2099-09-19'
    },
    status: 200
  } as const

  const store = setupStore()

  beforeEach(() => {
    vi.resetAllMocks()
  })

  it('should be defined', async () => {
    expect(longPollGetLatestAsOfUtcThunk).toBeDefined()
  })

  it('should update currentAsOfUtc', async () => {
    // given
    server.use(createHandler(successResponse))

    // when
    await store.dispatch(
      longPollGetLatestAsOfUtcThunk({
        signal: new AbortController().signal
      })
    )
    const state = store.getState()

    // then
    expect(state.report.currentAsOfUtc).toBe('2099-09-19')
  })
})
