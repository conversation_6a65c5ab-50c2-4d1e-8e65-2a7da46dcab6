import { RootState } from '@/core/redux/store'
import { resetColumnOrder } from '@/core/utils/grid'
import { ColumnApi } from '@ag-grid-community/core'
import { createAsyncThunk } from '@reduxjs/toolkit'

export const resetColumnOrderThunk = createAsyncThunk(
  'report/resetColumnOrderThunk',
  async (columnApi: ColumnApi, { getState }) => {
    const { report } = getState() as RootState
    const defaultColumnDefs = report.columns
    const columnState = columnApi?.getColumnState()
    if (columnState && columnState.length > 0) {
      const state = resetColumnOrder(defaultColumnDefs, columnState)
      columnApi?.applyColumnState({ state, applyOrder: true })
      return state
    }
    return null
  }
)
