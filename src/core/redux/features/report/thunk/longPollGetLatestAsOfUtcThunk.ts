import { RootState } from '@/core/redux/store'
import {
  getLatestAsOfUtc,
  getLatestAsOfUtcErrorRetryDelay,
  getLatestAsOfUtcErrorStatusCode
} from '@/core/services/riskReport'
import { createAsyncThunk } from '@reduxjs/toolkit'
import { logger } from '@/core/utils/logger'
import { AxiosError, isAxiosError } from 'axios'

interface SubscriptionProps {
  signal?: AbortSignal
}

export interface GetLatestAsOfUtcThunkRejectResponse {
  retryAfterSecondsFromHeader?: number
  statusCode?: number
  requestWasCancelled: boolean
}

export const longPollGetLatestAsOfUtcThunk = createAsyncThunk(
  'report/longPollGetLatestAsOfUtc',
  async ({ signal }: SubscriptionProps, { getState, rejectWithValue }) => {
    try {
      const { report } = getState() as RootState

      return await getLatestAsOfUtc({
        reportName: report.reportName,
        currentAsOfUtc: report.currentAsOfUtc,
        signal
      })
    } catch (error: unknown) {
      const retryAfterSecondsFromHeader = getLatestAsOfUtcErrorRetryDelay(error)
      const requestWasCancelled =
        isAxiosError(error) && error.code === AxiosError.ERR_CANCELED
      const response = {
        retryAfterSecondsFromHeader,
        statusCode: getLatestAsOfUtcErrorStatusCode(error),
        requestWasCancelled
      } as GetLatestAsOfUtcThunkRejectResponse

      if (!requestWasCancelled) {
        logger.error(
          'Unable to Subscribe to Latest call failed through longPollGetLatestAsOfUtc method with a retry after delay error. Report Parameters & Error details are as follows: ',
          {
            function: 'getLatestAsOfUtcThunk',
            response,
            state: getState() as RootState
          },
          error as Error
        )
      }

      return rejectWithValue(response)
    }
  }
)
