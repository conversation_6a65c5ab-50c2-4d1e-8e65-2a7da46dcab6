import { epicApi, IEpicUserProfile, IUsersByRole } from '@/core/services/epic'
import { createAsyncThunk } from '@reduxjs/toolkit'

export type InitializeUserData = {
  userProfile: IEpicUserProfile | undefined
  usersByRole: IUsersByRole | undefined
}

export const initializeUserData = createAsyncThunk(
  'user/initializeData',
  async (): Promise<InitializeUserData> => {
    const userProfile = await epicApi.getUserProfile()
    const usersByRole = await epicApi.getUsersByRoles()

    return { userProfile, usersByRole }
  }
)
