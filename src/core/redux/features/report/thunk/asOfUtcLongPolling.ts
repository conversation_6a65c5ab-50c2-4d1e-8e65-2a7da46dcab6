import { RootState } from '@/core/redux/store'
import { AnyAction, ThunkDispatch } from '@reduxjs/toolkit'
import { longPollGetLatestAsOfUtcThunk } from './longPollGetLatestAsOfUtcThunk'

let abortController = new AbortController()

export const POLLING_INTERVAL_SECONDS = 1

export const startAsOfUtcLongPoll = (
  dispatch: ThunkDispatch<RootState, unknown, AnyAction>
) => {
  abortController.abort()
  abortController = new AbortController()
  dispatch(
    longPollGetLatestAsOfUtcThunk({
      signal: abortController.signal
    })
  )

  return abortController
}

export const stopAsOfUtcLongPoll = () => {
  abortController.abort()
}
