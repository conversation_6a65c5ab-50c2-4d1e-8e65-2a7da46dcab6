import {
  PreProcessingParams,
  setReportDetails,
  setReportName,
  setWindowUUID,
  setUserPreProcessingParams,
  currentAsOfUtcSelector,
  preProcessingParamsSelector,
  isLiveModeOrParentLiveMode
} from '@/core/redux/features/report'
import { riskReportSummaryThunk } from '@/core/redux/features/report/thunk/riskReportSummaryThunk'
import { getUserPreferences } from '@/core/redux/features/users/thunk'
import { RootState } from '@/core/redux/store'
import { epicApi } from '@/core/services/epic'
import {
  ILatestAsOfResponse,
  IMetaDataResponse,
  getLatestAsOfUtc
} from '@/core/services/riskReport'
import { getRiskReportApi } from '@/core/services/riskReport/riskReportApi'
import { logger } from '@/core/utils/logger'
import { createAsyncThunk } from '@reduxjs/toolkit'
import { intersection } from 'lodash'
import { setPresets } from '../../presets'
import { setUserData, setUserPreferences } from '../../users'
import { v4 as uuidv4 } from 'uuid'
import {
  getWindowUUID,
  inWorkspace,
  setWorkspaceWindowUUID
} from '@/core/services/glue/epic'
import { getGlueContext } from '@/core/services/glue'

export type InitializeReportData = {
  latestAsOfUtcResponse: ILatestAsOfResponse
  metadata: IMetaDataResponse | null
  tradingAreas: string[]
  selectedTradingAreas: string[]
  reportName: string
}

export const initializeReportDataThunk = createAsyncThunk(
  'report/initializeData',
  async (reportName: string, { dispatch, getState }) => {
    dispatch(setReportName(reportName))

    const state = getState() as RootState
    const currentAsOfUtc = currentAsOfUtcSelector(state)
    const preProcessingParams = preProcessingParamsSelector(state)
    const isLiveModeEnabled = isLiveModeOrParentLiveMode(state)

    // If a drillThroughContext was provided by glue, the currentAsOfUtc and originatingReportLatestAsOfUtc should already be initialized
    // and there's no need to get newer ones. In fact, they shouldn't be updated as they may be timestamps in the past.
    const latestAsOfUtcResponse = currentAsOfUtc
      ? ({
          latestAsOfUtc: currentAsOfUtc,
          originatingReportLatestAsOfUtc:
            preProcessingParams?.originatingContext?.reportAsOfUtc
        } as ILatestAsOfResponse)
      : await getLatestAsOfUtc({
          reportName
        })

    if (!latestAsOfUtcResponse.latestAsOfUtc) {
      logger.error('Latest as of UTC date is empty.')
      return Promise.reject('Latest as of UTC date is empty.')
    }

    const riskReportApi = getRiskReportApi(isLiveModeEnabled)
    const reportDetails = await riskReportApi.getReportDetails(reportName)

    dispatch(setReportDetails(reportDetails))

    const { tradingAreas, selectedTradingAreas } = await initializeTradingAreas(
      {
        riskReportApi,
        reportName,
        asOfUtc: latestAsOfUtcResponse.latestAsOfUtc
      }
    )

    const latestPreProcessingParams =
      latestAsOfUtcResponse.originatingReportLatestAsOfUtc
        ? ({
            ...preProcessingParams,
            originatingContext: {
              ...preProcessingParams?.originatingContext,
              reportAsOfUtc:
                latestAsOfUtcResponse.originatingReportLatestAsOfUtc
            }
          } as PreProcessingParams)
        : preProcessingParams

    const metadataResponse = await riskReportApi.getMetadata(reportName, {
      snapshot: {
        asOfUtc: latestAsOfUtcResponse.latestAsOfUtc
      },
      preProcessingParams: latestPreProcessingParams
    })

    dispatch(
      riskReportSummaryThunk({
        reportName,
        asOfUtc: latestAsOfUtcResponse.latestAsOfUtc,
        selectedTradingAreas,
        preProcessingParams: latestPreProcessingParams
      })
    )

    if (reportDetails?.reportType) {
      const presets =
        (await epicApi.getAllPresets(reportDetails.reportType)) || []
      dispatch(setPresets(presets))
    }

    // windowUUID will be '' when the app is not started in a glue workspace,
    // or the workspace does not yet have a uuid saved into its context.
    // In both cases, the user preferences stored with just the reportName as the key will be used.
    let windowUUID: string = await getWindowUUID()
    const userPreferences = await getUserPreferences(reportName, windowUUID)

    const inWorkSpace = await inWorkspace()
    if (window.glue && inWorkSpace) {
      if (!windowUUID) windowUUID = uuidv4()
      await setWorkspaceWindowUUID(windowUUID)
      dispatch(setWindowUUID(windowUUID))
    }
    dispatch(setUserPreferences(userPreferences))
    dispatch(setUserPreProcessingParams(userPreferences))

    const userProfile = await epicApi.getUserProfile()
    const usersByRole = await epicApi.getUsersByRoles()

    dispatch(setUserData({ userProfile, usersByRole }))

    return {
      selectedTradingAreas,
      latestAsOfUtcResponse,
      tradingAreas,
      metadata: metadataResponse,
      reportName
    } as InitializeReportData
  }
)

async function initializeTradingAreas({
  riskReportApi,
  reportName,
  asOfUtc
}: {
  riskReportApi: ReturnType<typeof getRiskReportApi>
  asOfUtc: string
  reportName: string
}) {
  const tradingAreasResponse = await riskReportApi.getTradingAreas(reportName, {
    snapshot: {
      asOfUtc
    },
    tradingAreas: ['*'],
    columns: ['TradingArea'],
    filters: []
  })

  const responseTradingAreas =
    tradingAreasResponse?.distinctColumnValues.TradingArea ?? []

  const drillThroughSelectedTradingAreas =
    getGlueContext()?.drillThroughContext?.selectedTradingAreas

  const selectedTradingAreasResult = drillThroughSelectedTradingAreas
    ? intersection(responseTradingAreas, drillThroughSelectedTradingAreas)
    : responseTradingAreas

  return {
    tradingAreas: responseTradingAreas.sort(),
    selectedTradingAreas: selectedTradingAreasResult.sort()
  }
}
