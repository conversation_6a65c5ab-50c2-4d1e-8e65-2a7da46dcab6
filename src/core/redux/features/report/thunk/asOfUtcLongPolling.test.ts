import { vi } from 'vitest'
import { startAsOfUtcLongPoll, stopAsOfUtcLongPoll } from './asOfUtcLongPolling'

describe('asOfUTCSubscription module', () => {
  it('should dispatch longPollGetLatestAsOfUtc', async () => {
    // given
    const dispatchMock = vi.fn()

    // when
    startAsOfUtcLongPoll(dispatchMock)

    expect(dispatchMock).toHaveBeenCalledTimes(1)
  })

  it('should abort previous subscription', async () => {
    // given
    const dispatchMock = vi.fn()
    const abortController = startAsOfUtcLongPoll(dispatchMock)

    const abortSpy = vi.spyOn(abortController, 'abort')

    // when
    stopAsOfUtcLongPoll()

    expect(abortSpy).toHaveBeenCalledTimes(1)
  })
})
