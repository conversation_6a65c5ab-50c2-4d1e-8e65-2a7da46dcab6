import { initializeReportDataThunk } from '@/core/redux/features/report/thunk/initializeReportDataThunk'
import { setupStore } from '@/core/redux/store'
import { saveMyApplicationWindowState } from '@epic/window'
import { AgGridState, IUserPreferenceKey } from '@/core/services/epic'
import { IGetReportDetailsResponse } from '@/core/services/riskReport'
import {
  allHandlers,
  apiPaths,
  createHandler,
  setupMockServer
} from '@/core/testing'
import { getTestDate } from '@/core/testing/date'
import {
  formatDateToLocal,
  formatDateToUtc,
  formatTimestampToLocalWithMilliseconds,
  formatTimestampToUTCWithMilliseconds
} from '@/core/utils/date'
import * as glue from '@/core/services/glue/glue'
import { Glue42 } from '@glue42/desktop'
import { waitFor } from '@testing-library/react'
import { rest } from 'msw'
import { describe, expect, it, vi } from 'vitest'

const testReportName = 'testReportName'
const referenceDate = new Date().toISOString()

const reportDetails: IGetReportDetailsResponse = {
  columnInfo: [],
  preProcessingParamSpecs: [],
  reportType: 'reportType',
  publishSyncData: true
}

const userPreferences = {
  Key: 'PnlLive',
  Data: {
    [IUserPreferenceKey.LastUsedPresetId]: '123',
    [IUserPreferenceKey.ComponentVisibilitySidePanel]: {
      isAppHeaderVisible: true,
      isFloatingFiltersBarVisible: true,
      isFooterBarVisible: true,
      isGroupingBarVisible: true,
      isSummaryBarVisible: true
    }
  }
}

const tradingAreas = {
  distinctColumnValues: {
    TradingArea: ['ARNO', 'BALT', 'BHIR']
  }
}

const summary = {
  headlineValues: {
    Daily: -6402664.470662436,
    Monthly: 13739133.525537847,
    Yearly: 246853132.7275392,
    'Fiscal YTD': 521564694.6381372,
    DV01: -1762450.326350303,
    PV01: 1762450.326350303,
    BasisRisk: -1318359.510916529,
    FxDeltaUSD: 1592022147.8183224,
    FxVegaUSD: 15989477.293620594,
    IrVegaUSD: 1862262.0492124346,
    'Eq Delta': -160796720.73840767,
    'Eq Gamma': 92003242.39270225,
    'Eq Vega': 1422556.9574725975,
    'Ir Delta': -1937791.2617549403,
    'Ir Delta Par': -1958527.29805378
  }
}

const userProfile = {
  UserId: '123456',
  UserFullName: 'John Doe',
  UserFirstName: 'John',
  UserLastName: 'Doe',
  UserPreferredName: 'John',
  UserInitials: null,
  UserEmail: '<EMAIL>',
  TeamId: 'DEFAULT',
  OriginalTeamId: 'DEFAULT',
  ImpersonatingAs: '',
  LayoutWriteOnMimicEnabled: null,
  EffectiveFrom: '2021-10-26T22:33:42Z',
  EffectiveTo: '2079-06-06T04:00:00Z',
  OnBoardingDate: '2022-11-18T15:42:23.766Z',
  CreatedById: 'JP11749',
  ModifiedDate: '2023-07-13T12:29:11.238Z',
  ModifiedById: '123456',
  TimeZoneId: null,
  CountryCode: null,
  EmployeeLocation: null,
  Region: null,
  JobTitle: null,
  EmployeeId: '10986',
  IsServiceAccount: false,
  IsPm: false,
  IsAnalyst: false,
  IsDataAnalyst: false,
  IsAdmin: false,
  IsManagement: false,
  IsExecutionTrader: false,
  IsPreferredMap: false,
  IsTMTProfessional: false,
  IsSE: false,
  IsXO: false,
  IsDeveloper: true,
  LastHireDate: '2021-10-25T04:00:00Z',
  CompanyAddressCity: null,
  CompanyAddressState: null,
  CompanyAddressCountry: null,
  WorkLocation: null,
  ManagerName: null,
  DepartmentName: null,
  Id: '11122231123312132123',
  CreatedDate: '2022-05-20T10:23:02.646Z',
  IsDeleted: false
}

const usersByRoles = {
  'Preset Admin': [
    {
      EmployeeFirstName: 'John',
      EmployeeLastName: 'Doe',
      ADUserID: '123456'
    },
    {
      EmployeeFirstName: 'Sarah',
      EmployeeLastName: 'Connor',
      ADUserID: '654321'
    }
  ]
}

const testDate1 = getTestDate(1)
const testDate2 = getTestDate(2)
const testDate3 = getTestDate(3)

const metaData = {
  metadata: {
    'Execution Time': testDate1,
    'Market Data': testDate2,
    Trades: testDate3
  }
}

const presets: unknown[] = [
  {
    ServiceId: 'macro',
    AppName: 'MacroPortal',
    OriginalSharedPresetId: null,
    OwnerId: '123456',
    SharedBy: null,
    GridName: 'PnlLive',
    PresetName: 'test filering',
    SharedWith: null,
    Data: {
      groupId: 'personal',
      name: 'test filering',
      id: '65e872d638560512c49fc912',
      state: {} as AgGridState
    },
    ModifiedDate: '2024-03-06T13:43:49.813Z',
    ModifiedBy: 'AB10986',
    CreatedBy: null,
    Id: '65e872d638560512c49fc912',
    CreatedDate: '2024-03-06T13:42:46.998Z',
    IsDeleted: false
  },
  {
    ServiceId: 'macro',
    AppName: 'MacroPortal',
    OriginalSharedPresetId: '65966e17cd471cba8434130d',
    OwnerId: '123456',
    SharedBy: 'JohnD',
    GridName: 'PnlLive',
    PresetName: 'Test (4)',
    SharedWith: [],
    Data: {
      groupId: 'personal',
      name: 'Test (4)',
      id: '65966e17cd471cba8434130d',
      state: {} as AgGridState
    },
    ModifiedDate: '2024-03-05T15:10:57.37Z',
    ModifiedBy: null,
    CreatedBy: 'JohnD',
    Id: '65e736019f674357e50b5788',
    CreatedDate: '2024-03-05T15:10:57.37Z',
    IsDeleted: false
  }
]

describe.skip('initializeReportDataThunk positive', () => {
  window.debugMode = true
  const store = setupStore()
  const server = setupMockServer(...allHandlers)

  server.use(
    createHandler({
      method: 'get',
      path: apiPaths.latestAsOf,
      response: {
        latestAsOfUtc: referenceDate
      },
      status: 200
    })
  )

  server.use(
    createHandler({
      method: 'get',
      path: apiPaths.getReportDetails,
      response: reportDetails
    })
  )

  server.use(
    createHandler({
      method: 'get',
      path: apiPaths.getUserPreference,
      response: userPreferences,
      status: 200
    })
  )

  server.use(
    createHandler({
      method: 'post',
      path: apiPaths.getDistinctColumnValues,
      response: tradingAreas,
      status: 200
    })
  )

  server.use(
    createHandler({
      method: 'get',
      path: apiPaths.getSummary,
      response: summary,
      status: 200
    })
  )

  server.use(
    createHandler({
      method: 'get',
      path: apiPaths.getAllPresets,
      response: presets,
      status: 200
    })
  )

  server.use(
    createHandler({
      method: 'get',
      path: apiPaths.getUserProfile,
      response: userProfile,
      status: 200
    })
  )

  server.use(
    createHandler({
      method: 'post',
      path: apiPaths.getUsersByRoles,
      response: usersByRoles,
      status: 200
    })
  )

  server.use(
    createHandler({
      method: 'post',
      path: apiPaths.getMetadata,
      response: metaData,
      status: 200
    })
  )

  beforeEach(() => vi.resetAllMocks())

  it('completes successfully with valid data', async () => {
    store.dispatch(initializeReportDataThunk(testReportName))

    await waitFor(() => {
      const reportState = store.getState().report
      const reportMetadataState = store.getState().reportMetadata
      const usersState = store.getState().users
      const presetsState = store.getState().presets

      expect(reportState.currentAsOfUtc).toBe(referenceDate)
      expect(reportState.reportName).toBe(testReportName)

      expect(reportState.selectedTradingAreas).toStrictEqual(
        tradingAreas.distinctColumnValues.TradingArea
      )
      expect(reportState.tradingAreas).toStrictEqual(
        tradingAreas.distinctColumnValues.TradingArea
      )

      expect(usersState.usersByRoles).toStrictEqual(usersByRoles)
      expect(usersState.profile).toStrictEqual(userProfile)
      expect(usersState.userPreferences).toStrictEqual(userPreferences.Data)

      expect(presetsState.userPresets).toStrictEqual(presets)

      expect(reportState.isInitialized).toBeTruthy()

      expect(reportMetadataState.metadata).toStrictEqual({
        'Execution Time': {
          localDate: formatDateToLocal(testDate1),
          localDatePrecise: formatTimestampToLocalWithMilliseconds(
            testDate1.toISOString()
          ),
          utcString: formatDateToUtc(testDate1),
          utcStringPrecise: formatTimestampToUTCWithMilliseconds(
            testDate1.toISOString()
          ),
          rawValue: testDate1.toISOString()
        },
        'Market Data': {
          localDate: formatDateToLocal(testDate2),
          localDatePrecise: formatTimestampToLocalWithMilliseconds(
            testDate2.toISOString()
          ),
          utcString: formatDateToUtc(testDate2),
          utcStringPrecise: formatTimestampToUTCWithMilliseconds(
            testDate2.toISOString()
          ),
          rawValue: testDate2.toISOString()
        },
        Trades: {
          localDate: formatDateToLocal(testDate3),
          localDatePrecise: formatTimestampToLocalWithMilliseconds(
            testDate3.toISOString()
          ),
          utcString: formatDateToUtc(testDate3),
          utcStringPrecise: formatTimestampToUTCWithMilliseconds(
            testDate3.toISOString()
          ),
          rawValue: testDate3.toISOString()
        }
      })
    })
  })

  it('test getWindowUUID with Window Context of Unique Id', async () => {
    window.glue = {} as Glue42.Glue

    vi.mocked(saveMyApplicationWindowState)

    vi.spyOn(glue, 'inWorkspace').mockReturnValue(Promise.resolve(true))

    vi.spyOn(glue, 'getWindowUUID').mockReturnValue(
      Promise.resolve('application-unique-id')
    )

    store.dispatch(initializeReportDataThunk(testReportName))

    await waitFor(() => {
      const reportStore = store.getState().report

      expect(reportStore.windowUUID).toStrictEqual('application-unique-id')
    })
  })

  it('test tradingAreas intersection when DT', async () => {
    const DTSelectedTradingAreas = [
      tradingAreas.distinctColumnValues.TradingArea.slice().at(0)
    ]

    vi.spyOn(glue, 'getGlueContext').mockReturnValue({
      drillThroughContext: {
        selectedTradingAreas: DTSelectedTradingAreas as string[],
        originatingContext: {
          reportName: testReportName,
          groupKey: {},
          filters: [],
          reportAsOfUtc: ''
        },
        currentAsOf: null,
        isLiveModeEnabled: false,
        drillThroughViewLiveUpdateAsOfUtcMetadataOrigin: ''
      }
    })

    store.dispatch(initializeReportDataThunk(testReportName))

    await waitFor(() => {
      const reportStore = store.getState().report

      expect(reportStore.selectedTradingAreas).toStrictEqual(
        DTSelectedTradingAreas
      )
    })
  })
})

describe('initializeReportDataThunk negative', () => {
  const store = setupStore()
  const server = setupMockServer(...allHandlers)

  it('test rejection when asOf request is failing', async () => {
    server.use(
      rest.get(apiPaths.latestAsOf, (_, res, ctx) => {
        return res(ctx.status(500))
      })
    )

    store.dispatch(initializeReportDataThunk(testReportName))
    await waitFor(() => {
      const reportStore = store.getState().report
      expect(reportStore.currentAsOfUtc).toBeFalsy()
    })
  })

  it('test rejection when asOf returned as undefined/null/empty string', async () => {
    server.use(
      createHandler({
        method: 'get',
        path: apiPaths.latestAsOf,
        response: {
          latestAsOfUtc: ''
        },
        status: 200
      })
    )

    store.dispatch(initializeReportDataThunk(testReportName))
    await waitFor(() => {
      const reportStore = store.getState().report
      expect(reportStore.isInitialized).toBeTruthy()
      expect(reportStore.currentAsOfUtc).toBeFalsy()
    })
  })
})
