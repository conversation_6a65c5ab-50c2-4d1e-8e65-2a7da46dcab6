import { RootState } from '@/core/redux/store'
import { getRiskReportApi } from '@/core/services/riskReport/riskReportApi'
import { createAsyncThunk } from '@reduxjs/toolkit'
import { isLiveModeOrParentLiveMode } from '../selectors'

export const reportDetailsThunk = createAsyncThunk(
  'report/initializeReportDetails',
  (reportName: string, { getState }) => {
    const state = getState() as RootState
    const isLiveModeEnabled = isLiveModeOrParentLiveMode(state)
    const riskReportApi = getRiskReportApi(isLiveModeEnabled)

    return riskReportApi.getReportDetails(reportName)
  }
)
