import { ISummaryRequest } from '@/core/services/riskReport'
import { getRiskReportApi } from '@/core/services/riskReport/riskReportApi'
import { createAsyncThunk } from '@reduxjs/toolkit'
import { PreProcessingParams } from '../types'
import { RootState } from '@/core/redux/store'
import {
  getSnapshot,
  isLiveModeOrParentLiveMode,
  reportPathSelector
} from '../selectors'

export const riskReportSummaryThunk = createAsyncThunk(
  'report/initializeSummary',
  async (
    {
      reportName,
      asOfUtc,
      selectedTradingAreas,
      preProcessingParams
    }: {
      reportName?: string
      asOfUtc?: string
      selectedTradingAreas: string[]
      preProcessingParams: PreProcessingParams | undefined
    },
    { getState }
  ) => {
    const state: RootState = getState() as RootState
    const reportPath = reportName ?? reportPathSelector(state)
    const isLiveModeEnabled = isLiveModeOrParentLiveMode(state)
    const riskReportApi = getRiskReportApi(isLiveModeEnabled)
    const snapshot = getSnapshot(state)
    const payload = {
      snapshot,
      tradingAreas: selectedTradingAreas,
      preProcessingParams
    } as ISummaryRequest

    if (asOfUtc) {
      // initializeReportDataThunk.ts sets optional asOfUtc
      payload.snapshot.asOfUtc = asOfUtc
    }

    return await riskReportApi.getSummary(reportPath, payload)
  }
)
