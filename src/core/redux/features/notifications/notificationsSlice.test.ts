import { uiSlice, NotificationsState } from './notificationsSlice'
import { IToast } from '@core-clib/web-components/types'

const { addNotification, removeNotification } = uiSlice.actions

describe('notificationsSlice', () => {
  let initialState: NotificationsState

  beforeEach(() => {
    initialState = {
      toasts: []
    }
  })

  test('addNotification', () => {
    const notification: IToast = {
      id: '1',
      intent: 'default',
      text: 'Test Message'
    }
    const expectedState: NotificationsState = {
      ...initialState,
      toasts: [notification]
    }
    expect(
      uiSlice.reducer(initialState, addNotification(notification))
    ).toEqual(expectedState)
  })

  test('removeNotification', () => {
    const notification: IToast = {
      id: '1',
      intent: 'danger',
      text: 'Test Message'
    }
    const initialStateWithNotification: NotificationsState = {
      ...initialState,
      toasts: [notification]
    }
    const expectedState: NotificationsState = {
      ...initialState,
      toasts: []
    }
    expect(
      uiSlice.reducer(
        initialStateWithNotification,
        removeNotification(notification.id)
      )
    ).toEqual(expectedState)
  })
})
