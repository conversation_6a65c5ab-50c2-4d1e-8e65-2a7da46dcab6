import { PayloadAction, createSlice } from '@reduxjs/toolkit'
import { IToast } from '@core-clib/web-components/types'

type SystemError = {
  title: string
}
interface IErrorToast extends Omit<IToast, 'text'> {
  text: string | SystemError
}
export interface NotificationsState {
  toasts: IToast[]
  errorMessage: string | null
  pendingLoaders: string[]
  isLoading?: boolean
}

export const initialNotificationsState: NotificationsState = {
  toasts: [],
  pendingLoaders: [],
  // isLoading is true when there are pending loaders
  // this makes loading indicator in UI smooth (wont jump on/off in case of multiple loaders)
  isLoading: false,
  errorMessage: null
}

export const uiSlice = createSlice({
  name: 'ui',
  initialState: initialNotificationsState,
  reducers: {
    addNotification: (state, action: PayloadAction<IErrorToast>) => {
      const toastMessage =
        typeof action.payload.text === 'object'
          ? action.payload.text.title
          : action.payload.text
      if (!state.toasts.find((toast) => toast.text === toastMessage)) {
        state.toasts.push({
          ...action.payload,
          text: toastMessage
        })
      }
      // state.toasts = [...state.toasts, action.payload]
    },
    removeNotification: (state, action: PayloadAction<string | number>) => {
      state.toasts = state.toasts.filter(
        (notification) => notification.id !== action.payload
      )
    },
    addLoader: (state, { payload }: PayloadAction<string>) => {
      state.pendingLoaders.push(payload)
      state.isLoading = Boolean(state.pendingLoaders.length)
    },

    removeLoader: (state, { payload }: PayloadAction<string>) => {
      state.pendingLoaders = state.pendingLoaders.filter(
        (loaderId) => loaderId !== payload
      )

      state.isLoading = Boolean(state.pendingLoaders.length)
    },
    showErrorModal: (state, { payload }: PayloadAction<string>) => {
      state.errorMessage = payload
    },
    closeModal: (state) => {
      state.errorMessage = null
    }
  }
})

export const {
  addNotification,
  removeNotification,
  addLoader,
  removeLoader,
  closeModal,
  showErrorModal
} = uiSlice.actions
export const notificationsReducer = uiSlice.reducer
