import { IPreset } from '@/core/services/epic'
import {
  isPresetAvailable,
  isSharedPreset,
  isUserPreset
} from '@/core/utils/presets'
import { PayloadAction, createSlice } from '@reduxjs/toolkit'

export interface PresetsState {
  defaultPresetId?: string
  userPresets: IPreset[]
  sharedPresets: IPreset[]
  isReady: boolean
}

export const initialPresetsState: PresetsState = {
  defaultPresetId: undefined,
  userPresets: [],
  sharedPresets: [],
  isReady: false
}

const setAllPresetsReducer = (
  state: PresetsState,
  { payload }: PayloadAction<IPreset[]>
) => {
  const availablePresets = payload.filter(isPresetAvailable)
  state.userPresets = availablePresets.filter(isUserPreset)
  state.sharedPresets = availablePresets.filter(isSharedPreset)
}

export const presetsSlice = createSlice({
  name: 'grid',
  initialState: initialPresetsState,
  reducers: {
    // @ts-expect-error - for some reasons it doesn't recognize the type of pivotResultsColumns?: (ColDef | ColGroupDef)[] in Preset
    setPresets: setAllPresetsReducer,
    setPresetsReady: (state) => {
      state.isReady = true
    },
    addPreset: (state, { payload }: PayloadAction<IPreset>) => {
      isSharedPreset(payload)
        ? state.sharedPresets.push(payload)
        : state.userPresets.push(payload)
    },
    removePreset: (state, { payload }: PayloadAction<string>) => {
      const presetId = payload
      state.sharedPresets = state.sharedPresets.filter(
        (preset) => preset.Id !== presetId
      )
      state.userPresets = state.userPresets.filter(
        (preset) => preset.Id !== presetId
      )
    },
    updatePreset: (state, { payload }: PayloadAction<IPreset>) => {
      const presetId = payload.Id
      state.sharedPresets = state.sharedPresets.map((preset) =>
        preset.Id !== presetId ? preset : payload
      )

      state.userPresets = state.userPresets.map((preset) =>
        preset.Id !== presetId ? preset : payload
      )
    }
  }
})

export const {
  setPresets,
  setPresetsReady,
  addPreset,
  removePreset,
  updatePreset
} = presetsSlice.actions

export const presetsReducer = presetsSlice.reducer
