import { RootState } from '@/core/redux/store'
import { ColumnApi } from '@ag-grid-community/core'
import { createAsyncThunk } from '@reduxjs/toolkit'
import { lastUsedPresetSelector } from '@/core/redux/features/users'
import { IPresetToSave } from '@/core/services/epic'

/**
 * Apply pinned columns from preset
 */
export const applyPinnedColumnsThunk = createAsyncThunk(
  'report/applyPinnedColumnsThunk',
  async (columnApi: ColumnApi, { getState }) => {
    const state = getState() as RootState
    const preset = lastUsedPresetSelector(state)
    // for some reason some presets have PresetData key (should be Data)
    const columnState =
      preset?.Data?.state?.columnState ??
      (preset as unknown as IPresetToSave).PresetData.state.columnState

    if (columnState) {
      const pinnedColumns = columnState.filter((col) => Boolean(col.pinned))

      pinnedColumns?.forEach((col) => {
        columnApi.setColumnPinned(col.colId, col.pinned)
      })
    }
  }
)
