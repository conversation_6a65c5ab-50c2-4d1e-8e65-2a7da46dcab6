import {
  setPresets,
  addPreset,
  removePreset,
  presetsSlice
} from '@/core/redux/features/presets/presetsSlice'
import { IPresetShareAccessType, IPresetShareType } from '@/core/services/epic'
import { getPresetDummy, getPresetShareDummy } from '@/core/testing/dummies'

describe('presetsSlice', () => {
  const getSharedPresetMock = () =>
    getPresetDummy({
      SharedWith: [
        getPresetShareDummy({
          AccessType: IPresetShareAccessType.Read,
          Type: IPresetShareType.Public,
          Names: []
        })
      ]
    })

  const getUserPresetMock = () =>
    getPresetDummy({
      SharedWith: null
    })

  it('should set presets correctly with setPresets action', () => {
    const state = presetsSlice.reducer(
      undefined,
      setPresets([getUserPresetMock(), getSharedPresetMock()])
    )

    expect(state.userPresets).toHaveLength(1)
    expect(state.sharedPresets).toHaveLength(1)
  })

  it('should add a user preset with addPreset action', () => {
    const presetMock = getUserPresetMock()
    const state = presetsSlice.reducer(undefined, addPreset(presetMock))

    expect(state.userPresets[0].Id).toBe(presetMock.Id)
    expect(state.userPresets[0].PresetName).toBe(presetMock.PresetName)
  })

  it('should add a shared preset with addPreset action', () => {
    const presetMock = getSharedPresetMock()
    const state = presetsSlice.reducer(undefined, addPreset(presetMock))

    expect(state.sharedPresets[0].Id).toBe(presetMock.Id)
    expect(state.sharedPresets[0].PresetName).toBe(presetMock.PresetName)
  })

  it('should remove a user preset with removePreset action', () => {
    const presetMock = getUserPresetMock()
    const state = presetsSlice.reducer(
      { userPresets: [presetMock], sharedPresets: [] } as any,
      removePreset(presetMock.Id)
    )

    expect(state.userPresets).toHaveLength(0)
  })

  it('should remove a shared preset with removePreset action', () => {
    const presetMock = getSharedPresetMock()
    const state = presetsSlice.reducer(
      { userPresets: [], sharedPresets: [presetMock] } as any,
      removePreset(presetMock.Id)
    )

    expect(state.sharedPresets).toHaveLength(0)
  })
})
