import { useAppDispatch, useAppSelect } from '@/core/redux/hooks'
import { epicApi } from '@/core/services/epic'
import { getUserPreferencesKey } from '@/core/utils/preferences'
import { useEffect } from 'react'
import { setUserPreferencesChanged } from '..'

export const useUserPreferencesEffect = () => {
  const dispatch = useAppDispatch()
  const reportName = useAppSelect((state) => state.report.reportName)
  const windowUUID = useAppSelect((state) => state.report.windowUUID)
  const userPreferences = useAppSelect((state) => state.users.userPreferences)
  const userPreferencesChanged = useAppSelect(
    (state) => state.users.userPreferencesChanged
  )

  useEffect(() => {
    if (!userPreferencesChanged) return

    // TODO in MACROUIUX-1418
    // remove deprecated defaultPreset
    // const { defaultPreset, ...preferences } = userPreferences

    epicApi.saveUserPreference({
      Key: getUserPreferencesKey(reportName, windowUUID),
      Data: userPreferences
    })

    dispatch(setUserPreferencesChanged(false))
  }, [userPreferences, userPreferencesChanged, reportName, windowUUID])
}
