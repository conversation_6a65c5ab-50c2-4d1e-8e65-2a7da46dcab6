import { AppStartListening } from '@/core/redux/middleware/listenerMiddleware'
import { componentVisibilitySelector } from '../selectors/componentVisibilitySelector'
import { saveVisibilityPreferencesThunk } from '../thunk/saveVisibilityPreferences'

export const addSaveVisibilityPreferencesListener = (
  startListening: AppStartListening
) => {
  startListening({
    predicate: (_, currentState, previousState) => {
      const currentPreferences = componentVisibilitySelector(currentState)
      const currentPreferencesChanged =
        currentPreferences !== componentVisibilitySelector(previousState)

      return Boolean(currentPreferences) && currentPreferencesChanged
    },
    effect: async (_, { dispatch }) => {
      dispatch(saveVisibilityPreferencesThunk())
    }
  })
}
