import { PreProcessingReportParams } from '@/core/redux/features/report'
import { InitializeUserData } from '@/core/redux/features/report/thunk/initializeUserData'
import {
  IEpicUserProfile,
  IUserPreferences,
  IUserRole,
  IUsersByRole
} from '@/core/services/epic'
import { logger } from '@/core/utils/logger'
import { PayloadAction, createSlice } from '@reduxjs/toolkit'
import { IUserProfile } from './types'

export interface UserState {
  profile: IUserProfile | null
  usersByRoles: IUsersByRole
  userPreferences: IUserPreferences
  userPreferencesChanged: boolean
  theme: string
}

export const initialUsersState: UserState = {
  profile: null,
  usersByRoles: {
    [IUserRole.PresetAdmin]: [],
    [IUserRole.PreviewAccess]: [],
    [IUserRole.PreviewAccessDelayed]: [],
    [IUserRole.PreviewAccessBBGOverlay]: [],
    [IUserRole.PreviewAccessBBGOverlayDelayed]: []
  },
  userPreferences: {
    componentVisibility: {
      isAppHeaderVisible: true,
      isSummaryBarVisible: true,
      isFooterBarVisible: true,
      isGroupingBarVisible: true,
      isFloatingFiltersBarVisible: true
    }
  },
  userPreferencesChanged: false,
  theme: 'core-dark-theme'
}

const setUserProfileReducer = (
  state: UserState,
  { payload }: PayloadAction<IEpicUserProfile>
) => {
  state.profile = {
    UserId: payload.UserId,
    UserFullName: payload.UserFullName,
    UserFirstName: payload.UserFirstName,
    UserLastName: payload.UserLastName,
    UserEmail: payload.UserEmail,
    ImpersonatingAs: payload.ImpersonatingAs || null
  }
}

const setUsersByRolesReducer = (
  state: UserState,
  { payload }: PayloadAction<IUsersByRole>
) => {
  state.usersByRoles = payload
}

const setUserApplicationThemeReducer = (
  state: UserState,
  { payload }: PayloadAction<string>
) => {
  state.theme = payload
}

const setUserPreferencesReducer = (
  state: UserState,
  { payload }: PayloadAction<IUserPreferences | null>
) => {
  if (payload === null) return

  state.userPreferences = {
    ...state.userPreferences,
    ...payload
  }
}

const setLastUsedPresetIdReducer = (
  state: UserState,
  { payload }: PayloadAction<string>
) => {
  state.userPreferences.lastUsedPresetId = payload
}

const setPreProcessingReportParamsReducer = (
  state: UserState,
  { payload }: PayloadAction<PreProcessingReportParams>
) => {
  state.userPreferences.preProcessingReportParams = {
    ...state.userPreferences.preProcessingReportParams,
    ...payload
  }
}

const setIsSummaryBarCollapsedReducer = (
  state: UserState,
  { payload }: PayloadAction<boolean>
) => {
  state.userPreferences.isSummaryBarCollapsed = payload
  state.userPreferencesChanged = true
}

const setUserDataReducer = (
  state: UserState,
  { payload: { userProfile, usersByRole } }: PayloadAction<InitializeUserData>
) => {
  if (usersByRole) {
    state.usersByRoles = usersByRole
  }

  if (userProfile) {
    state.profile = userProfile
  } else {
    logger.warn('User profile is empty')
  }
}

const toggleAppHeaderReducer = (state: UserState) => {
  state.userPreferences.componentVisibility.isAppHeaderVisible =
    !state.userPreferences.componentVisibility.isAppHeaderVisible
}

const toggleSummaryBarReducer = (state: UserState) => {
  if (state.userPreferences.componentVisibility) {
    state.userPreferences.componentVisibility.isSummaryBarVisible =
      !state.userPreferences.componentVisibility.isSummaryBarVisible
  }
}
const toggleFooterBarReducer = (state: UserState) => {
  state.userPreferences.componentVisibility.isFooterBarVisible =
    !state.userPreferences.componentVisibility.isFooterBarVisible
}
const toggleGroupingBarReducer = (state: UserState) => {
  state.userPreferences.componentVisibility.isGroupingBarVisible =
    !state.userPreferences.componentVisibility.isGroupingBarVisible
}

const toggleFloatingBarReducer = (state: UserState) => {
  state.userPreferences.componentVisibility.isFloatingFiltersBarVisible =
    !state.userPreferences.componentVisibility.isFloatingFiltersBarVisible
}

export const userSlice = createSlice({
  name: 'users',
  initialState: initialUsersState,
  reducers: {
    setLastUsedPresetId: setLastUsedPresetIdReducer,
    setPreProcessingReportParams: setPreProcessingReportParamsReducer,
    setIsSummaryBarCollapsed: setIsSummaryBarCollapsedReducer,
    setUserApplicationTheme: setUserApplicationThemeReducer,
    setUserPreferencesChanged: (state, { payload }: PayloadAction<boolean>) => {
      state.userPreferencesChanged = payload
    },
    setUserProfile: setUserProfileReducer,
    setUsersByRole: setUsersByRolesReducer,
    setUserPreferences: setUserPreferencesReducer,
    setUserData: setUserDataReducer,
    toggleAppHeader: toggleAppHeaderReducer,
    toggleSummaryBar: toggleSummaryBarReducer,
    toggleFooterBar: toggleFooterBarReducer,
    toggleGroupingBar: toggleGroupingBarReducer,
    toggleFloatingBar: toggleFloatingBarReducer
  }
})

export const usersReducer = userSlice.reducer
export const {
  setLastUsedPresetId,
  setPreProcessingReportParams,
  setUserPreferencesChanged,
  setIsSummaryBarCollapsed,
  setUserApplicationTheme,
  setUserProfile,
  setUsersByRole,
  setUserPreferences,
  setUserData,
  toggleAppHeader,
  toggleSummaryBar,
  toggleFooterBar,
  toggleGroupingBar,
  toggleFloatingBar
} = userSlice.actions
