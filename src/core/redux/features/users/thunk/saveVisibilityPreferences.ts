import { RootState } from '@/core/redux/store'
import { epicApi } from '@/core/services/epic'
import { logger } from '@/core/utils/logger'
import { createAsyncThunk } from '@reduxjs/toolkit'
import { getUserPreferencesKey } from '@/core/utils/preferences'
import { reportNameSelector, windowUUIDSelector } from '../../report'
import { userPreferencesSelector } from '../selectors/userPreferencesSelector'

export const saveVisibilityPreferencesThunk = createAsyncThunk(
  'users/visibilityOptions',
  async (_, { getState }) => {
    try {
      const state = getState() as RootState
      const reportName = reportNameSelector(state)
      const windowUUID = windowUUIDSelector(state)
      const userPreferences = userPreferencesSelector(state)
      await epicApi.saveUserPreference({
        Key: getUserPreferencesKey(reportName, windowUUID),
        Data: userPreferences
      })
    } catch (error: unknown) {
      logger.error(
        'Unable to Save User Preferences through saveVisibilityPreferencesThunk method.',
        {
          function: 'saveVisibilityPreferencesThunk'
        },
        error as Error
      )
      return {}
    }
  }
)
