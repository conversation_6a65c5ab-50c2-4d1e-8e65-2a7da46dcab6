import {
  apiP<PERSON>s,
  createH<PERSON><PERSON>,
  handlers,
  setupMockServer
} from '@/core/testing'
import { logger } from '@/core/utils/logger'
import { waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { getUserPreferences } from './getUserPreferences'

describe('getUserPreferences', () => {
  window.debugMode = true
  const testReportName = 'PnlLive'
  const server = setupMockServer(handlers.getUserPreference)

  it('should log error and return {} if server error', async () => {
    server.use(
      createHandler({
        method: 'get',
        path: apiPaths.getUserPreference,
        response: {
          message: 'Server error!'
        },
        status: 500
      })
    )

    const logErrorSpy = vi.fn()
    vi.spyOn(logger, 'error').mockImplementation(logErrorSpy)

    const result = await getUserPreferences(testReportName)

    await waitFor(() => {
      expect(logErrorSpy).toHaveBeenCalledWith(
        'Unable to Get User Preferences through getUserPreferences method. Report Details with returned error information is as follows: ',
        {
          function: 'getUserPreferences'
        },
        expect.any(Object)
      )

      expect(result).toEqual(null)
    })
  })
})
