import { epicApi, isEpicUserPreference } from '@/core/services/epic'
import { logger } from '@/core/utils/logger'
import { getUserPreferencesKey } from '@/core/utils/preferences'
import { getLastUsedPresetId } from './getLastUsedPresetId'

export const getUserPreferences = async (
  reportName: string,
  windowUUID = ''
) => {
  try {
    logger.debug('Getting User Preferences.', {
      function: 'getUserPreferences',
      windowUUID,
      reportName
    })

    // Attempt to query user preferences with given windowUUID, if any
    let { data } = await epicApi.getUserPreference(
      getUserPreferencesKey(reportName, windowUUID)
    )

    // If no sensible response can be found, attempt to query the user preferences with the old, window-agnostic, key.
    // This ensures backward-compatible behavior when a user first opens a workspace before saving it with the newly generated UUID.
    if (!data || !isEpicUserPreference(data)) {
      logger.warn(
        'Unable to load user preferences for given reportName and windowUUID, falling back to user preferences stored without windowUUID in their key',
        {
          reportName,
          windowUUID
        }
      )
      data = (
        await epicApi.getUserPreference(getUserPreferencesKey(reportName, ''))
      ).data
    }

    if (!data || !isEpicUserPreference(data)) {
      logger.warn(
        'Unable to load user preferences for given reportName and windowUUID, or by falling back to user preferences stored without windowUUID in their key. Using default preset instead.',
        {
          reportName,
          windowUUID
        }
      )
      return null
    }

    return {
      ...data.Data,
      lastUsedPresetId: await getLastUsedPresetId(data.Data)
    }
  } catch (error: unknown) {
    logger.error(
      'Unable to Get User Preferences through getUserPreferences method. Report Details with returned error information is as follows: ',
      {
        function: 'getUserPreferences'
      },
      error as Error
    )
    return null
  }
}
