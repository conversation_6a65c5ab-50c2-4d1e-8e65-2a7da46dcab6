import {
  IUserPreferenceKey,
  IUserPreferences,
  epicApi,
  isEpicUserPreference
} from '@/core/services/epic'
import { logger } from '@/core/utils/logger'

export const getLastUsedPresetId = async (data: IUserPreferences) => {
  let lastUsedPresetId = data.lastUsedPresetId

  // if present; log "LastUsedPreset <PresetName> loaded" to console
  if (lastUsedPresetId) {
    logger.debug(
      `LastUsedPreset ID ${lastUsedPresetId} found in user preferences API`
    )
  } else {
    // Otherwise, attempt to load user preference key "defaultPreset" and use "Data.defaultPreset"
    // this fallback should be removed when all users have moved over to the LastUsedPresetId key
    const { data: defaultPreset } = await epicApi.getUserPreference(
      IUserPreferenceKey.DefaultPresetId
    )

    if (defaultPreset && isEpicUserPreference(defaultPreset)) {
      lastUsedPresetId = defaultPreset.Data[IUserPreferenceKey.DefaultPresetId]
    }

    if (lastUsedPresetId) {
      // log "defaultPreset loaded" to console for DATADOG monitoring
      logger.debug(
        `defaultPreset ID ${lastUsedPresetId} found in user preferences API`
      )
    }
  }

  return lastUsedPresetId
}
