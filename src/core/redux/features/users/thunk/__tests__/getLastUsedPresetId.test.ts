import { IUserPreferenceKey } from '@/core/services/epic'
import { getLastUsedPresetId } from '../getLastUsedPresetId'
import {
  apiPaths,
  createHandler,
  handlers,
  setupMockServer
} from '@/core/testing'

describe('getLastUsedPresetId', () => {
  const server = setupMockServer(handlers.getUserPreference)
  // defaultPreset should only be used if lastUsedPresetId des not exists
  server.use(
    createHandler({
      method: 'get',
      path: apiPaths.getUserPreference,
      response: {
        Key: 'PnlLive',
        Data: {
          [IUserPreferenceKey.DefaultPresetId]: 'defaultId'
        }
      },
      status: 200
    })
  )

  it('should return the lastUsedPresetId when it exists', async () => {
    const data = {
      lastUsedPresetId: '123'
    }

    const result = await getLastUsedPresetId(data)

    expect(result).toBe('123')
  })

  it('defaultPreset should only be used if lastUsedPresetId des not exists', async () => {
    const data = {
      lastUsedPresetId: undefined
    }

    const result = await getLastUsedPresetId(data)

    expect(result).toBe('default-preset-id')
  })
})
