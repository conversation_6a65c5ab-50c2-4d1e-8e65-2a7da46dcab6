import { getPresetDummy } from '@/core/testing/dummies'
import { useLastUsedPresetId } from './useLastUsedPresetId'
import { vi } from 'vitest'
import { logger } from '@/core/utils/logger'
import { getReduxWrapper } from '@/core/testing'
import { renderHook } from '@testing-library/react'

// mock logger
vi.mock('@/core/utils/logger', () => ({
  logger: {
    info: vi.fn(),
    debug: vi.fn()
  }
}))

describe('Name of the group', () => {
  const stateMock = {
    report: {
      reportName: 'PnlLive'
    },
    users: {
      userPreferences: {
        lastUsedPresetId: undefined
      }
    },
    presets: {
      sharedPresets: [
        getPresetDummy({ Id: 's1' }),
        getPresetDummy({ Id: 's2' }),
        getPresetDummy({ Id: 's3' })
      ],
      userPresets: [
        getPresetDummy({ Id: 'u1' }),
        getPresetDummy({ Id: 'u2' }),
        getPresetDummy({ Id: 'u3' })
      ]
    }
  }
  it('should select the last element of the Shared Presets collection', () => {
    const { wrapper } = getReduxWrapper(stateMock)
    const { result } = renderHook(useLastUsedPresetId, { wrapper })

    expect(result.current).toBe('s3')
  })

  it('should choose the last element of any Personal Presets', () => {
    const { wrapper } = getReduxWrapper({
      ...stateMock,
      presets: {
        ...stateMock.presets,
        sharedPresets: []
      }
    })
    const { result } = renderHook(useLastUsedPresetId, { wrapper })

    expect(result.current).toBe('u3')
  })

  it('should log "No preset loaded" to console', () => {
    const { wrapper } = getReduxWrapper({
      ...stateMock,
      presets: {
        ...stateMock.presets,
        sharedPresets: [],
        userPresets: []
      }
    })
    const { result } = renderHook(useLastUsedPresetId, { wrapper })

    expect(result.current).toBe(undefined)
    expect(logger.info).toHaveBeenCalledWith('No preset loaded')
  })

  it('should return lastUsedPresetId if it exists', () => {
    const { wrapper } = getReduxWrapper({
      ...stateMock,
      users: {
        ...stateMock.users,
        userPreferences: {
          lastUsedPresetId: 's2'
        }
      }
    })
    const { result } = renderHook(useLastUsedPresetId, { wrapper })

    expect(result.current).toBe('s2')
  })

  it('should return Shared Preset if lastUsedPresetId does not exists', () => {
    const { wrapper } = getReduxWrapper({
      ...stateMock,
      users: {
        ...stateMock.users,
        userPreferences: {
          lastUsedPresetId: 'this-preset-was-deleted'
        }
      }
    })
    const { result } = renderHook(useLastUsedPresetId, { wrapper })

    expect(result.current).toBe('s3')
  })
  it('should return Personal Preset if lastUsedPresetId does not exists and shared presets empty', () => {
    const { wrapper } = getReduxWrapper({
      ...stateMock,
      presets: {
        ...stateMock.presets,
        sharedPresets: []
      },
      users: {
        ...stateMock.users,
        userPreferences: {
          lastUsedPresetId: 'this-preset-was-deleted'
        }
      }
    })
    const { result } = renderHook(useLastUsedPresetId, { wrapper })

    expect(result.current).toBe('u3')
  })
})
