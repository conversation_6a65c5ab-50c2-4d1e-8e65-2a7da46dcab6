import { logger } from '@/core/utils/logger'
import { IPreset } from '@/core/services/epic'
import { useAppSelect } from '@/core/redux/hooks'
import { useMemo } from 'react'
import { getApplicationTitle } from '@/core/services/glue'

export const usePresetNameForTitle = () => {
  const lastUsedPresetId = useAppSelect(
    (state) => state.users.userPreferences.lastUsedPresetId
  )
  const sharedPresets = useAppSelect((state) => state.presets.sharedPresets)
  const userPresets = useAppSelect((state) => state.presets.userPresets)

  return useMemo(() => {
    let preset: IPreset | undefined = [...sharedPresets, ...userPresets].find(
      (preset) => preset.Id === lastUsedPresetId
    )

    // Fallback: select the last* element of the Shared Presets / User Presets collection
    if (!preset) {
      // Fallback: select the last* element of the Shared Presets collection
      if (sharedPresets.length > 0) {
        preset = sharedPresets[sharedPresets.length - 1]
      } else {
        // Otherwise, choose the last element of any Personal Presets;
        if (userPresets.length > 0) {
          preset = userPresets[userPresets.length - 1]
        } else {
          logger.info(`No Preset available to set Application Preset Title`)
        }
      }
    }

    if (preset) {
      // Append Current Preset to document Title for Better Readability
      document.title = `${preset.PresetName.toUpperCase()} - ${getApplicationTitle()}`

      logger.info(`Application Preset Title ${document.title} added`)
    }
  }, [lastUsedPresetId, sharedPresets, userPresets])
}
