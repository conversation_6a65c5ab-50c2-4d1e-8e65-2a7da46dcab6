import {
  isFloatingFiltersBarVisibleSelector,
  componentVisibilitySelector
} from '../componentVisibilitySelector'

describe('componentVisibilitySelector', () => {
  it('should return the side panel state', () => {
    const state = {
      users: {
        userPreferences: {
          componentVisibility: {
            isFloatingFiltersBarVisible: true,
            isFooterBarVisible: false,
            isGroupBarVisible: true,
            isPivotBarVisible: false,
            isStatusBarVisible: true
          }
        }
      }
    }

    const result = componentVisibilitySelector(state)

    expect(result).toEqual({
      isFloatingFiltersBarVisible: true,
      isFooterBarVisible: false,
      isGroupBarVisible: true,
      isPivotBarVisible: false,
      isStatusBarVisible: true
    })
  })

  it('isFloatingFiltersBarVisibleSelector should return isFloatingFiltersBarVisible false in pivotMode enabled', () => {
    const state = {
      grid: {
        isPivotModeEnabled: true
      },
      users: {
        userPreferences: {
          componentVisibility: {
            isFloatingFiltersBarVisible: true
          }
        }
      }
    }

    const result = isFloatingFiltersBarVisibleSelector(state)

    expect(result).toBe(false)
  })
})
