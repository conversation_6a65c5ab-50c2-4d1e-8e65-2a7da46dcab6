import { lastUsedPresetSelector } from '../lastUsedPresetSelector'

describe('lastUsedPresetSelector', () => {
  it('should return undefined when no preset is set', () => {
    const mockState = {
      users: {
        userPreferences: {
          lastUsedPresetId: null
        }
      }
    }
    expect(lastUsedPresetSelector(mockState as any)).toBeUndefined()
  })

  it('should return the last used preset from userPresets', () => {
    const mockState = {
      users: {
        userPreferences: {
          lastUsedPresetId: 'id1'
        }
      },
      presets: {
        userPresets: [
          {
            Id: 'id1',
            name: 'Preset 1'
          }
        ],
        sharedPresets: [
          {
            Id: 'id2',
            name: 'Preset 2'
          }
        ]
      }
    }
    const result = lastUsedPresetSelector(mockState as any)
    expect(result.name).toBe('Preset 1')
  })

  it('should return the last used preset from sharedPresets', () => {
    const mockState = {
      users: {
        userPreferences: {
          lastUsedPresetId: 'id2'
        }
      },
      presets: {
        userPresets: [
          {
            Id: 'id1',
            name: 'Preset 1'
          }
        ],
        sharedPresets: [
          {
            Id: 'id2',
            name: 'Preset 2'
          }
        ]
      }
    }
    const result = lastUsedPresetSelector(mockState as any)
    expect(result.name).toBe('Preset 2')
  })

  it('should handle undefined state gracefully', () => {
    const undefinedState = {
      users: undefined as any
    }
    const result = lastUsedPresetSelector(undefinedState as any)
    expect(result).toBeUndefined()
  })
})
