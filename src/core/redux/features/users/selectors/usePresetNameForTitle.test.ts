import { getPresetDummy } from '@/core/testing/dummies'
import { usePresetNameForTitle } from './usePresetNameForTitle'
import { vi } from 'vitest'
import { logger } from '@/core/utils/logger'
import { getReduxWrapper } from '@/core/testing'
import { renderHook } from '@testing-library/react'

// mock logger
vi.mock('@/core/utils/logger', () => ({
  logger: {
    info: vi.fn(),
    debug: vi.fn()
  }
}))

describe('Set Application Title Based on Preset', () => {
  const stateMock = {
    report: {
      reportName: 'PnlLive'
    },
    users: {
      userPreferences: {
        lastUsedPresetId: undefined
      }
    },
    presets: {
      sharedPresets: [
        getPresetDummy({ Id: 's1', PresetName: 'S1' }),
        getPresetDummy({ Id: 's2', PresetName: 'S2' }),
        getPresetDummy({ Id: 's3', PresetName: 'S3' })
      ],
      userPresets: [
        getPresetDummy({ Id: 'u1', PresetName: 'U1' }),
        getPresetDummy({ Id: 'u2', PresetName: 'U2' }),
        getPresetDummy({ Id: 'u3', PresetName: 'U3' })
      ]
    }
  }
  it('should select & log the last element of the Shared Presets collection', () => {
    const { wrapper } = getReduxWrapper(stateMock)
    renderHook(usePresetNameForTitle, { wrapper })

    expect(logger.info).toHaveBeenCalledWith(
      'Application Preset Title S3 - M72 added'
    )
  })

  it('should choose & log the last element of any Personal Presets', () => {
    const { wrapper } = getReduxWrapper({
      ...stateMock,
      presets: {
        ...stateMock.presets,
        sharedPresets: []
      }
    })
    renderHook(usePresetNameForTitle, { wrapper })

    expect(logger.info).toHaveBeenCalledWith(
      'Application Preset Title U3 - M72 added'
    )
  })

  it('should log "No Preset available to set Application Preset Title" to console', () => {
    const { wrapper } = getReduxWrapper({
      ...stateMock,
      presets: {
        ...stateMock.presets,
        sharedPresets: [],
        userPresets: []
      }
    })
    renderHook(usePresetNameForTitle, { wrapper })
    expect(logger.info).toHaveBeenCalledWith(
      'No Preset available to set Application Preset Title'
    )
  })
})
