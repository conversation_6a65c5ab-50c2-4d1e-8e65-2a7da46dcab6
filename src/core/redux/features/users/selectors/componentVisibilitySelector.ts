import { RootState } from '@/core/redux/store'
import { IComponentVisibilitySidePanel } from '@/core/services/epic'

export const componentVisibilitySelector = (
  state: RootState
): IComponentVisibilitySidePanel =>
  state.users.userPreferences.componentVisibility

export const isFloatingFiltersBarVisibleSelector = (
  state: RootState
): boolean =>
  state.users.userPreferences.componentVisibility.isFloatingFiltersBarVisible &&
  // Hide filter bar for pivot mode
  !state.grid.isPivotModeEnabled
