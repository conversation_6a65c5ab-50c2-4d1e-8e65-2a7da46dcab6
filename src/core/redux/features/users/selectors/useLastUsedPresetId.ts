import { logger } from '@/core/utils/logger'
import { useAppSelect } from '@/core/redux/hooks'
import { useMemo } from 'react'

export const useLastUsedPresetId = () => {
  const lastUsedPresetId = useAppSelect(
    (state) => state.users.userPreferences.lastUsedPresetId
  )
  const sharedPresets = useAppSelect((state) => state.presets.sharedPresets)
  const userPresets = useAppSelect((state) => state.presets.userPresets)

  return useMemo(() => {
    if (
      sharedPresets.some((preset) => preset.Id === lastUsedPresetId) ||
      userPresets.some((preset) => preset.Id === lastUsedPresetId)
    ) {
      logger.debug(`LastUsedPreset ID ${lastUsedPresetId} loaded`)
      return lastUsedPresetId
    }

    // Fallback: select the last* element of the Shared Presets collection
    if (sharedPresets.length > 0) {
      const preset = sharedPresets[sharedPresets.length - 1]
      // if there are more than 0 shared presets;
      // log "Shared preset <PresetName> loaded" to console(for DATADOG monitoring)
      logger.debug(`Shared preset ${preset.PresetName} loaded`)
      return preset.Id
    } else {
      // Otherwise, choose the last element of any Personal Presets;
      // log "Personal preset <PresetName> loaded" to console (for DATADOG monitoring)
      if (userPresets.length > 0) {
        const preset = userPresets[userPresets.length - 1]
        logger.debug(`Personal preset ${preset.PresetName} loaded`)
        return preset.Id
      } else {
        // If there are no personal presets either, don't use any preset
        // (no choice at this point, really)
        logger.info('No preset loaded')
        return lastUsedPresetId
      }
    }
  }, [lastUsedPresetId, sharedPresets, userPresets])
}
