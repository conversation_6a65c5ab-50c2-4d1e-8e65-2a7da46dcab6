import { RootState } from '@/core/redux/store'
import { lastUsedPresetIdSelector } from './lastUsedPresetIdSelector'

export const lastUsedPresetSelector = (state: RootState) => {
  const lastUsedPresetId = lastUsedPresetIdSelector(state)
  const preset =
    state?.presets?.userPresets.find((p) => p.Id === lastUsedPresetId) ||
    state?.presets?.sharedPresets.find((p) => p.Id === lastUsedPresetId)

  return preset
}
