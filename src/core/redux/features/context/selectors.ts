import { RootState } from '@/core/redux/store'
import { createSelector } from '@reduxjs/toolkit'

export const isConnectedToGlueChannelSelector = (state: RootState) =>
  state.context.isConnectedToGlueChannel

export const isConnectedSubscriberSelector = (state: RootState) =>
  state.context.isConnectedToGlueChannel && state.context.isConnectedSubscriber

export const isParentLiveModeEnabledSelector = (state: RootState) =>
  Boolean(state.context.isParentLiveModeEnabled)

export const rawMetadataSelector = (state: RootState) =>
  state.context.rawMetadata

export const metadataSelector = createSelector(
  [isConnectedSubscriberSelector, (state: RootState) => state.reportMetadata],
  (isConnectedSubscriber, reportMetadata) =>
    isConnectedSubscriber
      ? reportMetadata.parentMetadata
      : reportMetadata.metadata
)
