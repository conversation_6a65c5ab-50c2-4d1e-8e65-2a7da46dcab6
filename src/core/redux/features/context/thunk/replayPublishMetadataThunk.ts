import { RootState } from '@/core/redux/store'
import { createAsyncThunk } from '@reduxjs/toolkit'
import { rawMetadataSelector } from '@/core/redux/selectors'
import {
  MetadataUpdateEvent,
  SyncDataEventType
} from '@/core/services/glue/types'
import { publishToChannel } from '@/core/services/glue'
import { logger } from '@/core/utils/logger'

export const replayPublishMetadataThunk = createAsyncThunk(
  'report/replayPublishMetadataThunk',
  async (_, { getState }) => {
    const state = getState() as RootState
    const metadata = rawMetadataSelector(state)

    if (!metadata) {
      logger.debug('No metadata found to replay MetadataUpdate event')
      return
    }

    publishToChannel({
      type: SyncDataEventType.MetadataUpdate,
      metadata
    } as MetadataUpdateEvent)

    logger.debug(`Published (replay) MetadataUpdate event.`, metadata)
  }
)
