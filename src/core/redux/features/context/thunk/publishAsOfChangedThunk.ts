import { createAsyncThunk } from '@reduxjs/toolkit'
import { RootState } from '@/core/redux/store'
import {
  isSyncDataPublisherSelector,
  reportNameSelector,
  currentAsOfUtcSelector
} from '@/core/redux/selectors'
import { SyncDataEventType } from '@/core/services/glue/types'
import { publishToChannel } from '@/core/services/glue'
import { logger } from '@/core/utils/logger'

export const publishAsOfChangedThunk = createAsyncThunk(
  'report/publishAsOfChangedThunk',
  async (_, { getState }) => {
    const state = getState() as RootState
    const originatingReportAsOfUtc = currentAsOfUtcSelector(state)
    const isSyncDataPublisher = isSyncDataPublisherSelector(state)

    if (!isSyncDataPublisher) {
      return
    }

    publishToChannel({
      type: SyncDataEventType.OriginatingReportAsOfChanged,
      reportName: reportNameSelector(state),
      originatingReportAsOfUtc
    })

    logger.debug('Publish CurrentAsOfUtcChanged event', {
      originatingReportAsOfUtc
    })
  }
)
