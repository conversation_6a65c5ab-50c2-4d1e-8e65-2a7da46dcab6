import { RootState } from '@/core/redux/store'
import { createAsyncThunk } from '@reduxjs/toolkit'
import {
  lastOriginatingContextSelector,
  reportNameSelector
} from '@/core/redux/selectors'
import {
  GridRowSelectedEvent,
  SyncDataEventType
} from '@/core/services/glue/types'
import { publishToChannel } from '@/core/services/glue'
import { logger } from '@/core/utils/logger'
import { setLastOriginatingContext } from '@/core/redux/features/report/reportSlice'

export const replayPublishGridRowSelectedThunk = createAsyncThunk(
  'report/replayPublishGridRowSelectedThunk',
  async (_, { getState, dispatch }) => {
    const state = getState() as RootState
    const reportName = reportNameSelector(state)
    const originatingContext = lastOriginatingContextSelector(state)

    if (!originatingContext) {
      logger.debug(
        'No originating context found to replay GridRowSelected event'
      )
      return
    }

    publishToChannel({
      type: SyncDataEventType.GridRowSelected,
      reportName,
      originatingContext
    } as GridRowSelectedEvent)

    dispatch(setLastOriginatingContext(originatingContext))

    const publishedRowRoute = originatingContext.groupKey
      ? Object.values(originatingContext.groupKey)
      : '(root)'
    logger.debug(
      `Published (replay) drill through GridRowSelected event for row "${publishedRowRoute}" with originatingContext:`,
      originatingContext
    )
  }
)
