import { fetchMetadataThunk } from '../../../report/reportMetadata/thunk/fetchMetadataThunk'
import { publishToChannel } from '@/core/services/glue/glue'
import { setupStore } from '../../../../store'
import { vi } from 'vitest'
import { replayPublishMetadataThunk } from '../replayPublishMetadataThunk'
import { SyncDataEventType } from '@/core/services/glue/types'

vi.mock('@/core/services/glue/glue', () => ({
  publishToChannel: vi.fn()
}))

const store = setupStore()

describe('replayPublishMetadataThunk', () => {
  it('should publish rawMetadata to Glue (saved after fetchMetadataThunk fulfilled)', async () => {
    const metadata = {
      test: 'test'
    }
    const arg = undefined
    const requestId = 'testRequestId'

    await store.dispatch(
      fetchMetadataThunk.fulfilled(metadata as any, requestId, arg)
    )

    store.dispatch(replayPublishMetadataThunk())

    expect(publishToChannel).toHaveBeenCalledWith({
      type: SyncDataEventType.MetadataUpdate,
      metadata
    })
  })
})
