import { publishToChannel } from '@/core/services/glue/glue'
import { setupStore } from '../../../../store'
import { vi } from 'vitest'
import { publishAsOfChangedThunk } from '../publishAsOfChangedThunk'
import { SyncDataEventType } from '@/core/services/glue/types'
import {
  currentAsOfUtcSelector,
  isSyncDataPublisherSelector
} from '@/core/redux/selectors'

vi.mock('@/core/services/glue/glue', () => ({
  publishToChannel: vi.fn()
}))

describe('publishAsOfChangedThunk', () => {
  beforeEach(() => {
    vi.resetAllMocks()
  })

  it('should publish event with currentAsOfUtc if user is a publisher', async () => {
    const currentAsOfUtc = '2024-10-01T00:00:00Z'
    const store = setupStore({
      report: {
        currentAsOfUtc,
        publishSyncData: true,
        reportName: 'testReport'
      }
    })

    await store.dispatch(publishAsOfChangedThunk())

    expect(publishToChannel).toHaveBeenCalledWith({
      type: SyncDataEventType.OriginatingReportAsOfChanged,
      reportName: 'testReport',
      originatingReportAsOfUtc: currentAsOfUtc
    })
  })

  it('should not publish event if user is a subscriber', async () => {
    const currentAsOfUtc = '2024-10-01T00:00:00Z'
    const store = setupStore({
      report: {
        currentAsOfUtc,
        publishSyncData: false
      }
    })

    await store.dispatch(publishAsOfChangedThunk())

    expect(publishToChannel).not.toHaveBeenCalled()
  })
})
