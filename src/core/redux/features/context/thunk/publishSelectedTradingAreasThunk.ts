import { createAsyncThunk } from '@reduxjs/toolkit'
import { RootState } from '@/core/redux/store'
import {
  isSyncDataPublisherSelector,
  reportNameSelector,
  selectedTradingAreasSelector
} from '@/core/redux/selectors'
import {
  SelectedTradingAreasChangedEvent,
  SyncDataEventType
} from '@/core/services/glue/types'
import { publishToChannel } from '@/core/services/glue'
import { logger } from '@/core/utils/logger'

export const publishSelectedTradingAreasThunk = createAsyncThunk(
  'report/publishSelectedTradingAreasThunk',
  async (_, { getState }) => {
    const state = getState() as RootState
    const selectedTradingAreas = selectedTradingAreasSelector(state)
    const reportName = reportNameSelector(state)
    const isSyncDataPublisher = isSyncDataPublisherSelector(state)

    if (!isSyncDataPublisher) {
      return
    }

    publishToChannel({
      type: SyncDataEventType.SelectedTradingAreasChanged,
      reportName,
      selectedTradingAreas
    } as SelectedTradingAreasChangedEvent)

    logger.debug(
      `Published drill through SelectedTradingAreasChanged event for trading areas ${selectedTradingAreas}`
    )
  }
)
