import { createAsyncThunk } from '@reduxjs/toolkit'
import { RootState } from '@/core/redux/store'
import {
  isSyncDataPublisherSelector,
  reportNameSelector,
  isLiveModeEnabledSelector
} from '@/core/redux/selectors'
import {
  LiveModeChangedEvent,
  SyncDataEventType
} from '@/core/services/glue/types'
import { publishToChannel } from '@/core/services/glue'
import { logger } from '@/core/utils/logger'

export const publishLiveModeChangedThunk = createAsyncThunk(
  'report/publishLiveModeChangedThunk',
  async (_, { getState }) => {
    const state = getState() as RootState
    const isLiveModeEnabled = isLiveModeEnabledSelector(state)
    const reportName = reportNameSelector(state)
    const isSyncDataPublisher = isSyncDataPublisherSelector(state)

    if (!isSyncDataPublisher) {
      return
    }

    publishToChannel({
      type: SyncDataEventType.LiveModeChanged,
      reportName,
      isLiveModeEnabled
    } as LiveModeChangedEvent)

    logger.debug(`Published drill through LiveModeChanged event`, {
      isLiveModeEnabled
    })
  }
)
