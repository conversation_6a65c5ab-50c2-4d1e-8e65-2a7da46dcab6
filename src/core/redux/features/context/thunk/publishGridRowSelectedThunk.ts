import { RootState } from '@/core/redux/store'
import { RowSelectedEvent } from '@ag-grid-community/core'
import { createAsyncThunk } from '@reduxjs/toolkit'
import { reportNameSelector } from '@/core/redux/selectors'
import {
  GridRowSelectedEvent,
  SyncDataEventType
} from '@/core/services/glue/types'
import { publishToChannel } from '@/core/services/glue'
import { ContextAction } from '../../report/types'
import { logger } from '@/core/utils/logger'
import { setLastOriginatingContext } from '@/core/redux/features/report/reportSlice'
import { buildDrillThroughContext } from '@/core/services/glue/utils/drillThroughContext'

export const publishGridRowSelectedThunk = createAsyncThunk(
  'report/publishGridRowSelectedThunk',
  async (event: RowSelectedEvent, { getState, dispatch }) => {
    const state = getState() as RootState
    const reportName = reportNameSelector(state)
    const gridApi = event.api

    const contextActions = event.data['ContextActions']?.Actions as
      | ContextAction[]
      | undefined

    const drillThroughAction = contextActions?.find(
      (item) => item.Type === 'openDrillThroughView'
    )

    const originatingContext = buildDrillThroughContext({
      gridApi,
      originatingGroupKey: drillThroughAction
        ? drillThroughAction.Params.OriginatingGroupKey
        : '',
      state
    })

    publishToChannel({
      type: SyncDataEventType.GridRowSelected,
      reportName,
      originatingContext
    } as GridRowSelectedEvent)

    // save originating context to the store for reuse in replay thunk when listener joins channel
    dispatch(setLastOriginatingContext(originatingContext))

    const publishedRowRoute = originatingContext.groupKey
      ? Object.values(originatingContext.groupKey)
      : '(root)'
    logger.debug(
      `Published drill through GridRowSelected event for row "${publishedRowRoute}" with originatingContext:`,
      originatingContext
    )
  }
)
