import { IMetaDataResponse } from '@/core/services/riskReport'
import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { fetchMetadataThunk } from '../report/reportMetadata/thunk/fetchMetadataThunk'

export interface ContextState {
  isConnectedToGlueChannel: boolean
  isParentLiveModeEnabled?: boolean
  rawMetadata?: IMetaDataResponse
  isConnectedSubscriber: boolean
}

export const initialContextState: ContextState = {
  isConnectedToGlueChannel: false,
  isParentLiveModeEnabled: true,
  rawMetadata: undefined,
  isConnectedSubscriber: false
}

const contextSlice = createSlice({
  name: 'context',
  initialState: initialContextState,
  reducers: {
    setConnectedToGlueChannel: (state, { payload }: PayloadAction<boolean>) => {
      state.isConnectedToGlueChannel = payload
    },
    setParentLiveModeEnabled: (state, { payload }: PayloadAction<boolean>) => {
      state.isParentLiveModeEnabled = payload
    },
    setIsConnectedSubscriber: (state, { payload }: PayloadAction<boolean>) => {
      state.isConnectedSubscriber = payload
    }
  },
  extraReducers(builder) {
    builder.addCase(
      fetchMetadataThunk.fulfilled,
      (state, { payload }: PayloadAction<IMetaDataResponse | undefined>) => {
        state.rawMetadata = payload
      }
    )
  }
})

export const {
  setConnectedToGlueChannel,
  setParentLiveModeEnabled,
  setIsConnectedSubscriber
} = contextSlice.actions

export const contextReducer = contextSlice.reducer
