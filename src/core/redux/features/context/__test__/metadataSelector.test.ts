import { describe, it, expect } from 'vitest'
import { metadataSelector } from '../selectors'

describe('metadataSelector', () => {
  it('Given isConnectedSubscriber=false, When metadataSelector is called, Then it returns metadata', () => {
    const state = {
      context: {
        isConnectedToGlueChannel: false,
        isConnectedSubscriber: false
      },
      reportMetadata: {
        metadata: { foo: 'bar' },
        parentMetadata: { fooParent: 'barParent' }
      }
    }
    const result = metadataSelector(state as any)
    expect(result).toEqual({ foo: 'bar' })
  })

  it('Given isConnectedSubscriber=true, When metadataSelector is called, Then it returns parentMetadata', () => {
    const state = {
      context: {
        isConnectedToGlueChannel: true,
        isConnectedSubscriber: true
      },
      reportMetadata: {
        metadata: { foo: 'bar' },
        parentMetadata: { fooParent: 'barParent' }
      }
    }
    const result = metadataSelector(state as any)
    expect(result).toEqual({ fooParent: 'barParent' })
  })

  it('Given missing parentMetadata, When subscriber is true, Then it returns undefined', () => {
    const state = {
      context: {
        isConnectedToGlueChannel: true,
        isConnectedSubscriber: true
      },
      reportMetadata: {
        metadata: { foo: 'bar' }
      }
    }
    const result = metadataSelector(state as any)
    expect(result).toBeUndefined()
  })

  it('Given missing metadata, When subscriber is false, Then it returns undefined', () => {
    const state = {
      context: {
        isConnectedToGlueChannel: false,
        isConnectedSubscriber: false
      },
      reportMetadata: {
        parentMetadata: { fooParent: 'barParent' }
      }
    }
    const result = metadataSelector(state as any)
    expect(result).toBeUndefined()
  })
})
