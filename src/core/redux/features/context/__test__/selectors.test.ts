import {
  isConnectedToGlueChannelSelector,
  isConnectedSubscriberSelector,
  isParentLiveModeEnabledSelector,
  rawMetadataSelector
} from '../selectors'

describe('context selectors', () => {
  it('isConnectedToGlueChannelSelector should return the connection state', () => {
    const state = {
      context: {
        isConnectedToGlueChannel: true
      }
    }

    const result = isConnectedToGlueChannelSelector(state)

    expect(result).toBe(true)
  })

  it('isConnectedSubscriberSelector should return the subscriber connection state', () => {
    const state = {
      context: {
        isConnectedToGlueChannel: true,
        isConnectedSubscriber: true
      }
    }

    const result = isConnectedSubscriberSelector(state)

    expect(result).toBe(true)
  })

  it('isParentLiveModeEnabledSelector should return the parent live mode state', () => {
    const state = {
      context: {
        isParentLiveModeEnabled: true
      }
    }

    const result = isParentLiveModeEnabledSelector(state)

    expect(result).toBe(true)
  })

  it('rawMetadataSelector should return the raw metadata', () => {
    const state = {
      context: {
        rawMetadata: { key: 'value' }
      }
    }

    const result = rawMetadataSelector(state)

    expect(result).toEqual({ key: 'value' })
  })
})
