import { store } from './store'

describe('Redux store', () => {
  it('should have dispatch method', () => {
    expect(store.dispatch).toBeDefined()
  })

  it('should have getState method', () => {
    expect(store.getState).toBeDefined()
  })

  it('should have subscribe method', () => {
    expect(store.subscribe).toBeDefined()
  })

  it('should have grid reducer', () => {
    expect(store.getState().grid).toBeDefined()
  })

  it('should have report reducer', () => {
    expect(store.getState().report).toBeDefined()
  })
})
