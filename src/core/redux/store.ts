import { reportReducer } from '@/core/redux/features/report'
import { usersReducer } from '@/core/redux/features/users'
import { listenerMiddlewareInstance } from '@/core/redux/middleware'
import {
  combineReducers,
  configureStore,
  PreloadedState
} from '@reduxjs/toolkit'
import { setupListeners } from '@reduxjs/toolkit/query'
import { epicRtkQueryApi as epicApi } from '../services/epic'
import { gridReducer } from './features/grid'
import { notificationsReducer } from './features/notifications'
import { presetsReducer } from './features/presets'
import { executionTimeFilterReducer } from './features/quickFilters/executionTimeFilter/executionTimeFilterSlice'
import { reportMetadataReducer } from './features/report/reportMetadata'
import { contextReducer } from './features/context/contextSlice'

const rootReducer = combineReducers({
  report: reportReducer,
  reportMetadata: reportMetadataReducer,
  grid: gridReducer,
  notifications: notificationsReducer,
  presets: presetsReducer,
  users: usersReducer,
  executionTimeFilter: executionTimeFilterReducer,
  [epicApi.reducerPath]: epicApi.reducer,
  context: contextReducer
})

export const setupStore = (preloadedState?: PreloadedState<RootState>) =>
  configureStore({
    reducer: rootReducer,
    preloadedState,
    devTools: true,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          // Ignore these action types
          ignoredActions: ['report/longPollGetLatestAsOfUtc']
        }
      })
        .prepend(listenerMiddlewareInstance.middleware)
        .concat(epicApi.middleware)
  })

export const store = setupStore()

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof rootReducer>
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch

// optional, but required for refetchOnFocus/refetchOnReconnect behaviors
// see `setupListeners` rtk-query docs - takes an optional callback as the 2nd arg for customization
setupListeners(store.dispatch)
