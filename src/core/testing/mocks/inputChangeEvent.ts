import { ChangeEvent } from 'react'

/**
 * Creates a synthetic input change event with the given value.
 */
export const createInputChangeEvent = (
  value: string
): ChangeEvent<HTMLInputElement> => {
  const inputElement = document.createElement('input')
  inputElement.value = value

  const event = new Event('change', {
    bubbles: true
  })

  Object.defineProperty(event, 'target', {
    writable: false,
    value: inputElement
  })

  return event as unknown as ChangeEvent<HTMLInputElement>
}
