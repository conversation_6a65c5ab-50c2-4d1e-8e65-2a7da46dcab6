import { initialNotificationsState } from '@/core/redux/features/notifications'
import { initialGridState } from '@/core/redux/features/grid'
import { initialPresetsState } from '@/core/redux/features/presets'
import { RootState } from '@/core/redux/store'
import { initialReportState } from '@/core/redux/features/report/reportSlice'
import { RiskReportApiType } from '@/core/services/riskReport/riskReportApi'
import { EpicApiType } from '@/core/services/epic'

export const stateMock: RootState = {
  grid: {
    ...initialGridState
  },
  notifications: {
    ...initialNotificationsState
  },
  report: {
    ...initialReportState
  },
  presets: {
    ...initialPresetsState
  },
  riskReportApi: {} as RiskReportApiType,
  epicApi: {} as EpicApiType
}
