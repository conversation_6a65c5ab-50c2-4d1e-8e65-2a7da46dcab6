import { vi } from 'vitest'
import { ColumnApi } from '@ag-grid-community/core'

export const columnApiMock = {
  // Add the methods and properties you need to mock here
  autoSizeColumn: vi.fn(),
  autoSizeAllColumns: vi.fn(),
  getColumnState: vi.fn().mockReturnValue([]),
  getColumns: vi.fn().mockReturnValue([]),
  getPivotResultColumns: vi.fn().mockReturnValue([]),
  getValueColumns: vi.fn().mockReturnValue([]),
  getPivotColumns: vi.fn().mockReturnValue([]),
  setPivotResultColumns: vi.fn(),
  setValueColumns: vi.fn(),
  setPivotColumns: vi.fn(),
  isPivotMode: vi.fn().mockReturnValue(false),
  getAllColumns: vi.fn().mockReturnValue([]),
  setPivotMode: vi.fn(),
  resetColumnState: vi.fn()
  // ... other ColumnApi methods you need to mock
} as unknown as ColumnApi

// TODO: Replace the `columnApiMock` occurrences with the `getColumnApiMock` function.
export const getColumnApiMock = (overrides?: Partial<ColumnApi>): ColumnApi =>
  ({
    ...columnApiMock,
    ...overrides
  } as ColumnApi)
