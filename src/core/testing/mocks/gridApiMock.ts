import { GridApi } from '@ag-grid-community/core'
import { vi } from 'vitest'
import { columnApiMock } from './columnApiMock'

export const gridApiMock = {
  setColumnDefs: vi.fn(),
  setRowData: vi.fn(),
  sizeColumnsToFit: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  getColumns: vi.fn().mockReturnValue([]),
  getColumnDefs: vi.fn().mockReturnValue([]),
  getFilterModel: vi.fn().mockReturnValue({}),
  getModel: vi.fn().mockReturnValue({
    forEachNode: vi.fn()
  }),
  setServerSideDatasource: vi.fn(),
  refreshToolPanel: vi.fn(),
  refreshServerSide: vi.fn(),
  forEachNode: vi.fn(),
  getDisplayedRowCount: vi.fn(),
  getCacheBlockState: vi.fn().mockReturnValue({})
  // ... other GridApi methods you need to mock
} as unknown as GridApi

// TODO: Replace the `gridApiMock` occurrences with the `getGridApiMock` function.
export const getGridApiMock = (overrides?: Partial<GridApi>): GridApi =>
  ({
    ...gridApiMock,
    ...overrides
  } as GridApi)

export const gridApiRefMock = {
  current: {
    api: gridApiMock,
    columnApi: columnApiMock
  } as any
}
