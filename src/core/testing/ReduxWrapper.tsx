import { Provider } from 'react-redux'
import { RootState, setupStore } from '../redux/store'
import { DeepPartial } from '@/core/utils/types'

// TODO: Replace the function with `getReduxWrapper` from the `testWrappers.ts` file.
export const ReduxWrapper =
  (initialState?: DeepPartial<RootState>) =>
  ({ children }: { children?: React.ReactNode }) =>
    (
      <Provider store={setupStore(initialState as RootState)}>
        {children}
      </Provider>
    )
