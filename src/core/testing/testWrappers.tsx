import {
  InitializeApp,
  InitializeAppProps
} from '@/components/App/InitializeApp'
import { InitializeData } from '@/components/App/InitializeData'
import { GridProvider } from '@/components/Grid/context/GridProvider'
import { RootState, setupStore } from '@/core/redux/store'
import { gridApiRefMock } from '@/core/testing/mocks'
import { AgGridReact } from '@ag-grid-community/react'
import { DeepPartial } from '@reduxjs/toolkit'
import React, { RefObject } from 'react'
import { Provider } from 'react-redux'
import { MemoryRouter, Route, Routes } from 'react-router-dom'
import { WrapperComponentType } from '../utils/types'

/**
 * `WrapperComponent` is a type alias for a component wrapper used in testing React Hooks.
 */
type WrapperComponent = WrapperComponentType

/**
 * `TestWrapper` returns an object with a `wrapper` component and optional other properties that belong to a particular wrapper.
 * Example:
 * const { wrapper } = getGridProviderWrapper()
 * const { wrapper, store } = getReduxWrapper() // the `store` value is only from the redux wrapper.
 */
type TestWrapper<TProps, TReturnType = unknown> = (
  props?: TProps
) => TReturnType & {
  wrapper: WrapperComponent
}

export const getGridProviderWrapper: TestWrapper<{
  gridApiRef?: RefObject<AgGridReact<any>>
}> = (props) => {
  const gridApiRef = props?.gridApiRef || gridApiRefMock

  return {
    wrapper: ({ children }) => (
      <GridProvider gridApiRef={gridApiRef}>{children}</GridProvider>
    )
  }
}

export const getReduxWrapper: TestWrapper<
  DeepPartial<RootState>,
  { store: ReturnType<typeof setupStore>; resetStore: () => void }
> = (initialState) => {
  let store = setupStore(initialState as RootState)

  const resetStore = () => {
    store = setupStore(initialState as RootState)
  }

  return {
    wrapper: ({ children }) => <Provider store={store}>{children}</Provider>,
    store,
    resetStore
  }
}

export const getInitializeDataWrapper: TestWrapper<void> = () => {
  return {
    wrapper: ({ children }) => <InitializeData>{children}</InitializeData>
  }
}

export const getInitializeAppWrapper = (
  url = '/reports/trades',
  initialState = undefined
) => {
  const { wrapper: reduxWrapper, store } = getReduxWrapper(initialState)
  const wrapper = combineWrappers(wrapWithUrl(url), reduxWrapper, InitializeApp)

  return { wrapper, store }
}

/**
 * `combineWrappers` is a utility function that combines multiple wrappers into a single wrapper.
 * This is particularly useful when a hook under test requires multiple wrappers, e.g. Redux and Grid Provider:
 *
 * const reduxWrapper = getReduxWrapper()
 * const gridProviderWrapper = getGridProviderWrapper()
 *
 * const wrapper = combineWrappers(
 *   reduxWrapper.wrapper,
 *   gridProviderWrapper.wrapper
 * )
 *
 * const { result } = renderHook(useHook, {
 *   wrapper
 * })
 *
 */

export const combineWrappers = (
  ...wrappers: WrapperComponent[]
): WrapperComponent => {
  return ({ children }) => {
    return wrappers.reduceRight((nestedChildren, Wrapper) => {
      return <Wrapper>{nestedChildren}</Wrapper>
    }, children)
  }
}

export const wrapWithUrl = (url: string): WrapperComponent => {
  return ({ children }) => {
    return (
      <MemoryRouter initialEntries={[url]}>
        <Routes>
          <Route path="/reports/:reportName" element={children} />
        </Routes>
      </MemoryRouter>
    )
  }
}
