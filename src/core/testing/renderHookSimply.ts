import { renderHook } from '@testing-library/react'
import { WrapperComponentType } from '../utils/types'
import { DeepPartial } from '@reduxjs/toolkit'
import { RootState } from '../redux/store'
import { getReduxWrapper } from './testWrappers'

/**
 * Makes it much simpler to render hooks in the tests.
 * Example usage:
 * const { changeTab } = renderHookSimply(useTabs)
 */
export const renderHookSimply = <TProps, TResult>(
  callback: (props: TProps) => TResult,
  wrapper?: WrapperComponentType
) => {
  const { result } = renderHook(callback, {
    wrapper
  })
  return result.current
}

export const renderHookWithRedux = <TProps, TResult>(
  callback: (props: TProps) => TResult,
  initialState?: DeepPartial<RootState>
) => {
  const { wrapper } = getReduxWrapper(initialState)
  return renderHookSimply(callback, wrapper)
}
