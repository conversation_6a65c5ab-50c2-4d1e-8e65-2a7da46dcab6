import { renderHookWithRedux } from './renderHookSimply'
import { useAppSelect } from '../redux/hooks'
import { describe, it, expect } from 'vitest'

describe('renderHookWithRedux', () => {
  it('should render hook with initial state', () => {
    const initialState = {
      report: {
        isLiveModeEnabled: true,
        reportName: 'test-report'
      }
    }

    const result = renderHookWithRedux(
      () => useAppSelect((state) => state.report),
      initialState
    )

    expect(result.isLiveModeEnabled).toBe(true)
    expect(result.reportName).toBe('test-report')
  })
})
