import { render } from '@testing-library/react'
import { ReactElement } from 'react'
import { Provider } from 'react-redux'
import { setupStore } from '../redux/store'
import { stateMock } from '.'

export const renderWithReduxInitialState = (
  ui: ReactElement,
  initialState = {}
) => {
  const store = setupStore(initialState)
  return {
    store,
    ...render(ui, {
      wrapper: ({ children }) => <Provider store={store}>{children}</Provider>
    })
  }
}

export const renderWithMockedState = (ui: ReactElement, initialState = {}) => {
  return {
    stateMock,
    ...renderWithReduxInitialState(ui, { ...stateMock, ...initialState })
  }
}
