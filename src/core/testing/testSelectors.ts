import { getByPlaceholderText, screen, waitFor } from '@testing-library/react'

const getByDataToggleButtonId = (container: HTMLElement, id: string) =>
  container.querySelector(`[data-toggle-button-id="${id}"]`)

/**
 * agGrid Execution Time column filter button
 * @param screen
 * @returns
 */
export const getExecutionTimeFilterButton = async () => {
  let filterButtons: HTMLElement[]

  await waitFor(
    () => {
      filterButtons = screen.getAllByRole('button', {
        name: 'Open Filter Menu'
      })
    },
    {
      timeout: 4000
    }
  )

  if (!filterButtons) {
    throw new Error('Filter buttons not found')
  }

  return filterButtons[4]
}

/**
 * Wait for Quick Filters to render
 * @param container
 * @returns
 */
export const waitForQuickFilters = async (container: HTMLElement) => {
  let fromLabel

  await waitFor(
    () => {
      fromLabel = container.querySelector(
        'core-clib-page-header-item[label="From"]'
      )
      expect(fromLabel).toBeInTheDocument()
    },
    {
      timeout: 4000
    }
  )

  const toLabel = document.querySelector(
    'core-clib-page-header-item[label="To"]'
  )

  const AllBtn = getByDataToggleButtonId(container, 'all')
  const TodayBtn = getByDataToggleButtonId(container, 'today')
  const T1Btn = getByDataToggleButtonId(container, 't1')
  const datePickerFrom = getByPlaceholderText(fromLabel, /field_input_date/)
  const datePickerTo = getByPlaceholderText(toLabel, /field_input_date/)

  return {
    AllBtn,
    TodayBtn,
    T1Btn,
    fromLabel,
    datePickerFrom,
    datePickerTo
  }
}
