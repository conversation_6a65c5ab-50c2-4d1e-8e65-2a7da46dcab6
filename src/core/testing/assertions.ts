/**
 * This helper function is used to perform deep equality checks in the tests.
 * It extends <PERSON><PERSON>'s `expect.objectContaining` method to recursively check nested properties of an object.
 *
 * It's useful when you want to assert that an object contains certain expected properties,
 * but you don't care about other properties that might be present.
 *
 * @param {Object} obj - The object that we want to match against another object.
 *
 * @returns {Object} - A Jest matcher that can be used with `expect().toHaveBeenCalledWith()`, `expect().toEqual()`, etc.
 *
 * @example
 * expect(reqSpy).toHaveBeenCalledWith(
 *   deepObjectContaining({
 *     prop1: {
 *       nestedProp: 'value'
 *     },
 *     prop2: ['element1', 'element2']
 *   })
 * );
 */
export const deepObjectContaining = (
  obj: Record<string, unknown>
): Record<string, unknown> => {
  if (typeof obj !== 'object' || obj === null) {
    return obj
  }

  if (Array.isArray(obj)) {
    return expect.arrayContaining(obj.map(deepObjectContaining))
  }

  return expect.objectContaining(
    Object.keys(obj).reduce((result: Record<string, unknown>, key: string) => {
      const param = obj[key]
      result[key] = deepObjectContaining(param as Record<string, unknown>)
      return result
    }, {})
  )
}
