import { env } from '@/core/config'
import {
  epicApiEndpoints,
  IEpicUserPreference,
  IEpicUserProfile,
  IPreset,
  IPresetToShare,
  IUserPreferences,
  IUsersByRole
} from '@/core/services/epic'
import {
  IDistinctColumnValuesRequest,
  IDistinctColumnValuesResponse,
  IGetReportDetailsResponse,
  ILatestAsOfResponse,
  IMetaDataRequest,
  IMetaDataResponse,
  ISubsetRequest,
  ISubsetResponse,
  ISummaryRequest,
  ISummaryResponse
} from '@/core/services/riskReport'
import { riskReportApiEndpoints } from '@/core/services/riskReport/apiEndpoints'
import { aggregatorApiEndpoints } from '@/core/services/simultaneousAccess'
import {
  getAllPresetsResponseDummy,
  getDistinctColumnValuesResponseDummy,
  getLatestAsOfUtcResponseDummy,
  getMetadataResponseDummy,
  getPresetDummy,
  getReportDetailsResponseDummy,
  getSubsetResponseDummy,
  getSummaryResponseDummy,
  getUserPreferenceResponseDummy,
  getUserProfileDummy,
  getUsersByRolesDummy,
  saveUserPreferenceResponseDummy
} from '@/core/testing/dummies'
import { ValueOf } from '@/core/utils/types'
import { HeadersObject } from 'headers-polyfill'
import { DefaultBodyType, rest } from 'msw'
import { Mock } from 'vitest'

/**
 * Clear the query params from the pathname, because MSW expects only the pathname to be passed to a handler without query params.
 */
export const removeQueryParams = (path: string): string => {
  return path.split('?')[0]
}

// Define a list of API paths with wildcard matching for request handlers.
export const apiPaths = {
  getDistinctColumnValues: `*${riskReportApiEndpoints.GetDistinctColumnValues(
    '*'
  )}`,
  latestAsOf: `*${riskReportApiEndpoints.LatestAsOf('*', '*')}`,
  getReportDetails: `*${riskReportApiEndpoints.getReportDetails('*')}`,
  getSubset: `*${riskReportApiEndpoints.GetSubset('*')}`,
  getMetadata: `*${riskReportApiEndpoints.GetMetadata('*')}`,
  getSummary: `*${riskReportApiEndpoints.GetSummary('*')}`,
  getUserProfile: `*${epicApiEndpoints.getUserProfile}`,
  getUsersByRoles: `*${removeQueryParams(epicApiEndpoints.getUsersByRoles)}`,
  getUserPreference: `*${epicApiEndpoints.getUserPreference('*')}`,
  savePreset: `*${epicApiEndpoints.savePreset}`,
  deletePreset: `*${epicApiEndpoints.deletePreset}`,
  sendPresetCopy: `*${epicApiEndpoints.sendPresetCopy()}`,
  sharePreset: `*${epicApiEndpoints.sharePreset}`,
  saveUserPreference: `*${epicApiEndpoints.saveUserPreference}`,
  getAllPresets: `*${removeQueryParams(epicApiEndpoints.getAllPresets('*'))}`,
  getClientDetails: `*${aggregatorApiEndpoints.getClientDetails}`,
  connectAggregatorWebSocket: `${env.aggregatorApiUrl}/socket.io`,
  getKnowledgeDatesAsOf: `*${riskReportApiEndpoints.GetKnowledgeDatesAsOf('*')}`
} as const

interface CreateHandlerParams<TResponse> {
  method: 'post' | 'get' | 'delete' | 'put'
  path: string
  response?: TResponse
  status?: number
  headers?: HeadersObject
  reqSpy?: Mock
}

/**
 * Utility function that takes a configuration object and returns a request handler for the specified method, path, response, and status.
 * This function simplifies the process of creating request handlers by reducing repeatable code.
 */
export const createHandler = <
  TRequest extends DefaultBodyType,
  TResponse extends DefaultBodyType
>({
  method,
  path,
  response,
  status = 200,
  headers = {},
  reqSpy
}: CreateHandlerParams<TResponse>) =>
  rest[method]<TRequest, never, TResponse>(path, (req, res, ctx) => {
    const { searchParams, pathname } = req.url
    const { body } = req
    reqSpy &&
      reqSpy({
        searchParams: Object.fromEntries(searchParams.entries()),
        pathname,
        body
      })
    return res(
      ctx.status(status),
      ctx.json(response as TResponse),
      ctx.set(headers)
    )
  })

/**
 * Define a list of request handlers for different API endpoints.
 */
export const handlers = {
  getDistinctColumnValues: createHandler<
    IDistinctColumnValuesRequest,
    IDistinctColumnValuesResponse
  >({
    method: 'post',
    path: apiPaths.getDistinctColumnValues,
    response: getDistinctColumnValuesResponseDummy()
  }),
  latestAsOf: createHandler<never, ILatestAsOfResponse>({
    method: 'get',
    path: apiPaths.latestAsOf,
    response: getLatestAsOfUtcResponseDummy()
  }),
  getReportDetails: createHandler<never, IGetReportDetailsResponse>({
    method: 'get',
    path: apiPaths.getReportDetails,
    response: getReportDetailsResponseDummy()
  }),
  getSubset: createHandler<ISubsetRequest, ISubsetResponse>({
    method: 'post',
    path: apiPaths.getSubset,
    response: getSubsetResponseDummy()
  }),
  getMetadata: createHandler<IMetaDataRequest, IMetaDataResponse>({
    method: 'post',
    path: apiPaths.getMetadata,
    response: getMetadataResponseDummy()
  }),
  getSummary: createHandler<ISummaryRequest, ISummaryResponse>({
    method: 'post',
    path: apiPaths.getSummary,
    response: getSummaryResponseDummy()
  }),
  getUserProfile: createHandler<string, IEpicUserProfile>({
    method: 'get',
    path: apiPaths.getUserProfile,
    response: getUserProfileDummy()
  }),
  getUsersByRoles: createHandler<string, IUsersByRole>({
    method: 'post',
    path: apiPaths.getUsersByRoles,
    response: getUsersByRolesDummy()
  }),
  getUserPreference: createHandler<
    string,
    IEpicUserPreference<IUserPreferences>
  >({
    method: 'get',
    path: apiPaths.getUserPreference,
    response: getUserPreferenceResponseDummy()
  }),
  saveUserPreference: createHandler<
    IEpicUserPreference<IUserPreferences>,
    IEpicUserPreference<IUserPreferences>
  >({
    method: 'post',
    path: apiPaths.saveUserPreference,
    response: saveUserPreferenceResponseDummy()
  }),
  getAllPresets: createHandler<string, IPreset[]>({
    method: 'get',
    path: apiPaths.getAllPresets,
    response: getAllPresetsResponseDummy()
  }),
  savePreset: createHandler<IPreset, IPreset>({
    method: 'post',
    path: apiPaths.savePreset,
    response: getPresetDummy()
  }),
  deletePreset: createHandler<string, never>({
    method: 'delete',
    path: apiPaths.deletePreset
  }),
  sharePreset: createHandler<IPresetToShare, never>({
    method: 'post',
    path: apiPaths.sharePreset
  }),
  sendPresetCopy: createHandler<string[], never>({
    method: 'post',
    path: apiPaths.sendPresetCopy
  }),
  getClientDetails: createHandler<never, { data: string }>({
    method: 'get',
    path: apiPaths.getClientDetails,
    response: {
      data: '***********'
    }
  }),
  connectAggregatorWebSocket: createHandler<never, never>({
    method: 'get',
    path: apiPaths.connectAggregatorWebSocket
  })
}

export const allHandlers = [...Object.values(handlers)]
