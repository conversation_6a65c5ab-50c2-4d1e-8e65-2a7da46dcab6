import { setupServer } from 'msw/node'

export type HandlerParams = Parameters<typeof setupServer>

/**
 * A utility function that takes request handlers as arguments and sets up a mock server using the MSW library.
 * Use it to mock API calls in integration tests.
 */
export const setupMockServer = (...handlers: HandlerParams) => {
  // Create a server instance with the provided request handlers.
  const server = setupServer(...handlers)

  // Enable request interception before all tests in the suite
  // and configure the server to throw an error for unhandled requests.
  beforeAll(() => server.listen({ onUnhandledRequest: 'warn' }))

  // Reset the request handlers after each test to avoid affecting
  // other tests with altered handlers.
  afterEach(() => server.resetHandlers())

  // Clean up the server instance after all tests in the suite.
  afterAll(() => server.close())

  // Return the server instance for further customization if needed.
  return server
}
