import { apiP<PERSON>s, createHandler, handlers } from '@/core/testing'

import {
  getColumnInfoDummy,
  getDistinctColumnValuesResponseDummy,
  getReportDetailsResponseDummy,
  getSubsetResponseDummy
} from '@/core/testing/dummies'
import { ColumnType } from '@/core/services/riskReport'
import { EXECUTION_TIME } from '@/core/redux/features/quickFilters/executionTimeFilter/constants'

import { setupServer } from 'msw/node'
import { rest } from 'msw'
import { vi } from 'vitest'

export const setupMockBackend = () => {
  const server = setupServer()
  server.resetHandlers()
  server.listen({ onUnhandledRequest: 'warn' })
  server.use(handlers.getUserPreference)
  server.use(handlers.getDistinctColumnValues)
  server.use(handlers.getSummary)
  server.use(handlers.getClientDetails)
  server.use(handlers.getAllPresets)
  server.use(handlers.getUserProfile)
  server.use(handlers.getUsersByRoles)
  server.use(handlers.getMetadata)

  server.use(
    createHandler({
      method: 'get',
      path: apiPaths.latestAsOf,
      response: {
        latestAsOfUtc: '2024-08-12T12:58:30.0009923Z'
      },
      status: 200
    })
  )

  server.use(
    createHandler({
      method: 'get',
      path: apiPaths.getReportDetails,
      response: getReportDetailsResponseDummy({
        columnInfo: [
          getColumnInfoDummy({ column: 'Team', filterType: 'TextSet' }),
          getColumnInfoDummy({
            name: 'TradingArea',
            column: 'TradingArea',
            filterType: 'TextSet'
          }),
          getColumnInfoDummy({
            name: 'Strategy',
            column: 'Strategy',
            filterType: 'TextSet'
          }),
          getColumnInfoDummy({
            name: 'ProductType',
            column: 'ProductType',
            filterType: 'TextSet'
          }),
          getColumnInfoDummy({
            name: 'ExecDateTimeUtc',
            column: EXECUTION_TIME,
            filterType: 'Date',
            type: ColumnType.DateTime
          })
        ],
        preProcessingParamSpecs: [],
        reportType: 'reportType'
      })
    })
  )

  server.use(
    rest.post(
      '*/api/RiskReports/Trades/GetDistinctColumnValues/*',
      (req, res, ctx) => {
        return res(
          ctx.status(200),
          ctx.json(
            getDistinctColumnValuesResponseDummy({
              Team: ['ARNO', 'BALT'],
              TradingArea: ['ARNO', 'BALT', 'ESPO'],
              Strategy: ['ARNO', 'AUDCAD', 'AUDCHF'],
              ProductType: ['AssetSwap', 'Bond', 'CDS'],
              ExecDateTimeUtc: [
                '2024-08-16T13:27:15.0000000Z',
                '2024-08-17T10:00:10.0000000Z'
              ]
            })
          )
        )
      }
    )
  )

  const getSubsetReqSpy = vi.fn()

  server.use(
    createHandler({
      reqSpy: getSubsetReqSpy,
      method: 'post',
      path: apiPaths.getSubset,
      response: getSubsetResponseDummy(),
      status: 200
    })
  )

  return { server, getSubsetReqSpy }
}
