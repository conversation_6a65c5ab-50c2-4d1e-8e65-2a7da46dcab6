import {
  IEpicUserProfile,
  IUser,
  IUsersByRole,
  IUserRole,
  IPresetShareType,
  IPreset,
  ClibPreset,
  IPresetState,
  IPresetShare,
  ClibPresetToCreate,
  IPresetsGroupId,
  IPresetShareAccessType
} from '@/core/services/epic'

export const getUserProfileDummy = (
  overrides?: Partial<IEpicUserProfile>
): IEpicUserProfile => ({
  UserId: 'MG13234',
  ...overrides
})

export const getUserDummy = (overrides?: Partial<IUser>): IUser => ({
  EmployeeFirstName: 'Adam',
  EmployeeLastName: 'Jackson',
  ADUserID: 'adamjack',
  ...overrides
})

export const getUsersByRolesDummy = (
  overrides?: Partial<IUsersByRole>
): IUsersByRole => ({
  [IUserRole.PresetAdmin]: [],
  [IUserRole.PreviewAccess]: [],
  [IUserRole.PreviewAccessDelayed]: [],
  [IUserRole.PreviewAccessBBGOverlay]: [],
  [IUserRole.PreviewAccessBBGOverlayDelayed]: [],
  ...overrides
})

export const getPresetStateDummy = (
  overrides?: Partial<IPresetState>
): IPresetState => ({
  columnState: [
    {
      colId: 'ag-Grid-AutoColumn',
      hide: false,
      width: 100,
      flex: null,
      sort: null,
      sortIndex: null,
      pivot: false,
      pivotIndex: null,
      pinned: null,
      rowGroup: false,
      rowGroupIndex: null
    }
  ],
  filterModel: {},
  headerNames: [
    {
      colId: 'All',
      name: 'All'
    }
  ],
  expandedGroupIds: ['All'],
  ...overrides
})

export const getClibPresetToCreateDummy = (
  overrides?: Partial<ClibPresetToCreate>
): ClibPresetToCreate => ({
  groupId: IPresetsGroupId.Personal,
  name: 'My new preset',
  state: getPresetStateDummy(),
  ...overrides
})

export const getClibPresetDummy = (
  overrides?: Partial<ClibPreset>
): ClibPreset => ({
  id: '123',
  groupId: IPresetsGroupId.Personal,
  name: 'presetDummy',
  state: getPresetStateDummy(),
  order: 1,
  ...overrides
})

export const getPresetShareDummy = (
  overrides?: Partial<IPresetShare>
): IPresetShare => ({
  AccessType: IPresetShareAccessType.Write,
  Type: IPresetShareType.AZManRole,
  Names: ['Preset Admin'],
  ...overrides
})

export const getPresetDummy = (overrides?: Partial<IPreset>): IPreset => ({
  PresetName: 'preset-1',
  SharedWith: [
    getPresetShareDummy(),
    getPresetShareDummy({
      AccessType: IPresetShareAccessType.Read,
      Type: IPresetShareType.Public,
      Names: []
    })
  ],
  Data: getClibPresetDummy(),
  Id: 'defaultPresetId',
  IsDeleted: false,
  ...overrides
})
