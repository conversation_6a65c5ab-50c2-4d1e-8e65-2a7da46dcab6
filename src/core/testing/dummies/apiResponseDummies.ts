import { EXECUTION_TIME } from '@/core/redux/features/quickFilters/executionTimeFilter/constants'
import {
  IPreset,
  IPresetsGroupId,
  IEpicUserPreference,
  IUserPreferenceKey,
  IUserPreferences
} from '@/core/services/epic'
import {
  ColumnType,
  IDistinctColumnValues,
  IDistinctColumnValuesResponse,
  IGetReportDetailsResponse,
  ILatestAsOfResponse,
  IMetaDataResponse,
  IPreProcessingParamName,
  ISubsetResponse,
  ISummaryHeadlineValues,
  ISummaryResponse,
  PreProcessingParamSpec,
  PreProcessingParamValueSpec
} from '@/core/services/riskReport'
import {
  columnsDummy,
  getClibPresetDummy,
  getColumnInfoDummy,
  getPresetDummy
} from '@/core/testing/dummies'

export const getDistinctColumnValuesResponseDummy = (
  overrides?: IDistinctColumnValues
): IDistinctColumnValuesResponse => ({
  distinctColumnValues: {
    TradingArea: ['ARNO', 'BLUM'],
    ...overrides
  }
})

export const getLatestAsOfUtcResponseDummy = (
  overrides?: Partial<ILatestAsOfResponse>
): ILatestAsOfResponse => ({
  latestAsOfUtc: '2023-08-04T07:33:13.6298249Z',
  ...overrides
})

export const getPreProcessingParamValueSpecDummy = (
  overrides?: Partial<PreProcessingParamValueSpec>
): PreProcessingParamValueSpec => ({
  value: 'None',
  displayValue: 'Show',
  shortDescription: 'Show Repos as individual TickerNet positions',
  ...overrides
})

export const getPreProcessingParamSpecsDummy = (
  overrides?: Partial<PreProcessingParamSpec>
): PreProcessingParamSpec => ({
  paramName: IPreProcessingParamName.RepoNetting,
  displayLabel: 'Repos',
  paramValues: [
    getPreProcessingParamValueSpecDummy({
      value: 'None'
    }),
    getPreProcessingParamValueSpecDummy({
      value: 'CollapseIntoSingleTicker'
    }),
    getPreProcessingParamValueSpecDummy({
      value: 'MergeWithBondTicker'
    })
  ],
  ...overrides
})

export const getReportDetailsResponseDummy = (
  overrides?: Partial<IGetReportDetailsResponse>
): IGetReportDetailsResponse => ({
  columnInfo: [
    getColumnInfoDummy({ column: 'All' }),
    getColumnInfoDummy({ column: 'TradingArea' }),
    getColumnInfoDummy({ column: 'Team' })
  ],
  preProcessingParamSpecs: [getPreProcessingParamSpecsDummy()],
  reportType: 'PnlLive',
  ...overrides
})

export const getSubsetResponseDummy = (): ISubsetResponse => ({
  data: {
    WellKnownType: 'Table',
    Columns: [
      {
        Name: 'Team',
        Type: ColumnType.String,
        Count: 2,
        ContainsNulls: false,
        Values: ['ARNO', 'ARNO']
      },
      {
        Name: 'TradingArea',
        Type: ColumnType.String,
        Count: 2,
        ContainsNulls: false,
        Values: ['ARNO', 'ESPO']
      },
      {
        Name: EXECUTION_TIME,
        Type: ColumnType.String,
        Count: 2,
        ContainsNulls: false,
        Values: ['2024-08-16T13:27:15.0000000Z', '2024-08-17T10:00:10.0000000Z']
      }
    ]
  },
  totalRowCount: 2
})

export const getMetadataResponseDummy = (
  overrides?: Partial<IMetaDataResponse>
): IMetaDataResponse => ({
  metadata: {
    'Execution Time': '2023-08-04T07:35:08.3138212Z',
    'Market Data': '2023-08-04T07:35:12.5621651Z',
    Trades: '2023-08-04T07:33:30.4063813Z'
  },
  displayMetadata: ['Execution Time', 'Market Data', 'Trades'],
  ...overrides
})
export const subsetDummy: ISubsetResponse = {
  data: {
    WellKnownType: 'Table',
    Columns: columnsDummy
  },
  totalRowCount: 25531
}

export const getSummaryResponseDummy = (
  overrides?: ISummaryHeadlineValues
): ISummaryResponse => ({
  headlineValues: overrides || {
    BasisRisk: -1554270.06025743,
    FxDeltaUSD: 882287927.9570632,
    'Eq Delta': -46292869.85141079
  }
})

export const getUserPreferenceResponseDummy = (
  overrides?: Partial<IEpicUserPreference<IUserPreferences>>
): IEpicUserPreference<IUserPreferences> => ({
  Id: '123',
  CreatedDate: '2022-01-01',
  IsDeleted: false,
  UserId: '456',
  Key: 'example',
  ModifiedDate: '2022-01-01',
  ModifiedBy: 'John Doe',
  CreatedBy: 'Jane Doe',
  Data: {
    [IUserPreferenceKey.DefaultPresetId]: 'default-preset-id',
    isSummaryBarCollapsed: true
  },
  ...overrides
})

export const saveUserPreferenceResponseDummy = getUserPreferenceResponseDummy

export const getAllPresetsResponseDummy = (): IPreset[] => [
  getPresetDummy(),
  getPresetDummy({
    Id: '6527cf59540638ed0b7bc5bf',
    PresetName: 'preset-2',
    SharedWith: null,
    Data: getClibPresetDummy({
      id: '6527cf59540638ed0b7bc5bf',
      groupId: IPresetsGroupId.Personal
    })
  })
]
