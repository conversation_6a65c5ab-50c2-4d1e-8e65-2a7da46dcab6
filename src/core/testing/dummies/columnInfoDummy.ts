import { ColumnType, IColumnInfo } from '@/core/services/riskReport'

export const getColumnInfoDummy = (
  overrides?: Partial<IColumnInfo>
): IColumnInfo => {
  return {
    name: 'All',
    column: 'All',
    aggregationOperations: ['UniqueValue'],
    canGroupBy: true,
    type: 'String' as ColumnType,
    filterType: 'TextSet',
    shortDescription: undefined,
    isPrimaryKey: true,
    isSystemData: false,
    canPivotBy: false,
    ...overrides
  }
}
