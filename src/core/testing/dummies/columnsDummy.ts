import {
  IColumn,
  IFilter,
  IFilterModel,
  FilterOperators
} from '@/core/services/riskReport'

export const columnsDummy: IColumn[] = [
  {
    Name: 'All',
    Type: 'String',
    Count: 4,
    ContainsNulls: false,
    Values: ['All', 'All', 'All', 'All']
  },
  {
    Name: 'Team',
    Type: 'String',
    Count: 4,
    ContainsNulls: false,
    Values: ['ARNO', 'ARNO', 'ARNO', 'ARNO']
  },
  {
    Name: 'TradingArea',
    Type: 'String',
    Count: 4,
    ContainsNulls: false,
    Values: ['ARNO', 'ARNO', 'ARNO', 'ARNO']
  },
  {
    Name: 'Strategy',
    Type: 'String',
    Count: 4,
    ContainsNulls: false,
    Values: ['AUDUSD', 'ClosedStrategies', 'GBPUSD', 'USDJPY']
  }
]

export const getFilterDummy = (overrides?: Partial<IFilter>): IFilter => ({
  operator: FilterOperators.In,
  value: 'ARNO',
  ...overrides
})

export const getFilterModelDummy = (
  overrides?: Partial<IFilterModel>
): IFilterModel => ({
  column: 'Team',
  logicalOperator: 'Or',
  filters: [getFilterDummy()],
  ...overrides
})
