import { vi } from 'vitest'
import { getRowId } from './getRowId'

describe('getRowId', () => {
  beforeEach(() => {
    vi.resetAllMocks()
  })

  const columnApiMock = {
    getRowGroupColumns: vi.fn()
  }
  const getParamsMock = (rowData: unknown) =>
    ({
      parentKeys: ['All'],
      data: rowData,
      columnApi: columnApiMock
    } as any)

  const params = getParamsMock({
    All: 'All',
    ProfitLossTradingUSD: 0,
    AdjustedProfitLossDayToDayUSD: -688.2352124050376,
    FxCross: null,
    FxCrossDelta: true
  })

  it('should throw an error when keyColumns are empty and no grouping is defined', () => {
    // given
    const keyColumns: string[] = []

    // then
    expect(() => getRowId(params, keyColumns)).toThrow(Error)
  })

  it('should return a slash-separated string of encoded key values', () => {
    // given
    const keyColumns = [
      'All',
      'ProfitLossTradingUSD',
      'AdjustedProfitLossDayToDayUSD',
      'FxCross',
      'FxCrossDelta'
    ]

    // when
    const result = getRowId(params, keyColumns)

    // then
    expect(result).toEqual('All-All/0/-688.2352124050376/null/true')
  })

  it('should return rowId for group row, level 2', () => {
    const params = {
      ...getParamsMock({
        col1: 'colVal1',
        col2: 'colVal2',
        col3: 'colVal3',
        col4: 'colVal4',
        col5: 'colVal5'
      }),
      parentKeys: ['colVal1', 'colVal2'],
      level: 2
    }

    columnApiMock.getRowGroupColumns.mockReturnValue([
      { getColId: () => 'col1' },
      { getColId: () => 'col2' },
      { getColId: () => 'col3' }
    ])

    const result = getRowId(params, [])

    expect(result).toEqual('colVal1/colVal2/colVal3')
  })

  it('should return rowId for group row, level 1', () => {
    const params = {
      ...getParamsMock({
        col1: 'colVal1',
        col2: 'colVal2',
        col3: 'colVal3',
        col4: 'colVal4',
        col5: 'colVal5'
      }),
      parentKeys: ['colVal1'],
      level: 1
    }

    columnApiMock.getRowGroupColumns.mockReturnValue([
      { getColId: () => 'col1' },
      { getColId: () => 'col2' },
      { getColId: () => 'col3' }
    ])

    const result = getRowId(params, [])

    expect(result).toEqual('colVal1/colVal2')
  })

  it('should return rowId for non-group row for level 0 when no grouping is defined', () => {
    const params = {
      ...getParamsMock({
        col1: 'colVal1',
        col2: 'colVal2',
        col3: 'colVal3',
        col4: 'colVal4',
        col5: 'colVal5'
      }),
      parentKeys: undefined,
      level: 0
    }

    const result = getRowId(params, ['col1', 'col2'])

    expect(result).toEqual('-colVal1/colVal2')
  })

  it('should return rowId for non-group row for fully expanded grouping level', () => {
    const params = {
      ...getParamsMock({
        col1: 'colVal1',
        col2: 'colVal2',
        col3: 'colVal3',
        col4: 'colVal4',
        col5: 'colVal5'
      }),
      parentKeys: ['parentKey1', 'parentKey2', 'parentKey3', 'parentKey4'],
      level: 4
    }

    const result = getRowId(params, ['col1', 'col2'])

    expect(result).toEqual(
      'parentKey1/parentKey2/parentKey3/parentKey4-colVal1/colVal2'
    )
  })

  it('should clear the rowId from special characters', () => {
    const params = {
      ...getParamsMock({
        col1: 'colVal1@',
        col2: 'colVal2/',
        col3: 'colVal3/',
        col4: 'colVal4',
        col5: 'colVal5'
      }),
      parentKeys: ['parentKey@'],
      level: 0
    }

    const result = getRowId(params, ['col1', 'col2'])

    expect(result).toEqual('parentKey%40-colVal1%40/colVal2%2F')
  })

  it('should return rowId for group row when level is 0', () => {
    const rowData = {
      col1: 'colVal1',
      col2: 'colVal2',
      col3: 'colVal3',
      col4: 'colVal4',
      col5: 'colVal5'
    }

    columnApiMock.getRowGroupColumns.mockReturnValue([
      { getColId: () => 'col1' },
      { getColId: () => 'col2' },
      { getColId: () => 'col3' }
    ])

    const getRowIdParams = {
      data: rowData,
      columnApi: columnApiMock,
      level: 0
    } as any

    const result = getRowId(getRowIdParams, ['col1', 'col2'])

    expect(result).toEqual('colVal1')
  })

  it('should throw an error when the theColumnKey is undefined', () => {
    const rowData = {
      col1: 'colVal1',
      col2: 'colVal2',
      col3: 'colVal3',
      col4: 'colVal4',
      col5: 'colVal5'
    }

    columnApiMock.getRowGroupColumns.mockReturnValue([
      { getColId: () => 'col1' },
      { getColId: () => undefined },
      { getColId: () => 'col3' }
    ])

    const getRowIdParams = {
      data: rowData,
      columnApi: columnApiMock,
      level: 1
    } as any
    const onErrorCallbackMock = vi.fn()

    expect(() =>
      getRowId(getRowIdParams, ['col2', 'col3'], onErrorCallbackMock)
    ).toThrowErrorMatchingSnapshot()

    expect(onErrorCallbackMock).toHaveBeenCalledWith(
      `Cannot accurately determine row identifier due to missing column id on level 1 for parent group undefined. Please contact M72 Support to investigate.`
    )
  })

  it('should add null into rowId', () => {
    const params = {
      ...getParamsMock({
        col1: 'colVal1',
        col2: null,
        col3: '',
        col4: 'colVal4',
        col5: 'colVal5'
      }),
      parentKeys: ['parentKey1', null, 'parentKey3'],
      level: 3
    }

    const result = getRowId(params, ['col1', 'col2', 'col3'])

    expect(result).toEqual('parentKey1/null/parentKey3-colVal1/null/')
  })
})
