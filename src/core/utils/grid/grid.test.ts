import {
  ColumnType,
  IColumn,
  IColumnInfo,
  ISummaryHeadlineValues
} from '@/core/services/riskReport'
import { columnsDummy, getColumnInfoDummy } from '@/core/testing/dummies'
import {
  addOpenByDefaultPropertyToChildren,
  columnDefFromColumInfo,
  filterOutEmptyColumnsVO,
  formatToDate,
  formatToLocalDateAndTime,
  formatToNumber,
  formatValue,
  getCachedBlockCount,
  getCachedBlockStatusSummary,
  getColumnDefinitionsOrderedByEnabledFilters,
  getColumnTooltipContent,
  getFilter,
  getGroupId,
  getKeyColumnNames,
  getNumericCellClassRules,
  getPivotColumnsOrdering,
  getRowRange,
  getTextSetColumnNames,
  isNumericColumn,
  isRowGroupOpenByDefault,
  mapColumnInfoToColumnDefs,
  mapColumnsToTableRows,
  mapColumnsVOToColumnsAggregation,
  mapColumnsVOToGroupingColumns,
  mapColumnToCellDataType,
  mapGridSortModelToApiSortModel,
  mapHeadlineValuesToRiskMetrics,
  resetColumnOrder,
  resetColumnState,
  shouldContentBeAlignedToRight,
  tooltipValueFormat,
  updateColumnsHeaderNames,
  updateFilterSidePanel,
  updateFiltersToolPanelLabelTextAndColour,
  updateFiltersWithinFiltersToolPanelWhenEnabled
} from '@/core/utils/grid'
import {
  CellClassParams,
  ColDef,
  ColGroupDef,
  ColumnApi,
  ColumnState,
  ColumnVO,
  GridApi,
  IFiltersToolPanel,
  SortModelItem
} from '@ag-grid-community/core'
import { ColumnsHeaderNames } from '@/core/redux/features/report'
import { DateTimeFilter } from '@/components/Grid/Filters/DateTimeFilter'
import { vi } from 'vitest'
import { columnDefs } from '@/core/testing/mocks'

describe('utils/grid', () => {
  describe('getNumericCellClassRules', () => {
    it('should return a class rule object for negative numeric cell values', () => {
      // given
      const params = { value: -1 } as CellClassParams

      // when
      const result = getNumericCellClassRules()

      // then
      expect(result.negative(params)).toBeTruthy()
      expect(result.positive(params)).toBeFalsy()
    })

    it('should return a class rule object for positive numeric cell values', () => {
      // given
      const params = { value: 1 } as CellClassParams

      // when
      const result = getNumericCellClassRules()

      // then
      expect(result.negative(params)).toBeFalsy()
      expect(result.positive(params)).toBeTruthy()
    })

    it('should return a class rule object for zero numeric cell values', () => {
      // given
      const params = { value: 0 } as CellClassParams

      // when
      const result = getNumericCellClassRules()

      // then
      expect(result.negative(params)).toBeFalsy()
      expect(result.positive(params)).toBeTruthy()
    })
  })

  describe('isNumericColumn', () => {
    it.each`
      columnType    | expectedResult
      ${'Date'}     | ${false}
      ${'DateTime'} | ${false}
      ${'Double'}   | ${true}
      ${'Boolean'}  | ${false}
      ${null}       | ${false}
      ${'String'}   | ${false}
    `(
      'should return $expectedResult for column type $columnType',
      ({ columnType, expectedResult }) => {
        expect(isNumericColumn(columnType)).toBe(expectedResult)
      }
    )
  })

  describe('shouldContentBeAlignedToRight', () => {
    it.each`
      columnType    | expectedResult
      ${'Date'}     | ${true}
      ${'DateTime'} | ${true}
      ${'Double'}   | ${true}
      ${'Boolean'}  | ${false}
      ${'String'}   | ${false}
      ${null}       | ${false}
    `(
      'should return $expectedResult if the column type is $columnType',
      ({ columnType, expectedResult }) => {
        const result = shouldContentBeAlignedToRight(columnType)
        expect(result).toBe(expectedResult)
      }
    )
  })

  describe('formatToDate', () => {
    it('should format date value to locale string', () => {
      // given
      const value = '2022-01-01T00:00:00.000Z'

      // when
      const result = formatToDate(value)

      // then
      expect(result).toEqual('1/1/2022')
    })
  })

  describe('formatToLocalDateAndTime', () => {
    it('should format date and time to locale string', () => {
      // given
      const value = '2021-11-08T14:25:00.000Z'

      // when
      const result = formatToLocalDateAndTime(value)

      // then
      expect(result).toEqual('11/8/2021 3:25:00 PM')
    })
  })

  describe('formatToNumber', () => {
    it('should format positive numbers correctly', () => {
      // given
      const value = 1000
      const numberFormat = { decimalDigits: 2, multiplier: 1.23, prefix: '$' }

      // when
      const result = formatToNumber(value, numberFormat)

      // then
      expect(result).toBe('$1,230.00')
    })

    it('should format negative numbers correctly', () => {
      // given
      const value = -1000
      const numberFormat = { decimalDigits: 0, multiplier: 0.01, suffix: '%' }

      // when
      const result = formatToNumber(value, numberFormat)

      // then
      expect(result).toBe('(10)%')
    })

    it('should handle string inputs', () => {
      // given
      const value = '1234.5678'
      const numberFormat = { decimalDigits: 3, multiplier: 0.1 }

      // when
      const result = formatToNumber(value, numberFormat)

      // then
      expect(result).toBe('123.457')
    })

    it('should handle missing prefix and suffix', () => {
      // given
      const value = 500
      const numberFormat = { decimalDigits: 0, multiplier: 1 }

      // when
      const result = formatToNumber(value, numberFormat)

      // then
      expect(result).toBe('500')
    })

    it('should apply the multiplier correctly', () => {
      //given
      const value = 10
      const numberFormat = { decimalDigits: 2, multiplier: 0.25 }

      // when
      const result = formatToNumber(value, numberFormat)

      // then
      expect(result).toBe('2.50')
    })
  })

  describe('formatValue', () => {
    describe('when columnType is Date', () => {
      it.each`
        value                              | expected
        ${null}                            | ${''}
        ${'2021-12-31'}                    | ${'12/31/2021'}
        ${'Fri, 27 Nov 2021 22:00:00 GMT'} | ${'11/27/2021'}
      `('should format $value to $expected', ({ value, expected }) => {
        // when
        const result = formatValue(value, ColumnType.Date)

        // then
        expect(result).toBe(expected)
      })
    })

    describe('when columnType is DateTime', () => {
      it.each`
        value                              | expected
        ${'2022-01-01T09:00:00.000Z'}      | ${'1/1/2022 10:00:00 AM'}
        ${'Sat, 01 Jan 2022 14:30:00 GMT'} | ${'1/1/2022 3:30:00 PM'}
      `('should format $value to $expected', ({ value, expected }) => {
        // when
        const result = formatToLocalDateAndTime(value)

        // then
        expect(result).toEqual(expected)
      })
    })

    describe('when columnType is Double', () => {
      it.each`
        value           | columnType  | numberFormat                                           | expected
        ${null}         | ${'Double'} | ${{ decimalDigits: 2, multiplier: 0.01 }}              | ${''}
        ${123.456}      | ${'Double'} | ${{ decimalDigits: 2, multiplier: 0.01, prefix: '$' }} | ${'$1.23'}
        ${-1000000.123} | ${'Double'} | ${{ decimalDigits: 0, multiplier: 0.01, suffix: '%' }} | ${'(10,000)%'}
        ${0}            | ${'Double'} | ${undefined}                                           | ${'0'}
        ${123.456}      | ${'Double'} | ${undefined}                                           | ${'123.456'}
        ${1230000}      | ${'Double'} | ${undefined}                                           | ${'1230000'}
      `(
        'should return $expected when value=$value and columnType=$columnType and numberFormat=$numberFormat',
        ({ value, columnType, numberFormat, expected }) => {
          const result = formatValue(value, columnType, numberFormat)

          expect(result).toBe(expected)
        }
      )
    })

    describe('when columnType is String', () => {
      it.each`
        value            | expected
        ${null}          | ${''}
        ${'some string'} | ${'some string'}
        ${123}           | ${'123'}
      `('should return $expected', ({ value, expected }) => {
        // when
        const result = formatValue(value, ColumnType.String)

        // then
        expect(result).toBe(expected)
      })
    })
  })

  describe('getFilter', () => {
    it.each`
      filterType   | type                   | expectedFilter
      ${'Text'}    | ${ColumnType.String}   | ${'agTextColumnFilter'}
      ${'TextSet'} | ${ColumnType.String}   | ${'agSetColumnFilter'}
      ${'Date'}    | ${ColumnType.DateTime} | ${DateTimeFilter}
      ${'Date'}    | ${ColumnType.Date}     | ${DateTimeFilter}
      ${'Number'}  | ${ColumnType.Double}   | ${'agNumberColumnFilter'}
    `(
      'should return $expectedFilter for filter type $filterType',
      ({ filterType, type, expectedFilter }) => {
        // when
        const result = getFilter({ filterType, type } as IColumnInfo)

        // then
        expect(result).toBe(expectedFilter)
      }
    )
  })

  describe('mapColumnToCellDataType', () => {
    it.each`
      columnType    | expected
      ${'Boolean'}  | ${'text'}
      ${'String'}   | ${'text'}
      ${'Date'}     | ${'dateString'}
      ${'DateTime'} | ${'dateString'}
      ${'Double'}   | ${'number'}
      ${null}       | ${undefined}
    `('should map $columnType to $expected', ({ columnType, expected }) => {
      // when
      const result = mapColumnToCellDataType(columnType)

      // then
      expect(result).toEqual(expected)
    })
  })

  describe('mapColumnInfoToColumnDefs', () => {
    it('should map column info array to column defs', () => {
      // given
      const columnInfo = [
        getColumnInfoDummy({
          name: 'All',
          column: 'All',
          aggregationOperations: ['UniqueValue'],
          canGroupBy: true,
          type: ColumnType.String
        }),
        getColumnInfoDummy({
          name: 'Team',
          column: 'Team',
          aggregationOperations: ['Sum'],
          canGroupBy: true,
          type: ColumnType.Date
        }),
        getColumnInfoDummy({
          name: 'Trading Area',
          column: 'TradingArea',
          aggregationOperations: ['UniqueValue'],
          canGroupBy: false,
          type: ColumnType.Double
        })
      ]

      // when
      const result = mapColumnInfoToColumnDefs({ columnInfo })

      // then
      expect(result).toMatchObject([
        {
          headerName: 'All',
          field: 'All',
          aggFunc: 'UniqueValue',
          enableRowGroup: true,
          refData: {
            type: ColumnType.String
          }
        },
        {
          headerName: 'Team',
          field: 'Team',
          aggFunc: 'Sum',
          enableRowGroup: true,
          refData: {
            type: ColumnType.Date
          }
        },
        {
          headerName: 'Trading Area',
          field: 'TradingArea',
          aggFunc: 'UniqueValue',
          enableRowGroup: false,
          refData: {
            type: ColumnType.Double
          }
        }
      ])
    })

    it('should filter out all columns marked as system data', () => {
      // given
      const columnInfo = [
        getColumnInfoDummy({
          column: 'All',
          isSystemData: false
        }),
        getColumnInfoDummy({
          column: 'Team',
          isSystemData: false
        }),
        getColumnInfoDummy({
          column: 'TradingArea',
          isSystemData: true
        })
      ]

      // when
      const result = mapColumnInfoToColumnDefs({ columnInfo })

      // then
      expect(result).toHaveLength(2)
      expect(result).toMatchObject([
        {
          field: 'All'
        },
        {
          field: 'Team'
        }
      ])
    })

    it('should add cell class rules to numeric cells', () => {
      // given
      const columnInfo = [
        getColumnInfoDummy({
          type: ColumnType.String
        }),
        getColumnInfoDummy({
          type: ColumnType.Double
        }),
        getColumnInfoDummy({
          type: ColumnType.Boolean
        })
      ]

      // when
      const [column1, column2, column3] = mapColumnInfoToColumnDefs({
        columnInfo
      })

      // then
      expect(column1).not.toHaveProperty('cellClassRules')
      expect(column2.cellClassRules).toHaveProperty('positive')
      expect(column2.cellClassRules).toHaveProperty('negative')
      expect(column3).not.toHaveProperty('cellClassRules')
    })

    it('should add the class "align-right" to cells in columns with a type of date, date-time, or double', () => {
      // given
      const columnInfo = [
        getColumnInfoDummy({
          type: ColumnType.String
        }),
        getColumnInfoDummy({
          type: ColumnType.Double
        }),
        getColumnInfoDummy({
          type: ColumnType.DateTime
        }),
        getColumnInfoDummy({
          type: ColumnType.Date
        }),
        getColumnInfoDummy({
          type: ColumnType.Boolean
        })
      ]

      // when
      const [column1, column2, column3, column4, column5] =
        mapColumnInfoToColumnDefs({ columnInfo })

      // then
      expect(column1).not.toHaveProperty('cellClass')
      expect(column2).toHaveProperty('cellClass', 'align-right')
      expect(column3).toHaveProperty('cellClass', 'align-right')
      expect(column4).toHaveProperty('cellClass', 'align-right')
      expect(column5).not.toHaveProperty('cellClass')
    })

    describe('column header tooltip', () => {
      it('should set tooltip based on the aggregation operation', () => {
        // given
        const columnInfo = [
          getColumnInfoDummy({
            aggregationOperations: ['UniqueValue']
          }),
          getColumnInfoDummy({
            aggregationOperations: ['Sum']
          })
        ]

        // when
        const [column1, column2] = mapColumnInfoToColumnDefs({ columnInfo })

        // then
        expect(column1).toHaveProperty(
          'headerTooltip',
          'Aggregation: UniqueValue'
        )
        expect(column2).toHaveProperty('headerTooltip', 'Aggregation: Sum')
      })

      it('should set "Aggregation: none" when there is no aggregation for a column', () => {
        // given
        const columnInfo = [
          getColumnInfoDummy({
            aggregationOperations: undefined
          })
        ]

        // when
        const [column] = mapColumnInfoToColumnDefs({ columnInfo })

        // then
        expect(column).toHaveProperty('headerTooltip', 'Aggregation: none')
      })

      it('should prepend short description when a column contains this property', () => {
        // given
        const columnInfo = [
          getColumnInfoDummy({
            aggregationOperations: undefined,
            shortDescription: 'Yearly PnL for fiscal year starting November 1st'
          })
        ]

        // when
        const [column] = mapColumnInfoToColumnDefs({ columnInfo })

        // then
        expect(column).toHaveProperty(
          'headerTooltip',
          `Yearly PnL for fiscal year starting November 1st\n\nAggregation: none`
        )
      })
    })

    it('should set the correct filter type', () => {
      // given
      const columnInfo = [
        getColumnInfoDummy({
          filterType: 'TextSet'
        }),
        getColumnInfoDummy({
          filterType: 'Date'
        }),
        getColumnInfoDummy({
          filterType: 'Number'
        }),
        getColumnInfoDummy({
          filterType: 'Text'
        })
      ]

      // when
      const [column1, column2, column3, column4] = mapColumnInfoToColumnDefs({
        columnInfo
      })

      // then
      expect(column1).toHaveProperty('filter', 'agSetColumnFilter')
      expect(column2).toHaveProperty('filter', expect.any(Object))
      expect(column3).toHaveProperty('filter', 'agNumberColumnFilter')
      expect(column4).toHaveProperty('filter', 'agTextColumnFilter')
    })

    it('should set hide = false for all columns if presetColumnsState is not provided', () => {
      const result = mapColumnInfoToColumnDefs({
        columnInfo: [getColumnInfoDummy(), getColumnInfoDummy()]
      })

      expect(result).toHaveLength(2)
      result.forEach((colDef) => {
        expect(colDef.hide).toBe(false)
      })
    })

    it('should set hide = true if the column field is NOT in presetColumnsState', () => {
      // presetColumnsState has only 'colA'; 'colB' should end up hidden
      const presetColumnsState = [{ colId: 'colA' }] as ColumnState[]

      const result = mapColumnInfoToColumnDefs({
        columnInfo: [
          getColumnInfoDummy({ column: 'colA' }),
          getColumnInfoDummy({ column: 'colB' })
        ],
        presetColumnsState
      })

      expect(result).toHaveLength(2)
      // Check each column definition for hide property
      const colADef = result.find((colDef) => colDef.field === 'colA')
      const colBDef = result.find((colDef) => colDef.field === 'colB')

      expect(colADef?.hide).toBe(false)
      expect(colBDef?.hide).toBe(true)
    })

    it('should set hide = false if the column field IS in presetColumnsState', () => {
      // presetColumnsState includes both columns, so neither should be hidden
      const presetColumnsState = [
        { colId: 'colA' },
        { colId: 'colB' }
      ] as ColumnState[]

      const result = mapColumnInfoToColumnDefs({
        columnInfo: [
          getColumnInfoDummy({ column: 'colA' }),
          getColumnInfoDummy({ column: 'colB' })
        ],
        presetColumnsState
      })

      expect(result).toHaveLength(2)
      result.forEach((colDef) => {
        expect(colDef.hide).toBe(false)
      })
    })

    it('should handle empty presetColumnsState by hiding all columns', () => {
      // An empty array means no columns match, so they should all be hidden
      const presetColumnsState: ColumnState[] = []

      const result = mapColumnInfoToColumnDefs({
        columnInfo: [getColumnInfoDummy(), getColumnInfoDummy()],
        presetColumnsState
      })

      expect(result).toHaveLength(2)
      result.forEach((colDef) => {
        expect(colDef.hide).toBe(true)
      })
    })

    it('should NOT hide if the column field IS in presetColumnsState and hide is false', () => {
      // presetColumnsState includes both columns, but hide is false for both
      const presetColumnsState = [
        { colId: 'colA', hide: false },
        { colId: 'colB', hide: false }
      ] as ColumnState[]

      const result = mapColumnInfoToColumnDefs({
        columnInfo: [
          getColumnInfoDummy({ column: 'colA' }),
          getColumnInfoDummy({ column: 'colB' })
        ],
        presetColumnsState
      })

      expect(result).toHaveLength(2)
      result.forEach((colDef) => {
        expect(colDef.hide).toBe(false)
      })
    })

    it('should hide if the column field IS in presetColumnsState and hide is true', () => {
      // presetColumnsState includes both columns, and hide is true for both
      const presetColumnsState = [
        { colId: 'colA', hide: true },
        { colId: 'colB', hide: true }
      ] as ColumnState[]

      const result = mapColumnInfoToColumnDefs({
        columnInfo: [
          getColumnInfoDummy({ column: 'colA' }),
          getColumnInfoDummy({ column: 'colB' })
        ],
        presetColumnsState
      })

      expect(result).toHaveLength(2)
      result.forEach((colDef) => {
        expect(colDef.hide).toBe(true)
      })
    })

    it('should NOT hide if resetColumns = true', () => {
      // presetColumnsState includes both columns, and hide is true for both
      const presetColumnsState = [
        { colId: 'colA', hide: true },
        { colId: 'colB', hide: true }
      ] as ColumnState[]

      const result = mapColumnInfoToColumnDefs({
        columnInfo: [
          getColumnInfoDummy({ column: 'colA' }),
          getColumnInfoDummy({ column: 'colB' })
        ],
        presetColumnsState,
        resetColumns: true
      })

      expect(result).toHaveLength(2)
      result.forEach((colDef) => {
        expect(colDef.hide).toBe(false)
      })
    })

    it('should handle columns with children correctly', () => {
      // given
      const columnInfo = [
        getColumnInfoDummy({
          name: 'Parent Column',
          column: 'parentColumn',
          children: [
            getColumnInfoDummy({
              name: 'Child Column 1',
              column: 'childColumn1',
              type: ColumnType.String
            }),
            getColumnInfoDummy({
              name: 'Child Column 2',
              column: 'childColumn2',
              type: ColumnType.Double
            })
          ]
        })
      ]

      // when
      const result = mapColumnInfoToColumnDefs({ columnInfo })

      // then
      expect(result).toHaveLength(1)
      expect(result[0]).toHaveProperty('headerName', 'Parent Column')
      expect(result[0]).toHaveProperty('children')
      expect(result[0].children).toHaveLength(3)
      expect(result[0].children[0]).toHaveProperty(
        'headerName',
        'Child Column 1'
      )
      expect(result[0].children[1]).toHaveProperty(
        'headerName',
        'Child Column 2'
      )
    })

    it('should set columnGroupShow to "open" for parent columns', () => {
      // given
      const columnInfo = [
        getColumnInfoDummy({
          name: 'Parent Column',
          column: 'parentColumn',
          children: [
            getColumnInfoDummy({
              name: 'Child Column 1',
              column: 'childColumn1',
              type: ColumnType.String
            })
          ]
        })
      ]

      // when
      const result = mapColumnInfoToColumnDefs({ columnInfo })

      // then
      expect(result[0]).toHaveProperty('columnGroupShow', 'open')
    })

    it('should recursively map children columns', () => {
      // given
      const columnInfo = [
        getColumnInfoDummy({
          name: 'Parent Column',
          column: 'parentColumn',
          children: [
            getColumnInfoDummy({
              name: 'Child Column 1',
              column: 'childColumn1',
              children: [
                getColumnInfoDummy({
                  name: 'Grandchild Column',
                  column: 'grandchildColumn',
                  type: ColumnType.Date
                })
              ]
            })
          ]
        })
      ]

      // when
      const result = mapColumnInfoToColumnDefs({ columnInfo })

      // then
      expect(result[0].children[0]).toHaveProperty('children')
      expect(result[0].children[0].children[0]).toHaveProperty(
        'headerName',
        'Grandchild Column'
      )
    })

    it('should handle columns with no children gracefully', () => {
      // given
      const columnInfo = [
        getColumnInfoDummy({
          name: 'Single Column',
          column: 'singleColumn',
          type: ColumnType.String
        })
      ]

      // when
      const result = mapColumnInfoToColumnDefs({ columnInfo })

      // then
      expect(result).toHaveLength(1)
      expect(result[0]).toHaveProperty('headerName', 'Single Column')
      expect(result[0]).not.toHaveProperty('children')
    })
  })

  describe('updateColumnsHeaderNames', () => {
    it('should update column header names based on the provided header names', () => {
      // given
      const colDefs: ColDef[] = [
        { field: '1', headerName: 'Name' },
        { field: '2', headerName: 'Age' }
      ]
      const headersNames: ColumnsHeaderNames = {
        '1': 'First Name',
        '2': 'User Age'
      }

      // when
      const result = updateColumnsHeaderNames(colDefs, headersNames)

      // then
      expect(result).toEqual([
        { field: '1', headerName: 'First Name' },
        { field: '2', headerName: 'User Age' }
      ])
    })

    it('should retain original header name if no corresponding header name is provided', () => {
      // given
      const colDefs: ColDef[] = [
        { field: '1', headerName: 'Name' },
        { field: '2', headerName: 'Age' }
      ]
      const headersNames: ColumnsHeaderNames = {
        '1': 'First Name'
      }

      // when
      const result = updateColumnsHeaderNames(colDefs, headersNames)

      // then
      expect(result).toEqual([
        { field: '1', headerName: 'First Name' },
        { field: '2', headerName: 'Age' }
      ])
    })
  })

  describe('mapColumnsToTableRows', () => {
    it('should map columns to table rows', () => {
      // given
      const columns: IColumn[] = columnsDummy

      // when
      const result = mapColumnsToTableRows(columns)

      // then
      expect(result).toStrictEqual([
        {
          All: 'All',
          Team: 'ARNO',
          TradingArea: 'ARNO',
          Strategy: 'AUDUSD'
        },
        {
          All: 'All',
          Team: 'ARNO',
          TradingArea: 'ARNO',
          Strategy: 'ClosedStrategies'
        },
        {
          All: 'All',
          Team: 'ARNO',
          TradingArea: 'ARNO',
          Strategy: 'GBPUSD'
        },
        {
          All: 'All',
          Team: 'ARNO',
          TradingArea: 'ARNO',
          Strategy: 'USDJPY'
        }
      ])
    })
  })

  describe('getRowRange', () => {
    it('should return a RowRange object with the expected properties', () => {
      // given
      const startRow = 5
      const endRow = 10

      // when
      const result = getRowRange(startRow, endRow)

      // then
      expect(result).toEqual({
        rowOffset: 5,
        rowCount: 6
      })
    })

    it('should return a RowRange object with rowOffset = 0 when startRow is not specified', () => {
      // given
      const endRow = 5

      // when
      const result = getRowRange(undefined, endRow)

      // then
      expect(result.rowOffset).toBe(0)
    })

    it('should return a RowRange object with the expected rowCount when startRow and endRow are specified', () => {
      // given
      const startRow = 0
      const endRow = 5

      // when
      const result = getRowRange(startRow, endRow)

      // then
      expect(result.rowCount).toBe(6)
    })

    it('should return a RowRange object with rowCount = 1 when startRow and endRow are equal', () => {
      // given
      const startRow = 3
      const endRow = 3

      // when
      const result = getRowRange(startRow, endRow)

      // then
      expect(result.rowCount).toBe(1)
    })
  })

  describe('filterOutEmptyColumnsVO', () => {
    it('should filter out empty columns from the array', () => {
      // given
      const columnsVO: ColumnVO[] = [
        {
          id: 'All',
          aggFunc: 'UniqueValue',
          displayName: 'All',
          field: 'All'
        },
        {
          id: 'Team',
          aggFunc: 'UniqueValue',
          displayName: 'Team'
        },
        {
          id: 'TradingArea',
          displayName: 'Trading Area',
          field: 'TradingArea'
        },
        {
          id: 'Strategy',
          aggFunc: 'UniqueValue',
          displayName: 'Strategy',
          field: 'Strategy'
        }
      ]

      // when
      const result = filterOutEmptyColumnsVO(columnsVO)

      // then
      expect(result).toStrictEqual([
        {
          id: 'All',
          aggFunc: 'UniqueValue',
          displayName: 'All',
          field: 'All'
        },
        {
          id: 'Strategy',
          aggFunc: 'UniqueValue',
          displayName: 'Strategy',
          field: 'Strategy'
        }
      ])
    })
  })

  describe('mapColumnsVOToColumnsAggregation', () => {
    it('should map columns to an object of field to aggregation function', () => {
      // given
      const columnsVO: Required<ColumnVO>[] = [
        {
          id: 'All',
          aggFunc: 'UniqueValue',
          displayName: 'All',
          field: 'All'
        },
        {
          id: 'ProfitLossYearToDateUSD',
          aggFunc: 'Sum',
          displayName: 'Yearly',
          field: 'ProfitLossYearToDateUSD'
        }
      ]

      // when
      const result = mapColumnsVOToColumnsAggregation(columnsVO)

      // then
      expect(result).toEqual({
        All: 'UniqueValue',
        ProfitLossYearToDateUSD: 'Sum'
      })
    })
  })

  describe('mapColumnsVOToGroupingColumns', () => {
    it('should map columns to string array with column names as values', () => {
      // given
      const columnsVO: Required<ColumnVO>[] = [
        {
          id: 'All',
          aggFunc: 'UniqueValue',
          displayName: 'All',
          field: 'All'
        },
        {
          id: 'ProfitLossYearToDateUSD',
          aggFunc: 'Sum',
          displayName: 'Yearly',
          field: 'ProfitLossYearToDateUSD'
        }
      ]

      // when
      const result = mapColumnsVOToGroupingColumns(columnsVO)

      // then
      expect(result).toEqual(['All', 'ProfitLossYearToDateUSD'])
    })
  })

  describe('mapGridSortModelToApiSortModel', () => {
    it('should correctly map multiple sort model items', () => {
      // given
      const input: SortModelItem[] = [
        {
          colId: 'column1',
          sort: 'asc'
        },
        {
          colId: 'column2',
          sort: 'desc'
        }
      ]

      // when
      const result = mapGridSortModelToApiSortModel(input)

      // then
      expect(result).toEqual([
        {
          column: 'column1',
          direction: 'asc'
        },
        {
          column: 'column2',
          direction: 'desc'
        }
      ])
    })
  })

  describe('getKeyColumnNames', () => {
    it('should return an array of key column names for all columns that are marked as keys', () => {
      // given
      const columnInfo: IColumnInfo[] = [
        getColumnInfoDummy({ column: 'All', isPrimaryKey: true }),
        getColumnInfoDummy({ column: 'Trading', isPrimaryKey: false }),
        getColumnInfoDummy({ column: 'Team', isPrimaryKey: true })
      ]

      // when
      const result = getKeyColumnNames(columnInfo)

      // then
      expect(result).toEqual(['All', 'Team'])
    })
  })

  describe('getGroupId', () => {
    test('should return an empty string when route is undefined', () => {
      // given
      const route = undefined

      // when
      const result = getGroupId(route)

      // then
      expect(result).toBe('')
    })

    test('should return an empty string when route is an empty array', () => {
      // given
      const route: string[] = []

      // when
      const result = getGroupId(route)

      // then
      expect(result).toBe('')
    })

    test('should return a single encoded component when route has one element', () => {
      // given
      const route = ['test']

      // when
      const result = getGroupId(route)

      // then
      expect(result).toBe('test')
    })

    test('should return a joined encoded string when route has multiple elements', () => {
      // given
      const route = ['test', 'with space', 'special&char']

      // when
      const result = getGroupId(route)

      // then
      expect(result).toBe('test/with%20space/special%26char')
    })
  })

  describe('getTextSetColumnNames', () => {
    it('should return an array of column names with filterType TextSet', () => {
      const columnInfo: IColumnInfo[] = [
        getColumnInfoDummy({
          column: 'All',
          filterType: 'TextSet'
        }),
        getColumnInfoDummy({
          column: 'Team',
          filterType: 'Number'
        }),
        getColumnInfoDummy({
          column: 'TradingArea',
          filterType: 'TextSet'
        })
      ]

      const textSetColumnNames = getTextSetColumnNames(columnInfo)

      expect(textSetColumnNames.length).toBe(2)
      expect(textSetColumnNames).toEqual(['All', 'TradingArea'])
    })
  })

  describe('mapHeadlineValuesToRiskMetrics', () => {
    it('should return an empty array when the input is an empty object', () => {
      const input: ISummaryHeadlineValues = {}

      const result = mapHeadlineValuesToRiskMetrics(input)

      expect(result).toEqual([])
    })

    it('should map a single key-value pair to a RiskReportMetric object', () => {
      const input: ISummaryHeadlineValues = {
        'Risk A': 10
      }

      const result = mapHeadlineValuesToRiskMetrics(input)

      expect(result).toEqual([
        {
          title: 'Risk A',
          value: 10
        }
      ])
    })

    it('should map multiple key-value pairs to an array of RiskReportMetric objects', () => {
      const input: ISummaryHeadlineValues = {
        'Risk A': 10,
        'Risk B': 20,
        'Risk C': 30
      }

      const result = mapHeadlineValuesToRiskMetrics(input)

      expect(result).toEqual([
        {
          title: 'Risk A',
          value: 10
        },
        {
          title: 'Risk B',
          value: 20
        },
        {
          title: 'Risk C',
          value: 30
        }
      ])
    })
  })

  describe('isRowGroupOpenByDefault', () => {
    test('should return true when groupId is in the expandedGroupIds array', () => {
      // given
      const groupId = 'group1'
      const expandedGroupIds = ['group1', 'group2', 'group3']

      // when
      const result = isRowGroupOpenByDefault(groupId, expandedGroupIds)

      // then
      expect(result).toBe(true)
    })

    test('should return false when groupId is not in the expandedGroupIds array', () => {
      // given
      const groupId = 'group4'
      const expandedGroupIds = ['group1', 'group2', 'group3']

      // when
      const result = isRowGroupOpenByDefault(groupId, expandedGroupIds)

      // then
      expect(result).toBe(false)
    })

    test('should return false when expandedGroupIds is empty', () => {
      // given
      const groupId = 'group1'
      const expandedGroupIds: string[] = []

      // when
      const result = isRowGroupOpenByDefault(groupId, expandedGroupIds)

      // then
      expect(result).toBe(false)
    })
  })

  describe('updateFilterSidePanel', () => {
    beforeEach(() => {
      const container: HTMLElement = document.createElement('div')
      container.innerHTML = `
        <div class="ag-side-button">
          <div class="ag-side-button-label">Columns</div>
        </div>
        <div class="ag-side-button">
          <div class="ag-side-button-label">Filters</div>
        </div>
        <div class="ag-side-button">
          <div class="ag-side-button-label">Visibility</div>
        </div>
        <div class="ag-has-filter">
          <div class="ag-filter-toolpanel-group-title">Strategy</div>
        </div>
        <div class="ag-has-filter">
          <div class="ag-filter-toolpanel-group-title">Daily</div>
        </div>
      `
      document.body.appendChild(container)
    })

    afterEach(() => {
      document.body.innerHTML = ''
    })

    test('should call api.getFilterModel and api.getToolPanelInstance', () => {
      const mockGetFilterModel = vi.fn().mockReturnValue({ make: true })
      const mockGetToolPanelInstance = vi.fn().mockReturnValue({
        setFilterLayout: vi.fn()
      })
      const mockGetColumnDefs = vi.fn().mockReturnValue(columnDefs)
      const mockApi = {
        getFilterModel: mockGetFilterModel,
        getToolPanelInstance: mockGetToolPanelInstance,
        getColumnDefs: mockGetColumnDefs
      } as unknown as GridApi
      const mockFiltersPanel = {
        setFilterLayout: vi.fn()
      } as unknown as IFiltersToolPanel

      updateFilterSidePanel(mockApi, mockFiltersPanel)

      expect(mockGetFilterModel).toHaveBeenCalled()
      expect(mockGetToolPanelInstance).toHaveBeenCalledWith('filters')
      expect(mockGetColumnDefs).toHaveBeenCalled()
    })

    test('should update Filters Panel Label', () => {
      updateFiltersToolPanelLabelTextAndColour({
        Startegy: 'value1',
        Daily: 'value2'
      })

      const elements = document.getElementsByClassName(
        'ag-side-button'
      ) as HTMLCollectionOf<Element>
      const label = elements[1].getElementsByClassName(
        'ag-side-button-label'
      ) as HTMLCollectionOf<Element>

      expect(elements[1].classList.contains('ag-highlight-yellow')).toBe(true)
      expect(label[0].innerHTML).toBe('Filters (2)')
    })

    test('should add css class to enabled filters within filters panel', () => {
      updateFiltersWithinFiltersToolPanelWhenEnabled()

      const elements = document.getElementsByClassName(
        'ag-has-filter'
      ) as HTMLCollectionOf<Element>
      const filter1 = elements[0].getElementsByClassName(
        'ag-filter-toolpanel-group-title'
      )[0] as HTMLElement
      const filter2 = elements[1].getElementsByClassName(
        'ag-filter-toolpanel-group-title'
      )[0] as HTMLElement

      expect(filter1.classList.contains('ag-highlight-yellow')).toBe(true)
      expect(filter2.classList.contains('ag-highlight-yellow')).toBe(true)
    })
  })

  describe('addOpenByDefaultPropertyToChildren', () => {
    it('should add openByDefault to top-level columns', () => {
      const columns: (ColDef | ColGroupDef)[] = [
        { openByDefault: false },
        { openByDefault: false, children: [] }
      ]

      addOpenByDefaultPropertyToChildren(columns)

      expect(columns[0].openByDefault).toBe(true)
      expect(columns[1].openByDefault).toBe(true)
    })

    it('should add openByDefault to nested children up to the specified depth', () => {
      const columns: (ColDef | ColGroupDef)[] = [
        {
          openByDefault: false,
          children: [
            {
              openByDefault: false,
              children: [
                {
                  openByDefault: false,
                  children: [
                    {
                      openByDefault: false,
                      children: [
                        { openByDefault: false } // 5th level
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]

      addOpenByDefaultPropertyToChildren(columns)

      expect(columns[0].openByDefault).toBe(true)
      expect(columns[0].children![0].openByDefault).toBe(true)
      expect(columns[0].children![0].children![0].openByDefault).toBe(true)
      expect(
        columns[0].children![0].children![0].children![0].openByDefault
      ).toBe(true)
      expect(
        columns[0].children![0].children![0].children![0].children![0]
          .openByDefault
      ).toBe(true)
    })

    it('should not add openByDefault beyond the specified depth', () => {
      const columns: (ColDef | ColGroupDef)[] = [
        {
          openByDefault: false,
          children: [
            {
              openByDefault: false,
              children: [
                {
                  openByDefault: false,
                  children: [
                    {
                      openByDefault: false,
                      children: [
                        {
                          openByDefault: false,
                          children: [
                            { openByDefault: false } // 6th level
                          ]
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]

      addOpenByDefaultPropertyToChildren(columns, 5)

      expect(columns[0].openByDefault).toBe(true)
      expect(columns[0].children![0].openByDefault).toBe(true)
      expect(columns[0].children![0].children![0].openByDefault).toBe(true)
      expect(
        columns[0].children![0].children![0].children![0].openByDefault
      ).toBe(true)
      expect(
        columns[0].children![0].children![0].children![0].children![0]
          .openByDefault
      ).toBe(true)
      expect(
        columns[0].children![0].children![0].children![0].children![0]
          .children![0].openByDefault
      ).toBe(false)
    })

    it('should handle empty columns array gracefully', () => {
      const columns: (ColDef | ColGroupDef)[] = []
      addOpenByDefaultPropertyToChildren(columns)
      expect(columns.length).toBe(0)
    })

    it('should handle undefined columns gracefully', () => {
      const columns: (ColDef | ColGroupDef)[] | undefined = undefined
      addOpenByDefaultPropertyToChildren(columns)
      expect(columns).toBeUndefined()
    })
  })

  describe('getColumnDefinitionsOrderedByEnabledFilters', () => {
    it('should return column definitions ordered by enabled filters', () => {
      const mockApi = {
        getColumnDefs: vi
          .fn()
          .mockReturnValue([
            { field: 'col1' },
            { field: 'col2' },
            { field: 'col3' }
          ])
      } as unknown as GridApi

      const filterModel = { col2: true, col3: true }

      const result = getColumnDefinitionsOrderedByEnabledFilters(
        mockApi,
        filterModel
      )
      expect(result.map((colDef) => colDef.field)).toEqual([
        'col2',
        'col3',
        'col1'
      ])
    })
  })

  describe('getPivotColumnsOrdering', () => {
    it('should return ordered pivot column IDs', () => {
      const mockColumnApi = {
        getPivotResultColumns: vi.fn().mockReturnValue([
          { getColId: () => 'col1', getLeft: () => 100 },
          { getColId: () => 'col2', getLeft: () => 50 }
        ])
      } as unknown as ColumnApi

      const result = getPivotColumnsOrdering(mockColumnApi)
      expect(result).toEqual(['col2', 'col1'])
    })
  })

  describe('getCachedBlockStatusSummary', () => {
    it('should return a summary of cached block statuses', () => {
      const mockApi = {
        getCacheBlockState: vi.fn().mockReturnValue({
          block1: { pageStatus: 'loaded' },
          block2: { pageStatus: 'loading' },
          block3: { pageStatus: 'loaded' }
        })
      } as unknown as GridApi

      const result = getCachedBlockStatusSummary(mockApi)
      expect(result).toEqual({ loaded: 2, loading: 1 })
    })
  })

  describe('getCachedBlockCount', () => {
    it('should return the number of cached blocks', () => {
      const mockApi = {
        getCacheBlockState: vi.fn().mockReturnValue({
          block1: {},
          block2: {}
        })
      } as unknown as GridApi

      const result = getCachedBlockCount(mockApi)
      expect(result).toBe(2)
    })
  })

  describe('getColumnTooltipContent', () => {
    it('should return tooltip with aggregation and short description', () => {
      const columnDef: ColDef = { aggFunc: 'Sum' }
      const column: IColumnInfo = {
        type: ColumnType.String,
        column: 'exampleColumn',
        shortDescription: 'Short description',
        canGroupBy: true,
        isPrimaryKey: false,
        canPivotBy: false,
        isSystemData: false
      }

      const result = getColumnTooltipContent(columnDef, column)
      expect(result).toBe('Short description\n\nAggregation: Sum')
    })

    it('should return tooltip with aggregation only if no short description', () => {
      const columnDef: ColDef = { aggFunc: 'Sum' }
      const column: IColumnInfo = {
        type: ColumnType.String,
        column: 'exampleColumn',
        canGroupBy: true,
        isPrimaryKey: false,
        canPivotBy: false,
        isSystemData: false
      }

      const result = getColumnTooltipContent(columnDef, column)
      expect(result).toBe('Aggregation: Sum')
    })
  })

  describe('resetColumnState', () => {
    it('should reset column states to default order and visibility', () => {
      const defaultColumnDefs = ['colA', 'colB', 'colC']
      const columnState: ColumnState[] = [
        { colId: 'colC', hide: true },
        { colId: 'colA', hide: true },
        { colId: 'colB', hide: false }
      ]

      const result = resetColumnState(defaultColumnDefs, columnState)
      expect(result).toEqual([
        { colId: 'colA', hide: false },
        { colId: 'colB', hide: false },
        { colId: 'colC', hide: false }
      ])
    })
  })

  describe('resetColumnOrder', () => {
    it('should reorder column states based on default column definitions', () => {
      const defaultColumnDefs = ['colA', 'colB', 'colC']
      const columnState: ColumnState[] = [
        { colId: 'colC' },
        { colId: 'colA' },
        { colId: 'colB' }
      ]

      const result = resetColumnOrder(defaultColumnDefs, columnState)
      expect(result.map((state) => state.colId)).toEqual([
        'colA',
        'colB',
        'colC'
      ])
    })
  })

  describe('tooltipValueFormat', () => {
    it('should return an empty string for null or undefined values', () => {
      expect(tooltipValueFormat(null, ColumnType.Double)).toBe('')
      expect(tooltipValueFormat(undefined, ColumnType.Date)).toBe('')
    })

    it('should format date values correctly', () => {
      const dateValue = '2022-01-01T00:00:00.000Z'
      expect(tooltipValueFormat(dateValue, ColumnType.Date)).toBe('1/1/2022')
    })

    it('should format date-time values correctly', () => {
      const dateTimeValue = '2022-01-01T09:00:00.000Z'
      expect(tooltipValueFormat(dateTimeValue, ColumnType.DateTime)).toBe(
        '1/1/2022 10:00:00 AM'
      )
    })

    it('should format double values correctly', () => {
      const doubleValue = 1234.5678
      expect(tooltipValueFormat(doubleValue, ColumnType.Double)).toBe(
        '1234.5678'
      )
    })
  })

  describe('columnDefFromColumInfo', () => {
    it('should create a column definition with the correct header name and field', () => {
      const columnInfo: IColumnInfo = {
        name: 'Test Column',
        column: 'testColumn',
        type: ColumnType.String,
        aggregationOperations: ['Sum'],
        canGroupBy: true,
        canPivotBy: false,
        isPrimaryKey: false,
        isSystemData: false
      }

      const columnDef = columnDefFromColumInfo(columnInfo)

      expect(columnDef.headerName).toBe('Test Column')
      expect(columnDef.field).toBe('testColumn')
    })

    it('should set the correct filter based on column type', () => {
      const columnInfo: IColumnInfo = {
        name: 'Date Column',
        column: 'dateColumn',
        type: ColumnType.Date,
        filterType: 'Date',
        aggregationOperations: ['Sum'],
        canGroupBy: true,
        canPivotBy: false,
        isPrimaryKey: false,
        isSystemData: false
      }

      const columnDef = columnDefFromColumInfo(columnInfo)

      expect(columnDef.filter).toBe(DateTimeFilter)
    })

    it('should set tooltip component and value getter', () => {
      const columnInfo: IColumnInfo = {
        name: 'Tooltip Column',
        column: 'tooltipColumn',
        type: ColumnType.Double,
        aggregationOperations: ['Sum'],
        canGroupBy: true,
        canPivotBy: false,
        isPrimaryKey: false,
        isSystemData: false
      }

      const columnDef = columnDefFromColumInfo(columnInfo)

      expect(columnDef.tooltipComponent).toBeDefined()
      expect(columnDef.tooltipValueGetter).toBeDefined()
    })

    it('should set cell class rules for numeric columns', () => {
      const columnInfo: IColumnInfo = {
        name: 'Numeric Column',
        column: 'numericColumn',
        type: ColumnType.Double,
        aggregationOperations: ['Sum'],
        canGroupBy: true,
        canPivotBy: false,
        isPrimaryKey: false,
        isSystemData: false
      }

      const columnDef = columnDefFromColumInfo(columnInfo)

      expect(columnDef.cellClassRules).toHaveProperty('positive')
      expect(columnDef.cellClassRules).toHaveProperty('negative')
    })

    it('should set cell class for right-aligned content', () => {
      const columnInfo: IColumnInfo = {
        name: 'Right Align Column',
        column: 'rightAlignColumn',
        type: ColumnType.DateTime,
        aggregationOperations: ['Sum'],
        canGroupBy: true,
        canPivotBy: false,
        isPrimaryKey: false,
        isSystemData: false
      }

      const columnDef = columnDefFromColumInfo(columnInfo)

      expect(columnDef.cellClass).toBe('align-right')
    })

    it('should set hide property based on preset column state', () => {
      const columnInfo: IColumnInfo = {
        name: 'Hidden Column',
        column: 'hiddenColumn',
        type: ColumnType.String,
        aggregationOperations: ['Sum'],
        canGroupBy: true,
        canPivotBy: false,
        isPrimaryKey: false,
        isSystemData: false
      }

      const presetColumnsState: ColumnState[] = [
        { colId: 'hiddenColumn', hide: true }
      ]

      const columnDef = columnDefFromColumInfo(columnInfo, presetColumnsState)

      expect(columnDef.hide).toBe(true)
    })

    it('should not hide column if resetColumns is true', () => {
      const columnInfo: IColumnInfo = {
        name: 'Reset Column',
        column: 'resetColumn',
        type: ColumnType.String,
        aggregationOperations: ['Sum'],
        canGroupBy: true,
        canPivotBy: false,
        isPrimaryKey: false,
        isSystemData: false
      }

      const presetColumnsState: ColumnState[] = [
        { colId: 'resetColumn', hide: true }
      ]

      const columnDef = columnDefFromColumInfo(
        columnInfo,
        presetColumnsState,
        true
      )

      expect(columnDef.hide).toBe(false)
    })
  })
})
