import { DateTimeFilter } from '@/components/Grid/Filters/DateTimeFilter'
import { Tooltip } from '@/components/Tooltip'
import {
  ColumnsHeaderNames,
  RiskReportMetric
} from '@/core/redux/features/report'
import { GridFilter, TableRow } from '@/core/services/grid'
import {
  ColumnType,
  IColumn,
  IColumnInfo,
  IColumnsAggregation,
  IRowRange,
  ISortModel,
  ISummaryHeadlineValues,
  NumberFormat
} from '@/core/services/riskReport'
import {
  BaseCellDataType,
  CellClassParams,
  ColDef,
  ColGroupDef,
  ColumnApi,
  ColumnState,
  ColumnVO,
  GridApi,
  IFiltersToolPanel,
  ITooltipParams,
  SortModelItem
} from '@ag-grid-community/core'
import { formatNumber } from '../math'
import { ColumnGroupShowType } from '@ag-grid-community/core/dist/cjs/es5/main'

/**
 * This function returns a class rule object for numeric cell types, which is used to add conditional formatting to cells based on whether their value is negative or positive, turning them red or green respectively.
 */
export const getNumericCellClassRules = () => {
  return {
    negative: (params: CellClassParams) => params.value < 0,
    positive: (params: CellClassParams) => params.value >= 0
  }
}

export const isNumericColumn = (columnType: ColumnType | undefined) => {
  return columnType === 'Double'
}

export const shouldContentBeAlignedToRight = (
  columnType: ColumnType | undefined
): boolean => {
  return !!columnType && ['Date', 'DateTime', 'Double'].includes(columnType)
}

export const getLocaleRegion = () => {
  return navigator.languages && navigator.languages.length
    ? navigator.languages[0]
    : navigator.language
}

export const formatToDate = (value: string | number) =>
  new Date(value).toLocaleDateString(getLocaleRegion())

export const formatToLocalDateAndTime = (value: string | number) => {
  const localRegion = getLocaleRegion()
  const localTimeZone: string | undefined =
    Intl.DateTimeFormat().resolvedOptions().timeZone
  const date = new Date(value)
  const formattedDate = date.toLocaleDateString(localRegion, {
    timeZone: localTimeZone ?? 'UTC'
  })
  const formattedTime = date.toLocaleTimeString(localRegion, {
    timeZone: localTimeZone ?? 'UTC'
  })

  return `${formattedDate} ${formattedTime}`
}

export const formatToNumber = (
  value: string | number,
  numberFormat: NumberFormat
): string => {
  const { significantFigures, decimalDigits, multiplier, prefix, suffix } =
    numberFormat

  const formattedNumber = formatNumber({
    value: Number(value) * multiplier,
    significantFigures,
    decimalDigits
  })

  return `${prefix ?? ''}${formattedNumber}${suffix ?? ''}`
}

export const tooltipValueFormat = (
  value: string | number | null | undefined,
  columnType: ColumnType,
  numberFormat?: NumberFormat
): string => {
  if (!value || value === '...') {
    return ''
  }

  switch (columnType) {
    case 'Date':
      return formatToDate(value)
    case 'DateTime':
      return formatToLocalDateAndTime(value)
    case 'Double':
      if (numberFormat) {
        return String(
          value.toLocaleString(undefined, {
            minimumFractionDigits: 0,
            maximumFractionDigits: 20
          })
        )
      }
      return String(value)
    default:
      return String(value)
  }
}

export const formatValue = (
  value: string | number | null | undefined,
  columnType: ColumnType,
  numberFormat?: NumberFormat
): string => {
  if (value === null || value === undefined) {
    return ''
  }

  switch (columnType) {
    case 'Date':
      return formatToDate(value)
    case 'DateTime':
      return formatToLocalDateAndTime(value)
    case 'Double':
      if (numberFormat) {
        return formatToNumber(value, numberFormat)
      }
      return String(value)
    default:
      return String(value)
  }
}

export const getFilter = ({ filterType }: IColumnInfo) => {
  switch (filterType) {
    case 'TextSet':
      return GridFilter.AgSetColumnFilter
    case 'Date':
      return DateTimeFilter
    case 'Number':
      return GridFilter.AgNumberColumnFilter
    default:
      return GridFilter.AgTextColumnFilter
  }
}

export const mapColumnToCellDataType = (
  columnType: ColumnType
): BaseCellDataType | undefined => {
  switch (columnType) {
    case 'Boolean':
      return 'text'
    case 'String':
      return 'text'
    case 'Date':
    case 'DateTime':
      return 'dateString'
    case 'Double':
      return 'number'
    default:
      return undefined
  }
}

export const resetColumnOrder = (
  defaultColumnDefs: string[],
  columnState: ColumnState[]
) => {
  const state = [...columnState].sort((a, b) => {
    return (
      defaultColumnDefs.findIndex((x: any) => a.colId === x) -
      defaultColumnDefs.findIndex((x: any) => b.colId === x)
    )
  })

  return state
}

export const resetColumnState = (
  defaultColumnDefs: string[],
  columnState: ColumnState[]
) => {
  let presetColumnState = columnState.map(
    (columnState) =>
      ({
        colId: columnState.colId,
        hide: false
      } as ColumnState)
  )

  presetColumnState = resetColumnOrder(defaultColumnDefs, presetColumnState)

  return presetColumnState
}

export const columnDefFromColumInfo = (
  column: IColumnInfo,
  presetColumnsState?: ColumnState[],
  resetColumns?: boolean,
  columnGroupShow?: ColumnGroupShowType
) => {
  const supportedAggFuncs = column.aggregationOperations
  const canUseAsPivotValueColumn: boolean | undefined = !column.canPivotBy
  const defaultAggFunc = supportedAggFuncs && supportedAggFuncs[0]
  const presetColumnState = presetColumnsState?.find(
    (columnState) => columnState.colId === column.column
  )

  const hideColumn = (presetColumnState: ColumnState | undefined) => {
    if (resetColumns || !presetColumnsState) {
      return false
    }

    if (presetColumnState === undefined) {
      return true
    }

    return !!presetColumnState.hide
  }
  const columnDef: ColDef = {
    headerName: column.name,
    field: column.column,
    allowedAggFuncs: supportedAggFuncs,
    aggFunc: defaultAggFunc,
    defaultAggFunc: defaultAggFunc,
    enableRowGroup: column.canGroupBy,
    enablePivot: column.canPivotBy,
    enableValue: canUseAsPivotValueColumn,
    filter: getFilter(column),
    tooltipValueGetter: (params: ITooltipParams) =>
      tooltipValueFormat(params.value, column.type, column.numberFormat),
    tooltipComponent: Tooltip,
    valueFormatter: (params) =>
      formatValue(params.value, column.type, column.numberFormat),
    sortable: true,
    refData: {
      type: column.type || undefined
    },
    cellDataType: mapColumnToCellDataType(column.type),
    useValueFormatterForExport: false,
    hide: hideColumn(presetColumnState),
    columnGroupShow: columnGroupShow
  }

  columnDef.headerTooltip = getColumnTooltipContent(columnDef, column)

  if (isNumericColumn(column.type)) {
    columnDef.cellClassRules = getNumericCellClassRules()
  }

  if (shouldContentBeAlignedToRight(column.type)) {
    columnDef.cellClass = 'align-right'
  }

  return columnDef
}

export const mapColumnInfoToColumnDefs = ({
  columnInfo,
  presetColumnsState,
  resetColumns
}: {
  columnInfo: IColumnInfo[]
  presetColumnsState?: ColumnState[]
  resetColumns?: boolean
}): (ColDef | ColGroupDef)[] => {
  return columnInfo
    .filter((eachColumn) => !eachColumn.isSystemData)
    .map((column) => {
      if (column.children && column.children.length > 0) {
        const children = mapColumnInfoToColumnDefs({
          columnInfo: column.children,
          presetColumnsState,
          resetColumns
        })

        const columnDefClosed: ColDef = columnDefFromColumInfo(
          column,
          presetColumnsState,
          resetColumns,
          undefined
        )

        children.push(columnDefClosed)

        const columnDef: ColGroupDef = {
          headerName: column.name,
          children: children,
          columnGroupShow: 'open',
          openByDefault: true
        }
        columnDef.headerTooltip = getColumnTooltipContent(columnDef, column)

        return columnDef
      }
      const columnDef: ColDef = columnDefFromColumInfo(
        column,
        presetColumnsState,
        resetColumns,
        'open'
      )
      return columnDef
    })
}

export const getColumnTooltipContent = (
  columnDef: ColDef,
  column: IColumnInfo
) => {
  let tooltip = `Aggregation: ${columnDef.aggFunc || 'none'}`

  if (column.shortDescription) {
    tooltip = `${column.shortDescription}\n\n${tooltip}`
  }

  return tooltip
}

export const updateColumnsHeaderNames = (
  colDefs: ColDef[],
  headersNames: ColumnsHeaderNames
): ColDef[] => {
  return colDefs.map((colDef) => {
    const headerName =
      (colDef.field && headersNames[colDef.field]) || colDef.headerName

    return {
      ...colDef,
      headerName
    }
  })
}

/**
 * Maps an array of columns to an array of table rows.
 * Each row is an object with keys corresponding to column names and values corresponding to column values.
 */
export const mapColumnsToTableRows = (columns: IColumn[]): TableRow[] => {
  const rows: TableRow[] = []
  // Get the number of rows from the first column
  const rowCount = columns[0].Count

  // Loop through each row
  for (let i = 0; i < rowCount; i++) {
    const row: TableRow = {}

    // Loop through each column and add the value to the row object
    for (const column of columns) {
      row[column.Name] = column.Values[i]
    }

    rows.push(row)
  }

  return rows
}

/**
 * Returns a `RowRange` object that specifies a range of rows in a grid based on a starting row index and an ending row index.
 */
export const getRowRange = (startRow = 0, endRow = 0): IRowRange => {
  // Calculate the number of rows in the specified range
  const rowCount = endRow - startRow + 1

  return {
    rowOffset: startRow,
    rowCount
  }
}

/**
 * Removes any empty columns from an array of ColumnVO objects.
 * An empty column is defined as one that has no 'field' or 'aggFunc' properties.
 */
export const filterOutEmptyColumnsVO = (
  columnsVO: ColumnVO[]
): Required<ColumnVO>[] => {
  return columnsVO.reduce<Required<ColumnVO>[]>((prev, curr) => {
    const { field, aggFunc, ...rest } = curr

    // If the current column has both a 'field' and 'aggFunc', then it is not empty, so add it to the 'prev' array.
    if (field && aggFunc) {
      const columnVO = { field, aggFunc, ...rest }
      return [...prev, columnVO]
    }

    // If the current column is empty, then skip it and return 'prev' unchanged.
    return prev
  }, [])
}

export const mapColumnsVOToColumnsAggregation = (
  columnsVO: Required<ColumnVO>[]
): IColumnsAggregation => {
  return columnsVO.reduce<IColumnsAggregation>((prev, curr) => {
    return { ...prev, [curr.field]: curr.aggFunc }
  }, {})
}

export const mapColumnsVOToGroupingColumns = (
  rowGroupCols: Required<ColumnVO>[]
): string[] => {
  return rowGroupCols.map((column) => column.field)
}

export const mapGridSortModelToApiSortModel = (
  sortModels: SortModelItem[]
): ISortModel[] => {
  return sortModels.map((model) => ({
    column: model.colId,
    direction: model.sort
  }))
}

export const getKeyColumnNames = (columnInfo: IColumnInfo[]): string[] => {
  return columnInfo
    .filter((column) => column.isPrimaryKey)
    .map((column) => column.column)
}

export const getTextSetColumnNames = (columnInfo: IColumnInfo[]): string[] => {
  return columnInfo
    .filter((column) => column.filterType === 'TextSet')
    .map((column) => column.column)
}

export const mapHeadlineValuesToRiskMetrics = (
  headlineValues: ISummaryHeadlineValues
): RiskReportMetric[] => {
  return Object.entries(headlineValues).map(([title, value]) => ({
    title,
    value
  }))
}

export const isRowGroupOpenByDefault = (
  groupId: string,
  expandedGroupIds: string[]
): boolean => {
  return expandedGroupIds.includes(groupId)
}

export function getCachedBlockCount(api: GridApi): number {
  const cachedBlocks = api.getCacheBlockState()
  return Object.keys(cachedBlocks).length
}

export function getCachedBlockStatusSummary(api: GridApi): any {
  const cachedBlocks = api.getCacheBlockState()
  const cachedBlockStatuses: string[] = Object.values(cachedBlocks).map(
    (blockInfo: any) => blockInfo['pageStatus']
  )
  const counts: any = {}

  for (const status of cachedBlockStatuses) {
    counts[status] = counts[status] ? counts[status] + 1 : 1
  }

  return counts
}

export const getPivotColumnsOrdering = (columnApi: ColumnApi | undefined) =>
  columnApi
    ?.getPivotResultColumns()
    ?.sort((a, b) => (a.getLeft() ?? 0) - (b.getLeft() ?? 0))
    .map((col) => col.getColId())

export const getColumnDefinitionsOrderedByEnabledFilters = (
  api: GridApi,
  filterModel: { [key: string]: any } | undefined
) => {
  const enabledKeys = Object.keys(filterModel ?? {})

  const allFilters = api
    .getColumnDefs()
    ?.filter((coldef: ColDef) => !!coldef.field)
    .map((coldef: ColDef) => coldef)

  const isFilterEnabled = (coldef: ColDef): boolean => {
    return enabledKeys.includes(coldef.field as string)
  }

  const enabledFilters = allFilters?.filter(isFilterEnabled)

  const disabledFilters = allFilters?.filter(
    (coldef) => !isFilterEnabled(coldef)
  )

  const columnDefs = [...(enabledFilters ?? []), ...(disabledFilters ?? [])]

  return columnDefs
}

export const updateFilterSidePanel = (
  api: GridApi,
  filtersPanel: IFiltersToolPanel | null
) => {
  const filterModel = api.getFilterModel()
  const toolPanelInstance = api.getToolPanelInstance('filters')
  if (toolPanelInstance) {
    const colDefs = getColumnDefinitionsOrderedByEnabledFilters(
      api,
      filterModel
    )
    if (colDefs.length) {
      filtersPanel = toolPanelInstance as IFiltersToolPanel
      filtersPanel.setFilterLayout(colDefs)
    }
    updateFiltersToolPanelLabelTextAndColour(filterModel)
    updateFiltersWithinFiltersToolPanelWhenEnabled()
  }
}

export const updateFiltersToolPanelLabelTextAndColour = (
  filterModel: { [key: string]: any } | undefined
) => {
  const className = 'ag-highlight-yellow'
  const labelName = 'Filters'
  const elements = document.getElementsByClassName('ag-side-button')
  if (elements) {
    const element = elements[1]
    const labels = element.getElementsByClassName('ag-side-button-label')
    if (labels) {
      const label = labels[0]
      const enabledKeys = Object.keys(filterModel ?? {})
      if (enabledKeys.length) {
        if (!element.classList.contains(className)) {
          element.classList.add(`${className}`)
        }
        label.innerHTML = `${labelName} (${enabledKeys.length})`
      } else {
        element.classList.remove(`${className}`)
        label.innerHTML = `${labelName}`
      }
    }
  }
}

export const updateFiltersWithinFiltersToolPanelWhenEnabled = () => {
  const className = 'ag-highlight-yellow'
  const elementList = document.getElementsByClassName('ag-has-filter')
  if (elementList) {
    for (const elem of elementList) {
      const label = elem.getElementsByClassName(
        'ag-filter-toolpanel-group-title'
      )[0]
      label.classList.add(`${className}`)
    }
  }
}

export const addOpenByDefaultPropertyToChildren = (
  columns: (ColDef | ColGroupDef)[],
  depth = 5
) => {
  if (depth === 0 || !columns) return
  columns.forEach((column: ColDef | ColGroupDef) => {
    if ('openByDefault' in column) {
      column.openByDefault = true
      if (column.children) {
        addOpenByDefaultPropertyToChildren(column.children, depth - 1)
      }
    }
  })
}
