import { GetRowIdParams } from '@ag-grid-community/core'

const handleError = (
  message: string,
  data: unknown,
  onErrorCallback?: (message: string) => void
) => {
  if (onErrorCallback) {
    onErrorCallback(`${message} Please contact M72 Support to investigate.`)
  }

  throw Error(`${message} Row data: ${JSON.stringify(data)}`)
}
export const getRowId = (
  params: GetRowIdParams,
  keyColumns: string[],
  onErrorCallback?: (message: string) => void
) => {
  const { data, parentKeys, columnApi, level } = params
  const groups = columnApi.getRowGroupColumns()
  const isGroup = groups && groups.length > level

  if (!isGroup) {
    if (keyColumns.length === 0) {
      const message =
        'Cannot accurately determine row identifier due to missing key columns and no grouping is defined.'
      handleError(message, data, onErrorCallback)
    }

    return `${getGroupId(parentKeys)}-${keyColumns
      .map((key) => encodeURIComponent(data[key]))
      .join('/')}`
  }

  const thisColumnKey = groups[level].getColId()

  if (!thisColumnKey) {
    const message = `Cannot accurately determine row identifier due to missing column id on level ${level} for parent group ${parentKeys}.`
    handleError(message, data, onErrorCallback)
  }

  const thisColumnValue = data[thisColumnKey]

  return `${getGroupId([...(parentKeys || []), thisColumnValue])}`
}

export const getGroupId = (route: string[] | undefined): string =>
  route?.map(encodeURIComponent).join('/') ?? ''
