import {
  AgGridFilterModel,
  AgGridFilterModels,
  AgGridFilterOperator,
  AgGridLogicalFilterOperator
} from '@/core/services/grid'
import {
  FilterOperators,
  IFilter,
  IFilterModel,
  ILogicalFilterOperator
} from '@/core/services/riskReport'

export const mapAgGridFilterModelsToFilterModel = (
  agGridFilterModels: AgGridFilterModels,
  quickFilters?: Record<string, Partial<AgGridFilterModel> | undefined>
): IFilterModel[] => {
  const selectedFilters = Object.entries(agGridFilterModels)

  if (!selectedFilters.length) {
    return []
  }

  const result = selectedFilters.map((filter) => {
    const columnName = filter[0]
    let filterModel = filter[1]

    const output: IFilterModel = {
      column: columnName,
      logicalOperator: normalizeAgGridLogicalOperator(filterModel.operator),
      filters: []
    }

    // If quickFilters are provided, we need to check if the current column has a quick filter
    // and if so, we need to use it instead of the one from the grid.
    // We should use quick filter over ag-grid filter because, as instance, we use local time in ui filter but should
    // send utc time to the server.
    if (quickFilters?.[columnName]) {
      filterModel = quickFilters[columnName] as AgGridFilterModel
    }

    if (filterModel.filterType === AgGridFilterOperator.Set) {
      output.filters.push(deserializeSetFilter(filterModel))
    }

    if (filterModel.conditions && filterModel.operator) {
      output.logicalOperator = normalizeAgGridLogicalOperator(
        filterModel.operator
      )

      output.filters = filterModel.conditions.map(
        (condition: AgGridFilterModel) =>
          mapAgGridFilterModelToFilter(condition)
      )
    }

    if (isSimpleFilter(filterModel)) {
      output.filters.push(mapAgGridFilterModelToFilter(filterModel))
    }

    return output
  })

  return result
}

export const deserializeSetFilter = (
  filterModel: AgGridFilterModel
): IFilter => {
  const { values } = filterModel

  if (values?.length) {
    return {
      operator: FilterOperators.In,
      value: `${values.join(',')}`
    }
  }

  return {
    operator: FilterOperators.Equals,
    value: `${values}`
  }
}

export const mapAgGridFilterModelToFilter = (
  filterModel: AgGridFilterModel
): IFilter => {
  const { dateFrom, dateTo, type } = filterModel

  if (dateFrom && dateTo) {
    return {
      operator: FilterOperators.InRange,
      value: `${dateFrom},${dateTo}`
    }
  }

  if (dateFrom) {
    return {
      operator: mapAgGridFilterOperatorToFilterOperator(type),
      value: dateFrom
    }
  }

  return {
    operator: mapAgGridFilterOperatorToFilterOperator(type),
    value: getFilterValue(filterModel)
  }
}

export const getFilterValue = (filterModel: AgGridFilterModel): string => {
  const { dateFrom, dateTo, filter, filterTo } = filterModel

  if (dateFrom && dateTo) {
    return `${dateFrom},${dateTo}`
  }

  if (dateFrom) {
    return dateFrom
  }

  if (typeof filter !== 'undefined' && typeof filterTo !== 'undefined') {
    return `${filter},${filterTo}`
  }

  return `${filter}`
}

export const mapAgGridFilterOperatorToFilterOperator = (
  agGridOperator: AgGridFilterOperator
): FilterOperators => {
  const map: Record<string, FilterOperators> = {
    [`${AgGridFilterOperator.NotEqual}`]: FilterOperators.NotEquals,
    [`${AgGridFilterOperator.Blank}`]: FilterOperators.Null,
    [`${AgGridFilterOperator.NotBlank}`]: FilterOperators.NotNull,
    [`${AgGridFilterOperator.InRange}`]: FilterOperators.InRange,
    [`${AgGridFilterOperator.LessThanOrEqual}`]:
      FilterOperators.LessThanOrEquals,
    [`${AgGridFilterOperator.GreaterThanOrEqual}`]:
      FilterOperators.GreaterThanOrEquals
  }

  return map[agGridOperator] ?? agGridOperator
}

export const isSimpleFilter = (filterModel: AgGridFilterModel): boolean => {
  return (
    !(filterModel.conditions && filterModel.operator) &&
    filterModel.filterType !== AgGridFilterOperator.Set
  )
}

/**
 * Maps an AgGrid filter operator to the notation suited to the API.
 */
export const normalizeAgGridLogicalOperator = (
  filterOperator?: AgGridLogicalFilterOperator
): ILogicalFilterOperator => {
  switch (filterOperator) {
    case 'AND':
      return 'And'
    case 'OR':
      return 'Or'
    default:
      return 'Or'
  }
}

/**
 * For some reasons (I guess because of async issue somewhere in AgGrid)
 * the filterModel which has been put into data request is wrong when
 * we have just 1 element selected.
 *
 * If you curious about the case you can play here with currentFilterModel.
 *
 * If you will iterate over entries of that object and do console.log -> entry[1].values,
 * when 1 element is selected you will see Array(0) in the console.
 *
 * But if you slice that array (I think spread works too) you will see Array(1).
 */

export const refineFilterModel = (filterModel: Record<string, any>) =>
  Object.entries(filterModel || {}).reduce((acc, curr) => {
    const [key, value] = curr

    if (value.filterType === 'set' && value.values) {
      acc[key] = {
        ...value,
        values: value.values.slice()
      }
    } else {
      acc[key] = { ...value }
    }

    return acc
  }, {} as any)
