import {
  FilterOperators,
  ILogicalFilterOperator
} from '@/core/services/riskReport'
import {
  deserializeSetFilter,
  getFilterValue,
  isSimpleFilter,
  mapAgGridFilterModelToFilter,
  mapAgGridFilterOperatorToFilterOperator,
  normalizeAgGridLogicalOperator,
  refineFilterModel
} from '@/core/utils/grid'
import { AgGridFilterModel, AgGridFilterOperator } from '@/core/services/grid'

describe('utils/filters', () => {
  describe('refineFilterModel', () => {
    it('test refineFilterModel', () => {
      const filterModel = {
        column1: {
          filterType: 'set',
          values: ['value1']
        },
        column2: {
          filterType: 'set',
          values: ['value2']
        },
        column3: {
          filterType: 'set',
          values: ['value3']
        }
      }

      const refinedFilterModel = refineFilterModel(filterModel)

      expect(refinedFilterModel).toEqual({
        column1: {
          filterType: 'set',
          values: ['value1']
        },
        column2: {
          filterType: 'set',
          values: ['value2']
        },
        column3: {
          filterType: 'set',
          values: ['value3']
        }
      })
    })

    it('test refineFilterModel when the argument is null or undefined', () => {
      // @ts-expect-error - test case
      const refinedFilterModel = refineFilterModel(null)
      expect(refinedFilterModel).toEqual({})

      // @ts-expect-error - test case
      const refinedFilterModel2 = refineFilterModel(undefined)
      expect(refinedFilterModel2).toEqual({})
    })
  })

  describe('deserializeSetFilter', () => {
    it('should return a filter with IFilterOperator.In and single value', () => {
      // given
      const filterModel = {
        values: ['test']
      } as AgGridFilterModel

      // when
      const result = deserializeSetFilter(filterModel)

      // then
      expect(result).toEqual({
        operator: FilterOperators.In,
        value: 'test'
      })
    })

    it('should return a filter with IFilterOperator.In and multiple values', () => {
      const filterModel = {
        values: ['test1', 'test2', 'test3']
      } as AgGridFilterModel

      // when
      const result = deserializeSetFilter(filterModel)

      // then
      expect(result).toEqual({
        operator: FilterOperators.In,
        value: 'test1,test2,test3'
      })
    })

    it('should return a filter with IFilterOperator.Equals and empty value when values array is empty', () => {
      // given
      const filterModel = {
        values: [] as (string | number | boolean)[]
      } as AgGridFilterModel

      // then
      const result = deserializeSetFilter(filterModel)

      // then
      expect(result).toEqual({
        operator: FilterOperators.Equals,
        value: ''
      })
    })
  })

  describe('mapAgGridFilterModelToFilter', () => {
    it('should map dateFrom and dateTo to InRange operator', () => {
      const filterModel: AgGridFilterModel = {
        dateFrom: '2021-01-01',
        dateTo: '2021-01-31'
      } as AgGridFilterModel

      // when
      const result = mapAgGridFilterModelToFilter(filterModel)

      // then
      expect(result).toEqual({
        operator: FilterOperators.InRange,
        value: '2021-01-01,2021-01-31'
      })
    })

    it('should map dateFrom to the corresponding operator', () => {
      // given
      const filterModel = {
        type: AgGridFilterOperator.GreaterThan,
        dateFrom: '2021-01-01'
      } as AgGridFilterModel

      // when
      const result = mapAgGridFilterModelToFilter(filterModel)

      // then
      expect(result).toEqual({
        operator: AgGridFilterOperator.GreaterThan,
        value: '2021-01-01'
      })
    })

    it('should map filter and filterTo to the corresponding operator', () => {
      // given
      const filterModel = {
        type: AgGridFilterOperator.LessThanOrEqual,
        filter: 10,
        filterTo: 20
      } as AgGridFilterModel

      // when
      const result = mapAgGridFilterModelToFilter(filterModel)

      // then
      expect(result).toEqual({
        operator: FilterOperators.LessThanOrEquals,
        value: '10,20'
      })
    })

    it('should map filter to the corresponding operator', () => {
      const filterModel = {
        type: AgGridFilterOperator.Contains,
        filter: 'example'
      } as AgGridFilterModel

      // when
      const result = mapAgGridFilterModelToFilter(filterModel)

      // then
      expect(result).toEqual({
        operator: AgGridFilterOperator.Contains,
        value: 'example'
      })
    })
  })

  describe('getFilterValue', () => {
    test.each`
      dateFrom        | dateTo          | filter  | filterTo | expected
      ${'2021-01-01'} | ${'2021-01-31'} | ${5}    | ${10}    | ${'2021-01-01,2021-01-31'}
      ${'2021-01-01'} | ${undefined}    | ${5}    | ${10}    | ${'2021-01-01'}
      ${undefined}    | ${undefined}    | ${5}    | ${10}    | ${'5,10'}
      ${undefined}    | ${undefined}    | ${'A'}  | ${'Z'}   | ${'A,Z'}
      ${undefined}    | ${undefined}    | ${true} | ${false} | ${'true,false'}
    `(
      'returns $expected when dateFrom=$dateFrom, dateTo=$dateTo, filter=$filter, filterTo=$filterTo',
      ({ dateFrom, dateTo, filter, filterTo, expected }) => {
        // given
        const filterModel = {
          filter,
          filterTo,
          dateFrom,
          dateTo
        } as AgGridFilterModel

        // when
        const result = getFilterValue(filterModel)

        // then
        expect(result).toBe(expected)
      }
    )
  })

  describe('mapAgGridFilterOperatorToFilterOperator', () => {
    test.each`
      agGridOperator                             | expected
      ${AgGridFilterOperator.Equals}             | ${AgGridFilterOperator.Equals}
      ${AgGridFilterOperator.NotEqual}           | ${FilterOperators.NotEquals}
      ${AgGridFilterOperator.Contains}           | ${AgGridFilterOperator.Contains}
      ${AgGridFilterOperator.StartsWith}         | ${AgGridFilterOperator.StartsWith}
      ${AgGridFilterOperator.EndsWith}           | ${AgGridFilterOperator.EndsWith}
      ${AgGridFilterOperator.GreaterThan}        | ${AgGridFilterOperator.GreaterThan}
      ${AgGridFilterOperator.LessThan}           | ${AgGridFilterOperator.LessThan}
      ${AgGridFilterOperator.Blank}              | ${FilterOperators.Null}
      ${AgGridFilterOperator.NotBlank}           | ${FilterOperators.NotNull}
      ${AgGridFilterOperator.InRange}            | ${FilterOperators.InRange}
      ${AgGridFilterOperator.Set}                | ${AgGridFilterOperator.Set}
      ${AgGridFilterOperator.LessThanOrEqual}    | ${FilterOperators.LessThanOrEquals}
      ${AgGridFilterOperator.GreaterThanOrEqual} | ${FilterOperators.GreaterThanOrEquals}
    `('maps $agGridOperator to $expected', ({ agGridOperator, expected }) => {
      // when
      const result = mapAgGridFilterOperatorToFilterOperator(agGridOperator)

      // then
      expect(result).toEqual(expected)
    })
  })

  describe('isSimpleFilter', () => {
    it('should return true for simple filter without conditions and operator', () => {
      // given
      const filterModel = {
        conditions: undefined,
        operator: undefined
      } as AgGridFilterModel

      // when
      const result = isSimpleFilter(filterModel)

      expect(result).toBe(true)
    })

    it('should return false for filter with conditions and operator', () => {
      // given
      const filterModel = {
        operator: 'AND',
        conditions: [] as AgGridFilterModel[]
      } as AgGridFilterModel

      // when
      const result = isSimpleFilter(filterModel)

      // then
      expect(result).toBe(false)
    })

    it('should return false for filter with Set filterType', () => {
      // given
      const filterModel = {
        filterType: AgGridFilterOperator.Set
      } as AgGridFilterModel

      // when
      const result = isSimpleFilter(filterModel)

      // then
      expect(result).toBe(false)
    })

    it('should return true for filter with only conditions', () => {
      const filterModel: AgGridFilterModel = {
        conditions: [] as AgGridFilterModel[]
      } as AgGridFilterModel

      // when
      const result = isSimpleFilter(filterModel)

      // then
      expect(result).toBe(true)
    })

    it('should return true for filter with only operator', () => {
      // given
      const filterModel: AgGridFilterModel = {
        operator: 'AND'
      } as AgGridFilterModel

      // when
      const result = isSimpleFilter(filterModel)

      // then
      expect(result).toBe(true)
    })
  })

  describe('normalizeAgGridLogicalOperator', () => {
    test.each`
      filterOperator | expectedResult
      ${'AND'}       | ${'And'}
      ${'OR'}        | ${'Or'}
      ${undefined}   | ${'Or'}
    `(
      'should return $expectedResult when filterOperator is $filterOperator',
      ({ filterOperator, expectedResult }) => {
        // when
        const result: ILogicalFilterOperator =
          normalizeAgGridLogicalOperator(filterOperator)

        // then
        expect(result).toBe(expectedResult)
      }
    )
  })
})
