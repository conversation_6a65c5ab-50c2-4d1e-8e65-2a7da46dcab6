import { vi } from 'vitest'
import { logger } from '.'
import { env } from '@/core/config'
describe('Logger', () => {
  const consoleInfoSpy = vi.spyOn(console, 'info')
  const consoleErrorSpy = vi.spyOn(console, 'error')

  beforeEach(() => {
    vi.clearAllMocks()
    window.debugMode = true
  })

  it('should log message', () => {
    env.isLoggerEnabled = true
    logger.info('test', {}, undefined)
    expect(consoleInfoSpy).toHaveBeenCalledWith('test', {}, undefined)
  })

  it('should not log message', () => {
    env.isLoggerEnabled = false
    logger.info('test')
    expect(consoleInfoSpy).not.toHaveBeenCalled()
  })

  it('should log error message', () => {
    env.isLoggerEnabled = true
    logger.error('Error test')
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      'Error test',
      undefined,
      undefined
    )
  })
})
