import { vi } from 'vitest'
import { logger } from '.'
import { datadogLogs } from '@datadog/browser-logs'

vi.mock('@/core/config', () => ({
  env: {
    isLoggerEnabled: true
  }
}))

vi.mock('@datadog/browser-logs', () => ({
  datadogLogs: {
    logger: {
      info: vi.fn(),
      error: vi.fn()
    }
  }
}))

describe('Logger', () => {
  const info = console.info
  const error = console.error
  let loggedOutput = ''
  const recordOutput = (output: string, ...rest: any[]) => {
    loggedOutput = output + JSON.stringify(rest)
  }
  window.debugMode = true

  beforeEach(() => {
    vi.resetAllMocks()
    console.info = vi.fn().mockImplementation(recordOutput)
    console.error = vi.fn().mockImplementation(recordOutput)
  })

  afterEach(() => {
    console.info = info
    console.error = error
  })

  it('should log a message', () => {
    logger.info('test')

    expect(loggedOutput).toBe('test[null,null]')
    expect(datadogLogs.logger.info).toHaveBeenCalledWith(
      'test',
      undefined,
      undefined
    )
  })

  it('should log a message with context', () => {
    logger.info('test', { context: true })

    expect(loggedOutput).toBe('test[{"context":true},null]')
    expect(datadogLogs.logger.info).toHaveBeenCalledWith(
      'test',
      { context: true },
      undefined
    )
  })

  it('should log a message with error', () => {
    logger.info('test', undefined, { message: 'error', name: 'Error' })

    expect(loggedOutput).toBe('test[null,{"message":"error","name":"Error"}]')
    expect(datadogLogs.logger.info).toHaveBeenCalledWith('test', undefined, {
      message: 'error',
      name: 'Error'
    })
  })

  it('should log an error message', () => {
    logger.error('test')
    expect(loggedOutput).toBe('test[null,null]')
    expect(datadogLogs.logger.error).toHaveBeenCalledWith(
      'test',
      undefined,
      undefined
    )
  })

  it('should log an error message with context', () => {
    logger.error('test', { context: true })
    expect(loggedOutput).toBe('test[{"context":true},null]')
    expect(datadogLogs.logger.error).toHaveBeenCalledWith(
      'test',
      { context: true },
      undefined
    )
  })

  it('should log an error message with error', () => {
    logger.error('test', undefined, { message: 'error', name: 'Error' })
    expect(loggedOutput).toBe('test[null,{"message":"error","name":"Error"}]')
    expect(datadogLogs.logger.error).toHaveBeenCalledWith('test', undefined, {
      message: 'error',
      name: 'Error'
    })
  })
})
