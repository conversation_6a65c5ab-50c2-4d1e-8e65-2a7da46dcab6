import { datadogLogs } from '@datadog/browser-logs'
import { env } from '@/core/config'

export enum LogLevel {
  <PERSON>rro<PERSON>,
  Warn,
  Info,
  Debug
}

export const logger = {
  debug: (message: string, messageContext?: object, error?: Error) => {
    if (env.isLoggerEnabled) {
      if ((window as any).debugMode) {
        console.debug(message, messageContext, error)
        datadogLogs.logger.debug(message, messageContext, error)
      }
    }
  },
  info: (message: string, messageContext?: object, error?: Error) => {
    if (env.isLoggerEnabled) {
      if (window.debugMode) console.info(message, messageContext, error)
      datadogLogs.logger.info(message, messageContext, error)
    }
  },
  error: (message: string, messageContext?: object, error?: Error) => {
    if (env.isLoggerEnabled) {
      if (window.debugMode) console.error(message, messageContext, error)
      datadogLogs.logger.error(message, messageContext, error)
    }
  },
  warn: (message: string, messageContext?: object, error?: Error) => {
    if (env.isLoggerEnabled) {
      if (window.debugMode) console.warn(message, messageContext, error)
      datadogLogs.logger.warn(message, messageContext, error)
    }
  },
  log: (
    level: LogLevel,
    message: string,
    messageContext?: object,
    error?: Error
  ) => {
    switch (level) {
      case LogLevel.Error:
        logger.error(message, messageContext, error)
        break
      case LogLevel.Warn:
        logger.warn(message, messageContext, error)
        break
      case LogLevel.Info:
        logger.info(message, messageContext, error)
        break
      case LogLevel.Debug:
        logger.debug(message, messageContext, error)
        break
    }
  }
}
