import { JSXElementConstructor, ReactElement } from 'react'

/**
 * Utility type that takes a generic type `T` as input and returns the type of the values of the properties of `T`.
 * It does this by using the `keyof T` to access the value types of all properties in `T`.
 * This type is useful when you want to represent the value type of an object without knowing the specific property names.
 */
export type ValueOf<T> = T[keyof T]

/**
 * Let’s say you have an object with properties that each have their own objects.
 * If we use the Partial type, we are only allowing a subset of the highest level object’s keys, but not the lower level object.
 *
 * `DeepPartial` makes all nested properties optional as well.
 */
export type DeepPartial<T> = Partial<{ [P in keyof T]: DeepPartial<T[P]> }>

export type WrapperComponentType = JSXElementConstructor<{
  children: ReactElement<unknown, string | JSXElementConstructor<unknown>>
}>
