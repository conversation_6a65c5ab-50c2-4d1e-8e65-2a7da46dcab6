import {
  IPresetShareAccessType,
  IPresetShareType,
  IPresetToShare,
  IUser,
  IUserRole
} from '@/core/services/epic'
import { getPresetDummy, getPresetShareDummy } from '@/core/testing/dummies'
import { PresetUser } from '@core-clib/web-components'
import {
  getPresetToSharePayload,
  isSharedPreset,
  isUserPreset,
  isUserUnique,
  mapUserToPresetUser,
  sortUsersAlphabetically
} from './presets'

describe('utils/presets', () => {
  describe('mapUserTypeToPresetUser', () => {
    it('should map IUserType to PresetUser correctly', () => {
      // given
      const input: IUser = {
        EmployeeFirstName: 'John',
        EmployeeLastName: 'Doe',
        ADUserID: 'jdoe123'
      }

      // when
      const result = mapUserToPresetUser(input)

      // then
      expect(result).toEqual({
        id: 'jdoe123',
        name: '<PERSON>'
      })
    })

    it('should handle empty names correctly', () => {
      // given
      const input: IUser = {
        EmployeeFirstName: '',
        EmployeeLastName: '',
        ADUserID: 'empty123'
      }

      // when

      const result = mapUserToPresetUser(input)
      // then
      expect(result).toEqual({
        id: 'empty123',
        name: ' '
      })
    })
  })

  describe('isUserUnique', () => {
    it('should filter out duplicate users', () => {
      // given
      const users: PresetUser[] = [
        { id: 'user1', name: 'John Doe' },
        { id: 'user2', name: 'Jane Smith' },
        { id: 'user1', name: 'John Doe' }
      ]

      // when
      const filteredUsers = users.filter(isUserUnique)

      // then
      expect(filteredUsers).toEqual([
        { id: 'user1', name: 'John Doe' },
        { id: 'user2', name: 'Jane Smith' }
      ])
    })

    it('should not remove any users if there are no duplicates', () => {
      // given
      const users: PresetUser[] = [
        { id: 'user1', name: 'John Doe' },
        { id: 'user2', name: 'Jane Smith' },
        { id: 'user3', name: 'Alice Johnson' }
      ]

      // when
      const filteredUsers = users.filter(isUserUnique)

      // then
      expect(filteredUsers).toEqual([
        { id: 'user1', name: 'John Doe' },
        { id: 'user2', name: 'Jane Smith' },
        { id: 'user3', name: 'Alice Johnson' }
      ])
    })
  })

  describe('sortUsersAlphabetically', () => {
    it('should correctly sort users alphabetically by name', () => {
      // given
      const users: PresetUser[] = [
        { id: 'user1', name: 'John Doe' },
        { id: 'user2', name: 'Jane Smith' },
        { id: 'user3', name: 'Alice Johnson' }
      ]

      // when
      const sortedUsers = users.sort(sortUsersAlphabetically)

      // then
      expect(sortedUsers).toEqual([
        { id: 'user3', name: 'Alice Johnson' },
        { id: 'user2', name: 'Jane Smith' },
        { id: 'user1', name: 'John Doe' }
      ])
    })

    it('should handle users with the same name', () => {
      // given
      const users: PresetUser[] = [
        { id: 'user1', name: 'John Doe' },
        { id: 'user2', name: 'Jane Smith' },
        { id: 'user3', name: 'John Doe' }
      ]

      // when
      const sortedUsers = users.sort(sortUsersAlphabetically)

      // then
      expect(sortedUsers).toEqual([
        { id: 'user2', name: 'Jane Smith' },
        { id: 'user1', name: 'John Doe' },
        { id: 'user3', name: 'John Doe' }
      ])
    })
  })
})

describe('getPresetToSharePayload', () => {
  it('should return the expected request body', () => {
    const presetId = '123'
    const expectedRequestBody: IPresetToShare = {
      Id: presetId,
      Shares: [
        {
          AccessType: IPresetShareAccessType.Read,
          Type: IPresetShareType.Public,
          Names: []
        },
        {
          AccessType: IPresetShareAccessType.Write,
          Type: IPresetShareType.AZManRole,
          Names: [IUserRole.PresetAdmin]
        }
      ]
    }

    const requestBody = getPresetToSharePayload(presetId)

    expect(requestBody).toEqual(expectedRequestBody)
  })
})

describe('isSharedPreset', () => {
  it('should return true if preset is shared', () => {
    const preset = getPresetDummy({
      SharedWith: [
        getPresetShareDummy({
          AccessType: IPresetShareAccessType.Read,
          Type: IPresetShareType.Public,
          Names: []
        })
      ]
    })

    const result = isSharedPreset(preset)

    expect(result).toBe(true)
  })

  it('should return false if SharedWidth is null', () => {
    const preset = getPresetDummy({
      SharedWith: null
    })

    const result = isSharedPreset(preset)

    expect(result).toBe(false)
  })

  it('should return false if SharedWidth is []', () => {
    const preset = getPresetDummy({
      SharedWith: []
    })

    const result = isSharedPreset(preset)

    expect(result).toBe(false)
  })
})

describe('isUserPreset', () => {
  it('should return false if preset is shared public preset', () => {
    const preset = getPresetDummy({
      SharedWith: [
        getPresetShareDummy({
          AccessType: IPresetShareAccessType.Read,
          Type: IPresetShareType.Public,
          Names: []
        })
      ]
    })

    const result = isUserPreset(preset)

    expect(result).toBe(false)
  })

  it('should return true if SharedWidth is null', () => {
    const preset = getPresetDummy({
      SharedWith: null
    })

    const result = isUserPreset(preset)

    expect(result).toBe(true)
  })

  it('should return true if SharedWidth is []', () => {
    const preset = getPresetDummy({
      SharedWith: []
    })

    const result = isUserPreset(preset)

    expect(result).toBe(true)
  })
})
