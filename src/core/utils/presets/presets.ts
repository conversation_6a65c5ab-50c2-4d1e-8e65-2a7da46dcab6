import {
  ClibPreset,
  ClibPresetToCreate,
  IPreset,
  IPresetsGroupId,
  IPresetShare,
  IPresetShareAccessType,
  IPresetShareType,
  IPresetState,
  IPresetToShare,
  IUser,
  IUserRole,
  IUsersByRole
} from '@/core/services/epic'
import { IColumnInfo } from '@/core/services/riskReport'
import { ColumnState } from '@ag-grid-community/core'
import { PresetUser } from '@core-clib/web-components'

export const hasAdminRole = (userId: string, usersByRole: IUsersByRole) => {
  return usersByRole[IUserRole.PresetAdmin].some(
    ({ ADUserID }) => ADUserID === userId
  )
}

export const mapUserToPresetUser = (user: IUser): PresetUser => {
  return {
    id: user.ADUserID,
    name: `${user.EmployeeFirstName} ${user.EmployeeLastName}`
  }
}

export const isUserUnique = (
  user: PresetUser,
  index: number,
  users: PresetUser[]
) => {
  // Find the first index of a user with the same 'id' as the current user
  return users.findIndex((u) => u.id === user.id) === index // Keep the user if the first index is the same as the current index (i.e., it's not a duplicate)
}

export const isPresetAvailable = (preset: IPreset): boolean => {
  return !preset.IsDeleted
}

export const isUserPreset = (preset: IPreset): boolean =>
  !isSharedPreset(preset)

export const isSharedPreset = (preset: IPreset): boolean => {
  const isSharePublic = (share: IPresetShare) =>
    share.Type === IPresetShareType.Public
  return !!preset.SharedWith?.some(isSharePublic)
}

export const mapPresetToClibPreset = (preset: IPreset): ClibPreset => {
  const { Data, Id, PresetName } = preset
  const { state } = Data
  return {
    ...Data,
    state,
    id: Id,
    name: PresetName
  }
}

export const mapPresetToClibSharedPreset = (preset: IPreset): ClibPreset => {
  return {
    ...mapPresetToClibPreset(preset),
    groupId: IPresetsGroupId.Shared
  }
}

export const clearClibPresetFromSystemData = (
  preset: ClibPreset,
  columnInfo: IColumnInfo[]
): ClibPreset => {
  const { state, ...rest } = preset

  return {
    ...rest,
    state: {
      ...state,
      columnState: filterOutSystemDataColumns(state.columnState, columnInfo)
    }
  }
}

export const clearClibPresetsFromSystemData = (
  presets: ClibPreset[],
  columnInfo: IColumnInfo[]
): ClibPreset[] => {
  return presets.map((preset) =>
    clearClibPresetFromSystemData(preset, columnInfo)
  )
}

export const customizePreset = (
  preset: ClibPresetToCreate,
  customState: Partial<IPresetState>
): ClibPresetToCreate => {
  const { state, ...rest } = preset
  return {
    ...rest,
    state: {
      ...state,
      ...customState
    }
  }
}

export const extractSystemColumnIds = (columnInfo: IColumnInfo[]): string[] => {
  return columnInfo.reduce<string[]>((previousValue, currentValue) => {
    if (currentValue.isSystemData) {
      return [...previousValue, currentValue.column]
    }
    return previousValue
  }, [])
}

export const filterOutSystemDataColumns = (
  columnState: ColumnState[],
  columnInfo: IColumnInfo[]
): ColumnState[] => {
  const systemColumnIds = extractSystemColumnIds(columnInfo)

  const isNonSystemColumn = (column: ColumnState) => {
    return !systemColumnIds.includes(column.colId)
  }

  return columnState.filter(isNonSystemColumn)
}

export const sortUsersAlphabetically = (
  user1: PresetUser,
  user2: PresetUser
): number => {
  return user1.name.localeCompare(user2.name)
}

export const getPresetToSharePayload = (presetId: string): IPresetToShare => {
  return {
    Id: presetId,
    Shares: [
      {
        AccessType: IPresetShareAccessType.Read,
        Type: IPresetShareType.Public,
        Names: []
      },
      {
        AccessType: IPresetShareAccessType.Write,
        Type: IPresetShareType.AZManRole,
        Names: [IUserRole.PresetAdmin]
      }
    ]
  }
}
