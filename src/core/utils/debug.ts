// from https://stackoverflow.com/questions/55187563/determine-which-dependency-array-variable-caused-useeffect-hook-to-fire
import { useEffect, useRef } from 'react'
import { logger } from './logger'

const usePrevious = (value: any, initialValue: any) => {
  const ref = useRef(initialValue)
  useEffect(() => {
    ref.current = value
  })
  return ref.current
}

export function useEffectDebugger(
  effectHook: any,
  dependencies: any,
  dependencyNames = []
): any {
  const previousDeps = usePrevious(dependencies, [])

  const changedDeps = dependencies.reduce(
    (accum: any, dependency: any, index: number) => {
      if (dependency !== previousDeps[index]) {
        const keyName = dependencyNames[index] || index
        return {
          ...accum,
          [keyName]: {
            before: previousDeps[index],
            after: dependency
          }
        }
      }

      return accum
    },
    {}
  )

  if (Object.keys(changedDeps).length) {
    logger.debug('[use-effect-debugger] ', changedDeps)
  }

  useEffect(effectHook, dependencies)
}
