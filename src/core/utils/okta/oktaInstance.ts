import { env } from '@/core/config'
import OktaAuth from '@okta/okta-auth-js'
import { redirectUriFromLocation } from './restoreRedirectFromLocation'

const okta = env.isOkta
  ? new OktaAuth({
      issuer: env.oktaIssuerUri,
      clientId: env.oktaClientId,
      redirectUri: redirectUriFromLocation(),
      pkce: false,
      tokenManager: {
        autoRenew: true
      }
    })
  : undefined

export { okta }
