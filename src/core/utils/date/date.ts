import { format, isBefore, parseISO, sub } from 'date-fns'
import { logger } from '../logger'

const FRACTIONED_SECS_TEMPLATE = '0000000'

export function formatTimestampToUTCWithMilliseconds(
  timestamp: string
): string {
  if (!timestamp) {
    return new Date().toISOString()
  }

  const [date, fullTime] = timestamp.split('T')
  const [time, milliseconds] = fullTime.split('.')
  const clearedFractionSeconds = parseInt(milliseconds)

  return `${date} ${time}.${
    isNaN(clearedFractionSeconds)
      ? FRACTIONED_SECS_TEMPLATE
      : `${clearedFractionSeconds}`.padEnd(7, '0')
  }`
}

export function formatTimestampToLocalWithMilliseconds(
  utcString: string
): string {
  const date = utcString ? new Date(utcString) : new Date()

  const milliseconds = utcString?.split('.').at(-1) ?? FRACTIONED_SECS_TEMPLATE
  const clearedFractionSeconds = parseInt(milliseconds)

  const formattedLocalTimestamp = `${format(
    date,
    'yyyy-MM-dd HH:mm:ss'
  )}.${clearedFractionSeconds.toString().padEnd(7, '0')}`

  return formattedLocalTimestamp
}

export function formatDateToLocal(date: Date): string {
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: 'numeric',
    minute: 'numeric',
    second: 'numeric',
    hour12: true
  })
}

export function formatDateToUtc(date: Date): string {
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: 'numeric',
    minute: 'numeric',
    second: 'numeric',
    hour12: true,
    timeZone: 'UTC'
  })
}

export const getDateOlderThanMinutesPredicate =
  (minutes: number) =>
  (isoString: string): boolean => {
    const isoDate = parseISO(isoString)
    const fiveMinutesAgo = sub(new Date(), { minutes })

    return isBefore(isoDate, fiveMinutesAgo)
  }

export const isDateOlderThanFiveMinutes = getDateOlderThanMinutesPredicate(5)
export const isDateOlderThanFifteenMinutes =
  getDateOlderThanMinutesPredicate(15)

/**
 * Formats a date string to ISO string format, truncated to exclude seconds.
 * If the input string is not a valid date or is empty, a fallback value is used.
 * The fallback is either the current date in ISO format or a custom string provided by the caller.
 * @param {string} currentAsOfLocal - The date string to format.
 * @param {string} [fallback=new Date().toISOString()] - The fallback value to use if input is invalid.
 * @returns {string} The formatted date string or the fallback value.
 */
export const formatDateToISOString = (
  dateString: string,
  fallback = new Date().toISOString()
): string => {
  if (!dateString) {
    return fallback
  }

  let formattedDate = fallback

  try {
    formattedDate = format(new Date(dateString), "yyyy-MM-dd'T'HH:mm:ss")
  } catch (error) {
    logger.warn('Error formatting date to ISO string: ', { dateString })
  }

  return formattedDate
}

export const getLocalMidnight = (currentAsOfUtc: string): string => {
  const date = new Date(currentAsOfUtc)
  date.setHours(0, 0, 0, 0)
  return date.toISOString()
}

export const padTimeFigures = (figures: number) => {
  return String(figures).padStart(2, '0')
}

export const formatDateToISOStringWithZeroMilliseconds = (value: string) =>
  `${format(new Date(value), 'yyyy-MM-dd')}T00:00:00.000Z`

export const preserveTime = (
  oldDate: Date | undefined,
  newDate: Date | undefined
) => {
  if (!oldDate || !newDate) return newDate
  newDate.setHours(oldDate.getHours())
  newDate.setMinutes(oldDate.getMinutes())
  newDate.setSeconds(oldDate.getSeconds())
  return newDate
}

export const formatDateAsString = (date: Date) => {
  return `${date.getUTCFullYear()}-${(date.getUTCMonth() + 1)
    .toString()
    .padStart(2, '0')}-${date.getUTCDate().toString().padStart(2, '0')} ${date
    .getUTCHours()
    .toString()
    .padStart(2, '0')}:${date
    .getUTCMinutes()
    .toString()
    .padStart(2, '0')}:${date.getUTCSeconds().toString().padStart(2, '0')}`
}
