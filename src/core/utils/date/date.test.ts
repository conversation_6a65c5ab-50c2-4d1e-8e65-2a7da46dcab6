import { formatISO, sub } from 'date-fns'
import { vi } from 'vitest'
import {
  formatDateAsString,
  formatDateToISOString,
  formatDateToISOStringWithZeroMilliseconds,
  formatDateToLocal,
  formatTimestampToLocalWithMilliseconds,
  formatTimestampToUTCWithMilliseconds,
  getDateOlderThanMinutesPredicate,
  isDateOlderThanFifteenMinutes,
  isDateOlderThanFiveMinutes,
  padTimeFigures,
  preserveTime
} from './date'

describe('utils/date', () => {
  let RealDate = Date

  beforeEach(() => {
    // We cant relay on System's current time, which can change between
    // the time it's captured and the time it's used for comparison.
    // To make time related tests reliable, we mock the Date object
    // to always return a specific time
    RealDate = Date
    // casting the global Date object to any before assigning the mock class
    // (TypeScript is expecting a DateConstructor type for the global Date object)
    ;(global.Date as unknown) = class extends RealDate {
      constructor() {
        super()
        return new RealDate(2020, 5, 27, 0, 0, 0)
      }
    }
  })

  afterEach(() => {
    // restore the original implementation of Date
    global.Date = RealDate
  })

  describe('formatTimestampToUTCWithMilliseconds', () => {
    it('formatTimestampToUTCWithMilliseconds should return correct utc timestamp', () => {
      const timestamp = '2021-07-20T12:00:00.0000000Z'
      const expected = '2021-07-20 12:00:00.0000000'
      const result = formatTimestampToUTCWithMilliseconds(timestamp)

      expect(result).toEqual(expected)
    })

    it('should pad zeros to milliseconds', () => {
      const timestamp = '2021-07-20T12:00:00.123Z'
      const expected = '2021-07-20 12:00:00.1230000'
      const result = formatTimestampToUTCWithMilliseconds(timestamp)

      expect(result).toEqual(expected)
    })

    it('should put 7 zeroes if milliseconds is NaN', () => {
      const timestamp = '2021-07-20T12:00:00.abc'
      const expected = '2021-07-20 12:00:00.0000000'
      const result = formatTimestampToUTCWithMilliseconds(timestamp)

      expect(result).toEqual(expected)
    })

    // FLAKY TEST
    it('should return current date for empty string', () => {
      const date = new Date()
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      vi.spyOn(Date, 'constructor').mockReturnValue(date)
      const timestamp = ''
      const expected = new Date().toISOString()
      const result = formatTimestampToUTCWithMilliseconds(timestamp)

      expect(result).toEqual(expected)
    })
  })

  describe('formatLocalTimestamp', () => {
    it('formatLocalTimestamp should return correct local timestamp', () => {
      const utcString = '2021-07-20T12:00:00.1234567Z'
      const result = formatTimestampToLocalWithMilliseconds(utcString)
      const expected = '1234567'

      expect(
        result.match(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d+/)
      ).toBeTruthy()
      expect(result.split('.')[1]).toBe(expected)
    })
  })

  describe('formatDateToISOStringWithZeroMilliseconds', () => {
    it('should return date in string format with 00:00:00.000Z at the end', () => {
      const date = '2020-05-27 00:00:00.0000000'
      const expected = '2020-06-26T00:00:00.000Z'
      const result = formatDateToISOStringWithZeroMilliseconds(date)

      expect(result).toEqual(expected)
    })
  })
})

describe('formatDateToLocal', () => {
  it('should return a formatted string with year, month, day, hour, minute and second in 12-hour format when passed a valid date object', () => {
    const date = new Date('2022-01-01T12:34:56Z')

    const formattedDate = formatDateToLocal(date)

    expect(formattedDate).toContain('01/01/2022')
  })
})

describe('Date predicates', () => {
  it('getDateOlderThanMinutesPredicate returns true for dates older than specified minutes', () => {
    const olderDate = formatISO(sub(new Date(), { minutes: 10 }))
    const isDateOlderThanTenMinutes = getDateOlderThanMinutesPredicate(10)
    expect(isDateOlderThanTenMinutes(olderDate)).toBe(true)
  })

  it('getDateOlderThanMinutesPredicate returns false for dates newer than specified minutes', () => {
    const newerDate = formatISO(sub(new Date(), { minutes: 5 }))
    const isDateOlderThanTenMinutes = getDateOlderThanMinutesPredicate(10)
    expect(isDateOlderThanTenMinutes(newerDate)).toBe(false)
  })

  it('isDateOlderThanFiveMinutes returns false for dates newer than 5 minutes', () => {
    const newerDate = formatISO(sub(new Date(), { minutes: 2 }))
    expect(isDateOlderThanFiveMinutes(newerDate)).toBe(false)
  })

  it('isDateOlderThanFifteenMinutes returns false for dates newer than 15 minutes', () => {
    const newerDate = formatISO(sub(new Date(), { minutes: 10 }))
    expect(isDateOlderThanFifteenMinutes(newerDate)).toBe(false)
  })
})

describe('formatDateToISOString', () => {
  it('should format a valid date string to ISO string format, truncated to exclude milliseconds', () => {
    const input = '2022-01-01T12:34:56.123'
    const expectedOutput = '2022-01-01T12:34:56'
    const result = formatDateToISOString(input)
    expect(result).toBe(expectedOutput)
  })

  it('should handle date strings in different formats, as long as they are valid', () => {
    const input = '01/01/2022 12:34 PM'
    const expectedOutput = '2022-01-01T12:34:00'
    const result = formatDateToISOString(input)
    expect(result).toBe(expectedOutput)
  })

  it('should handle leap years correctly', () => {
    const input = '2024-02-29T12:34:56'
    const expectedOutput = '2024-02-29T12:34:56'
    const result = formatDateToISOString(input)
    expect(result).toBe(expectedOutput)
  })

  it('should return the current date if no input is provided', () => {
    const expectedOutputTimeMs = new Date().getTime()
    const resultTimeMs = new Date(formatDateToISOString('')).getTime()
    const diff = resultTimeMs - expectedOutputTimeMs
    expect(diff).toBeLessThan(5)
  })

  it('should return the current date if the input is invalid', () => {
    const expectedOutputTimeMs = new Date().getTime()
    const resultTimeMs = new Date(
      formatDateToISOString('invalid date')
    ).getTime()
    const diff = resultTimeMs - expectedOutputTimeMs
    expect(diff).toBeLessThan(5)
  })

  it('should return the custom fallback if the input is not a valid date string', () => {
    const result = formatDateToISOString('not a valid date', 'fallback')
    expect(result).toBe('fallback')
  })
})

describe('test padTimesFigures', () => {
  it('should add 0 to single figure', () => {
    expect(padTimeFigures(9)).toBe('09')
  })

  it('should not add 0 to double figure', () => {
    expect(padTimeFigures(11)).toBe('11')
  })
})

describe('preserveTime', () => {
  it('should return new date with the same time as the old date', () => {
    const oldDate = new Date('2022-01-01T12:34:56')
    const newDate = new Date('2022-02-02T00:00:00')

    const result = preserveTime(oldDate, newDate)

    expect(result).toEqual(new Date('2022-02-02T12:34:56'))
  })
})

describe('formatRegionalDateString', () => {
  it('should format the date correctly', () => {
    const regionalDate = new Date('2025-01-25T17:59:00')
    const formattedDate = formatDateAsString(regionalDate)
    expect(formattedDate).toBe('2025-01-25 16:59:00')
  })
})
