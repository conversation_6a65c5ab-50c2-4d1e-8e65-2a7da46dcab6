const isNumeric = (str: string) => !isNaN(parseInt(str))

export const camelCaseToKebabCase = (str: string): string =>
  str
    .split('')
    .map((char, index) => {
      const prevChar = index == 0 ? undefined : str[index - 1]
      const numberSwitch =
        prevChar !== undefined && isNumeric(prevChar) !== isNumeric(char)
      const insertDash = numberSwitch || !(char === char.toLowerCase())

      return insertDash ? (index !== 0 ? '-' : '') + char.toLowerCase() : char
    })
    .join('')
