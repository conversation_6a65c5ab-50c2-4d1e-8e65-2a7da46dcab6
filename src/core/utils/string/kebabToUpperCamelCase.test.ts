import { kebabToUpperCamelCase } from './kebabToUpperCamelCase'

describe('utils/report', () => {
  test('should convert reportname successfully', () => {
    expect(kebabToUpperCamelCase('pnl-live')).toBe('PnlLive')
    expect(kebabToUpperCamelCase('bbg-pnl-overlay')).toBe('BbgPnlOverlay')
    expect(kebabToUpperCamelCase('mx-72-test-pnl')).toBe('Mx72TestPnl')
  })

  test('should return null if report name is null', () => {
    expect(kebabToUpperCamelCase(null)).toBeNull()
  })
})
