import { getUserPreferencesKey } from './getUserPreferencesKey'

describe('getUserPreferencesKey', () => {
  it('should concat windoe Unique ID to Report Name', () => {
    expect(getUserPreferencesKey('pnl-live', 'unique-key')).toBe(
      'pnl-live_unique-key'
    )
  })
  it('should return Report Name without Window Id', () => {
    expect(getUserPreferencesKey('pnl-live', '')).toBe('pnl-live')
  })
})
