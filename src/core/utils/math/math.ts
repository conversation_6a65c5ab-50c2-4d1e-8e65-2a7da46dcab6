interface FormatNumberParams {
  value: number
  significantFigures?: number
  decimalDigits?: number
}

/**
 * Formats a number as a string with a specified number of decimal places.
 * Negative numbers are wrapped in parentheses.
 */
export function formatNumber({
  value,
  significantFigures,
  decimalDigits = 0
}: FormatNumberParams): string {
  if (Number(value) === 0) {
    return '0'
  }

  const wrapNumber = (result: string) =>
    value < 0 ? `(${result})` : `${result}`

  if (significantFigures) {
    const formattedValue = Math.abs(value).toLocaleString('en-US', {
      minimumSignificantDigits: significantFigures,
      maximumSignificantDigits: significantFigures
    })

    return wrapNumber(formattedValue)
  }

  const formattedValue = Math.abs(value).toLocaleString('en-US', {
    minimumFractionDigits: decimalDigits,
    maximumFractionDigits: decimalDigits
  })

  return wrapNumber(formattedValue)
}
