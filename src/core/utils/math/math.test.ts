import { formatNumber } from '@/core/utils/math'

describe('formatNumber', () => {
  describe('with decimal digits', () => {
    it.each`
      value           | decimalDigits | expected
      ${1234.5}       | ${0}          | ${'1,235'}
      ${0}            | ${4}          | ${'0'}
      ${-1234.123456} | ${3}          | ${'(1,234.123)'}
      ${-0.11111111}  | ${1}          | ${'(0.1)'}
      ${1234.1}       | ${3}          | ${'1,234.100'}
      ${1234}         | ${3}          | ${'1,234.000'}
      ${0.1}          | ${3}          | ${'0.100'}
      ${12.34}        | ${2}          | ${'12.34'}
      ${0.001234}     | ${2}          | ${'0.00'}
      ${98.76}        | ${1}          | ${'98.8'}
      ${'0'}          | ${4}          | ${'0'}
      ${'1234.12378'} | ${3}          | ${'1,234.124'}
      ${'0.11111111'} | ${3}          | ${'0.111'}
      ${'-1000'}      | ${0}          | ${'(1,000)'}
    `(
      'returns $expected when passed value="$value" and decimalDigits="$decimalDigits"',
      ({ value, decimalDigits, expected }) => {
        const result = formatNumber({ value, decimalDigits })
        expect(result).toEqual(expected)
      }
    )
  })

  describe('with significant figures', () => {
    it.each`
      value                | significantFigures | expected
      ${0.03767}           | ${5}               | ${'0.037670'}
      ${0}                 | ${4}               | ${'0'}
      ${123.4567890123}    | ${4}               | ${'123.5'}
      ${12}                | ${4}               | ${'12.00'}
      ${1.2}               | ${4}               | ${'1.200'}
      ${-1234.12345678901} | ${3}               | ${'(1,230)'}
      ${-1234.12378}       | ${3}               | ${'(1,230)'}
      ${-0.11111111}       | ${3}               | ${'(0.111)'}
      ${12.34}             | ${6}               | ${'12.3400'}
      ${0.001234}          | ${2}               | ${'0.0012'}
      ${98.76}             | ${6}               | ${'98.7600'}
      ${'0'}               | ${4}               | ${'0'}
      ${'1.23456789'}      | ${4}               | ${'1.235'}
      ${'-123456789'}      | ${4}               | ${'(123,500,000)'}
      ${'1234567.890123'}  | ${4}               | ${'1,235,000'}
    `(
      'should return $expected when passed value="$value" and significantFigures="$significantFigures"',
      ({ value, significantFigures, expected }) => {
        const result = formatNumber({ value, significantFigures })
        expect(result).toBe(expected)
      }
    )
  })
})
