import { useParams } from 'react-router-dom'
import { kebabToUpperCamelCase, camelCaseToKebabCase } from '../string'

export const useGetReportNameFromRoute = () => {
  const { reportName } = useParams<{ reportName: string }>()
  return kebabToUpperCamelCase(reportName)
}

export const useReportNameFormatIsValid = () => {
  const { reportName: rawReportName } = useParams<{ reportName: string }>()
  const camelCaseReportName = kebabToUpperCamelCase(rawReportName)
  if (camelCaseReportName == null) return false
  return rawReportName == camelCaseToKebabCase(camelCaseReportName)
}

export const getParamFromUrl = (param: string): string | null => {
  return new URLSearchParams(window.location.search).get(param)
}
