// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Routes file > should have the correct routes 1`] = `
{
  "_internalActiveDeferreds": Map {},
  "_internalFetchControllers": Map {},
  "_internalSetRoutes": [Function],
  "basename": "/",
  "createHref": [Function],
  "deleteBlocker": [Function],
  "deleteFetcher": [Function],
  "dispose": [Function],
  "enableScrollRestoration": [Function],
  "encodeLocation": [Function],
  "fetch": [Function],
  "getBlocker": [Function],
  "getFetcher": [Function],
  "initialize": [Function],
  "navigate": [Function],
  "revalidate": [Function],
  "routes": [
    {
      "children": undefined,
      "element": <ReportErrorPage />,
      "errorElement": <ReportErrorPage />,
      "hasErrorBoundary": true,
      "id": "0",
      "path": "/",
    },
    {
      "children": undefined,
      "element": <AppContainer />,
      "errorElement": <ReportErrorPage />,
      "hasErrorBoundary": true,
      "id": "1",
      "path": "/reports/:reportName",
    },
  ],
  "state": {
    "actionData": null,
    "blockers": Map {},
    "errors": null,
    "fetchers": Map {},
    "historyAction": "POP",
    "initialized": true,
    "loaderData": {},
    "location": {
      "hash": "",
      "key": "default",
      "pathname": "/",
      "search": "",
      "state": null,
    },
    "matches": [
      {
        "params": {},
        "pathname": "/",
        "pathnameBase": "/",
        "route": {
          "children": undefined,
          "element": <ReportErrorPage />,
          "errorElement": <ReportErrorPage />,
          "hasErrorBoundary": true,
          "id": "0",
          "path": "/",
        },
      },
    ],
    "navigation": {
      "formAction": undefined,
      "formData": undefined,
      "formEncType": undefined,
      "formMethod": undefined,
      "json": undefined,
      "location": undefined,
      "state": "idle",
      "text": undefined,
    },
    "preventScrollReset": false,
    "restoreScrollPosition": null,
    "revalidation": "idle",
  },
  "subscribe": [Function],
  "window": [Object],
}
`;
