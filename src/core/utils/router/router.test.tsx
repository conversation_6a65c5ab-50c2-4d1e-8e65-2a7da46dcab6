import { vi } from 'vitest'
import { setUrlParam } from '@/core/testing/mocks'
import {
  getParamFromUrl,
  useGetReportNameFromRoute,
  useReportNameFormatIsValid
} from '.'

const { mockedUseParamsMethod } = vi.hoisted(() => {
  return { mockedUseParamsMethod: vi.fn() }
})

vi.mock('react-router-dom', () => {
  const actual = vi.importActual('react-router-dom')
  return {
    ...actual,
    useParams: mockedUseParamsMethod
  }
})

describe('router', () => {
  afterEach(() => {
    vi.resetAllMocks()
  })

  it('Should get the reportName from the useParams called pnl-live', async () => {
    // given
    mockedUseParamsMethod.mockReturnValue({ reportName: 'pnl-live' })

    // when
    const { useParams } = await import('react-router-dom')

    // then
    expect(useParams()).toEqual({ reportName: 'pnl-live' })
  })

  it('should return param value from URL', () => {
    // given
    const param = 'testParam'
    const value = 'testValue'
    setUrlParam(param, value)

    // when
    const result = getParamFromUrl(param)

    // then
    expect(result).toBe(value)
  })

  it('should return report name in UpperCamelCase', async () => {
    // given
    mockedUseParamsMethod.mockReturnValue({ reportName: 'pnl-live' })

    // when
    const reportName = useGetReportNameFromRoute()

    // then
    expect(reportName).toEqual('PnlLive')
  })

  it('should validate report name to be in kebab-case', async () => {
    // given
    mockedUseParamsMethod.mockReturnValue({ reportName: 'pnl-live' })

    // when
    const isValid = useReportNameFormatIsValid()

    // then
    expect(isValid).toEqual(true)
  })

  it('should throw if report name is not in kebab-case', async () => {
    // given
    mockedUseParamsMethod.mockReturnValue({ reportName: 'PnlLive' })

    // when
    const isValid = useReportNameFormatIsValid()

    // then
    expect(isValid).toEqual(false)
  })
})
