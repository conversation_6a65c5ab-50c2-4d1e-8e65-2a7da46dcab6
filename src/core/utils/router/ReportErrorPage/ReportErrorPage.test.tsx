import { render } from '@testing-library/react'
import { MemoryRouter } from 'react-router-dom'
import { vi } from 'vitest'
import ReportErrorPage from '.'

vi.mock('react-router-dom', () => {
  const actual = vi.importActual('react-router-dom')
  return {
    ...actual,
    MemoryRouter: ({ children }: { children: React.ReactNode }) => children,
    useParams: () => ({ reportName: 'pnl-live' }),
    useRouteError: () => ({
      statusText: 'test error status text',
      message: 'test message'
    }),
    Link: ({ children }: { children: React.ReactNode }) => children
  }
})

describe('ReportErrorPage', () => {
  it('should display the error page', () => {
    const { getByText } = render(
      <MemoryRouter initialEntries={['reports/error']}>
        <ReportErrorPage />
      </MemoryRouter>
    )

    expect(getByText('Something went wrong!')).toBeVisible()
  })
  it('should display the error page with the error message', () => {
    const { getByText } = render(
      <MemoryRouter initialEntries={['reports/error']}>
        <ReportErrorPage />
      </MemoryRouter>
    )

    // test useRouteError
    expect(getByText('test error status text')).toBeVisible()
  })
})
