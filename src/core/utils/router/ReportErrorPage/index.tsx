import { Link, useRouteError } from 'react-router-dom'
import './style.scss'
interface RouteError {
  statusText?: string
  message?: string
}
export default function ReportErrorPage() {
  const error = useRouteError() as RouteError

  return (
    <section id="error-page" className="error-wrapper">
      <div>
        <h1>Something went wrong!</h1>
        <p>
          We couldn't find the report you're looking for. Please make sure
          you're using a supported report name.
        </p>
        <p>
          <i>{error.statusText || error.message}</i>
        </p>
        <Link to="/reports/pnl-live">
          Try with a valid report like "pnl-live"
        </Link>
      </div>
    </section>
  )
}
