import { debounce } from 'lodash'
import { DependencyList, useCallback, useEffect } from 'react'
import { appConfig } from '../config'
import { useAppSelect } from '../redux/hooks'

export const useAfterRefreshEffect = (
  callback: () => void,
  deps: DependencyList = [],
  debounceTimeMs = appConfig.afterRefreshDebounceTimeMs
) => {
  const isLoading = useAppSelect((state) => state.notifications.isLoading)

  const debouncedCallback = useCallback(
    debounce(callback, debounceTimeMs),
    deps
  )

  useEffect(() => {
    // TODO replace with native onModelUpdated agGrid option
    if (!isLoading) {
      debouncedCallback()
    }
  }, [isLoading, ...deps])
}
