import path from 'node:path'
import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import tsconfigPaths from 'vite-tsconfig-paths'
import EnvironmentPlugin from 'vite-plugin-environment'

export default defineConfig({
  plugins: [
    tsconfigPaths(),
    react(),
    EnvironmentPlugin('all', { prefix: 'VITE_' })
  ],
  test: {
    globals: true,
    setupFiles: path.resolve(__dirname, './test/setup.ts'),
    environmentMatchGlobs: [
      ['**/*.test.tsx', 'jsdom'],
      ['**/*.test.ts', 'jsdom']
    ],
    coverage: {
      reporter: ['lcov']
    },
    reporters: ['vitest-sonar-reporter', 'verbose'],
    outputFile: {
      'vitest-sonar-reporter': 'sonar-report.xml'
    },
    server: {
      deps: {
        inline: [/core-clib/],
        fallbackCJS: true
      }
    }
  }
})
