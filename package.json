{"name": "m72ui", "version": "0.2.0", "private": true, "scripts": {"dev": "vite --host", "tsc": "tsc -p tsconfig.build.json", "build": "yarn tsc && vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives", "preview": "vite preview --port 8082", "test": "cross-env TZ='CET' vitest run --coverage", "test:coverage": "cross-env TZ='CET' vitest run --coverage", "test:watch": "cross-env TZ='CET' vitest --reporter=basic", "test:ci": "cross-env TZ='CET' vitest run", "test:ui": "cross-env TZ='CET' vitest --ui", "prettier": "prettier --write \"**/*.{js,ts,jsx,tsx}\"", "postinstall": "husky install"}, "dependencies": {"@ag-grid-community/client-side-row-model": "30.2.1", "@ag-grid-community/core": "30.2.1", "@ag-grid-community/react": "30.2.1", "@ag-grid-community/styles": "30.2.1", "@ag-grid-enterprise/clipboard": "30.2.1", "@ag-grid-enterprise/column-tool-panel": "30.2.1", "@ag-grid-enterprise/core": "30.2.1", "@ag-grid-enterprise/filter-tool-panel": "30.2.1", "@ag-grid-enterprise/menu": "30.2.1", "@ag-grid-enterprise/range-selection": "30.2.1", "@ag-grid-enterprise/row-grouping": "30.2.1", "@ag-grid-enterprise/server-side-row-model": "30.2.1", "@ag-grid-enterprise/set-filter": "30.2.1", "@ag-grid-enterprise/side-bar": "30.2.1", "@ag-grid-enterprise/status-bar": "30.2.1", "@core-clib/ag-grid": "1.122.3", "@core-clib/assets": "1.122.3", "@core-clib/react": "1.122.3", "@core-clib/utils": "1.122.3", "@core-clib/web-components": "1.122.3", "@datadog/browser-logs": "5.7.0", "@datadog/browser-rum": "^5.2.0", "@epic/window": "^0.0.94", "@glue42/desktop": "^5.23.1", "@okta/okta-auth-js": "7.7.0", "@okta/okta-react": "6.9.0", "@reduxjs/toolkit": "^1.9.7", "axios": "^1.5.1", "axios-retry": "^4.0.0", "classnames": "2.5.1", "cross-env": "^7.0.3", "date-fns": "^2.30.0", "lodash": "4.17.21", "moment-timezone": "0.5.46", "react": "18.2.0", "react-dom": "18.2.0", "react-redux": "^8.1.0", "react-router-dom": "^6.19.0", "rxjs": "^7.8.1", "socket.io-client": "2.3.0", "typescript": "5.1.3", "uuid": "^9.0.0"}, "devDependencies": {"@core-clib/shared-types": "1.95.17", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.1.2", "@types/lodash": "^4.14.198", "@types/react": "^18.0.37", "@types/react-dom": "^18.0.11", "@types/socket.io-client": "1.4.36", "@types/uuid": "^9.0.3", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "@vitejs/plugin-react": "^4.3.1", "@vitejs/plugin-react-swc": "^3.7.0", "@vitest/coverage-v8": "2.1.1", "@vitest/ui": "2.1.1", "@welldone-software/why-did-you-render": "^7.0.1", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "husky": "^8.0.0", "jsdom": "^22.1.0", "msw": "^1.3.2", "node-fetch": "^3.3.2", "prettier": "^2.8.8", "redux-mock-store": "^1.5.5", "sass": "^1.63.4", "vite": "^4.3.9", "vite-plugin-environment": "^1.1.3", "vite-tsconfig-paths": "^4.2.2", "vitest": "2.1.1", "vitest-sonar-reporter": "^2.0.0"}}