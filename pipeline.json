{"app_version": "1.4.6", "team": "MACROUI", "project": "mstrat", "application_id": "1054972", "project_type": "nodejs", "pc_number": "MACROUIUX-2242", "debug": false, "continuous_delivery": true, "TeamDL": "<EMAIL>", "widget": false, "app_name": "m72ui", "target_ocp_cluster": "ocp414B", "nodejs_version": "22.5.1", "vitest": true, "sonar_sources": "src/", "sonar_test_sources": "src/", "sonar_test_exclusions": "src/components/App/App.test.tsx,src/**/*.test.ts", "sonar_test_inclusions": "**/*.test.tsx", "post_deploy_action": "sample.sh"}