#!/usr/bin/env bash

#
# git-good-commit(1) - Git hook to help you write good commit messages.
# Released under the MIT License.
#
# Version 0.6.1
#
# https://github.com/tommarshall/git-good-commit
#
#The regular expression ^MACROUIUX-[0-9]+ is used to match a specific pattern in a string.
# ^ matches the start of the string.
# MACROUIUX- matches the literal string "MACROUIUX-" exactly.
# [0-9]+ matches one or more digits (0-9).
JIRA_TICKET_REGEX="^MACROUIUX-[0-9]+"
JIRA_PREFIX="${JIRA_TICKET_REGEX}:? "
COMMIT_MSG_FILE="$1"
COMMIT_MSG_LINES=
HOOK_EDITOR=
SKIP_DISPLAY_WARNINGS=0
WARNINGS=

# enable color support
RED=
YELLOW=
BLUE=
WHITE=
NC=

set_colors() {
  local default_color=$(git config --get hooks.goodcommit.color || git config --get color.ui || echo 'auto')
  if [[ $default_color == 'always' ]] || [[ $default_color == 'true' ]] || [[ $default_color == 'auto' && -t 1 ]]; then
    RED='\033[1;31m'
    YELLOW='\033[1;33m'
    BLUE='\033[1;34m'
    WHITE='\033[1;37m'
    NC='\033[0m'
  fi
}

#
# Set the hook editor, using the same approach as git.
#

set_editor() {
  # $GIT_EDITOR appears to always be set to `:` when the hook is executed by Git?
  # ref: http://stackoverflow.com/q/41468839/885540
  # ref: https://github.com/tommarshall/git-good-commit/issues/11
  # HOOK_EDITOR=$GIT_EDITOR
  test -z "${HOOK_EDITOR}" && HOOK_EDITOR=$(git config --get core.editor)
  test -z "${HOOK_EDITOR}" && HOOK_EDITOR=$VISUAL
  test -z "${HOOK_EDITOR}" && HOOK_EDITOR=$EDITOR
  test -z "${HOOK_EDITOR}" && HOOK_EDITOR='vi'
}

#
# Output prompt help information.
#

prompt_help() {
  echo -e "${RED}$(
    cat <<-EOF
e - edit commit message
y - proceed with commit
n - abort commit
? - print help
EOF
  )${NC}"
}

#
# Add a warning with <line_number> and <msg>.
#

add_warning() {
  local line_number=$1
  local warning=$2
  WARNINGS[$line_number]="${WARNINGS[$line_number]}$warning;"
}

#
# Output warnings.
#

display_warnings() {
  if [ $SKIP_DISPLAY_WARNINGS -eq 1 ]; then
    # if the warnings were skipped then they should be displayed next time
    SKIP_DISPLAY_WARNINGS=0
    return
  fi

  for i in "${!WARNINGS[@]}"; do
    printf "%-74s ${WHITE}%s${NC}\n" "${COMMIT_MSG_LINES[$(($i - 1))]}" "[line ${i}]"
    IFS=';' read -ra WARNINGS_ARRAY <<<"${WARNINGS[$i]}"
    for ERROR in "${WARNINGS_ARRAY[@]}"; do
      echo -e " ${YELLOW}- ${ERROR}${NC}"
    done
  done
}

#
# Read the contents of the commit msg into an array of lines.
#

read_commit_message() {
  # reset commit_msg_lines
  COMMIT_MSG_LINES=()

  # read commit message into lines array
  while IFS= read -r; do

    # trim trailing spaces from commit lines
    shopt -s extglob
    REPLY="${REPLY%%*( )}"
    shopt -u extglob

    # ignore all lines after cut line
    [[ $REPLY == "# ------------------------ >8 ------------------------" ]]
    test $? -eq 1 || break

    # ignore comments
    [[ $REPLY =~ ^# ]]
    test $? -eq 0 || COMMIT_MSG_LINES+=("$REPLY")

  done < <(cat $COMMIT_MSG_FILE)
}

#
# Validate the contents of the commit msg against the good commit guidelines.
#

validate_commit_message() {
  # reset warnings
  WARNINGS=()

  # capture the subject, and remove the 'squash! ' prefix if present
  COMMIT_SUBJECT=${COMMIT_MSG_LINES[0]/#squash! /}

  # remove Jira ticket from the beginning
  COMMIT_SUBJECT_STRIPPED_JIRA=$(sed -E "s/${JIRA_PREFIX}//" <<<"${COMMIT_SUBJECT}")

  # if the commit is empty there's nothing to validate, we can return here
  COMMIT_MSG_STR="${COMMIT_MSG_LINES[*]}"
  test -z "${COMMIT_MSG_STR[*]// /}" && return

  # if the commit subject starts with 'fixup! ' there's nothing to validate, we can return here
  [[ $COMMIT_SUBJECT == 'fixup! '* ]] && return

  # do not validate merge commits
  [[ $COMMIT_SUBJECT == 'Merge '* ]] && return

  # 1. Separate subject from body with a blank line
  # ------------------------------------------------------------------------------

  test ${#COMMIT_MSG_LINES[@]} -lt 1 || test -z "${COMMIT_MSG_LINES[1]}"
  test $? -eq 0 || add_warning 2 "Separate subject from body with a blank line"

  # 2. Limit the subject line to 50 characters without Jira id
  # ------------------------------------------------------------------------------

  test "${#COMMIT_SUBJECT_STRIPPED_JIRA}" -le 50
  test $? -eq 0 || add_warning 1 "Limit the subject line without Jira id to 50 characters (${#COMMIT_SUBJECT_STRIPPED_JIRA} chars)"

  # 3. Capitalize the subject line
  # ------------------------------------------------------------------------------

  [[ ${COMMIT_SUBJECT_STRIPPED_JIRA} =~ ^[[:blank:]]*([[:upper:]]{1}[[:alnum:]]*|[[:digit:]]+)([[:blank:]]|[[:punct:]]|$) ]]
  test $? -eq 0 || add_warning 1 "Capitalize the subject line"

  # 4. Do not end the subject line with a period
  # ------------------------------------------------------------------------------

  [[ ${COMMIT_SUBJECT} =~ [^\.]$ ]]
  test $? -eq 0 || add_warning 1 "Do not end the subject line with a period"

  pt -u nocasematch

  # 5. Wrap the body at 72 characters
  # ------------------------------------------------------------------------------

  URL_REGEX='^[[:blank:]]*(https?|ftp|file|wss?|git|ssh|data|irc|dat)://[-A-Za-z0-9\+&@#/%?=~_|!:,.;]*[-A-Za-z0-9\+&@#/%=~_|]'

  for i in "${!COMMIT_MSG_LINES[@]}"; do
    LINE_NUMBER=$((i + 1))
    test "${#COMMIT_MSG_LINES[$i]}" -le 72 || [[ ${COMMIT_MSG_LINES[$i]} =~ $URL_REGEX ]]
    test $? -eq 0 || add_warning $LINE_NUMBER "Wrap the body at 72 characters (${#COMMIT_MSG_LINES[$i]} chars)"
  done

  # 6. Use the body to explain what and why vs. how
  # ------------------------------------------------------------------------------
  # TODO: implement natural language processor AI

  # 7. Do no write single worded commits
  # ------------------------------------------------------------------------------

  COMMIT_SUBJECT_WORDS=(${COMMIT_SUBJECT_STRIPPED_JIRA})
  test "${#COMMIT_SUBJECT_WORDS[@]}" -gt 1
  test $? -eq 0 || add_warning 1 "Do no write single worded commits"

  # 8. Do not start the subject line with whitespace
  # ------------------------------------------------------------------------------

  [[ ${COMMIT_SUBJECT} =~ ^[[:blank:]]+ ]]
  test $? -eq 1 || add_warning 1 "Do not start the subject line with whitespace"

  [[ ${COMMIT_SUBJECT_STRIPPED_JIRA} =~ ^[[:blank:]]+ ]]
  test $? -eq 1 || add_warning 1 "Put only one space between JIRA ticket and commit message, eg. 'MACROUIUX-1239: Add something'  or 'MACROUIUX-1239 Add something'"

  # 9. Start subject line with JIRA ticket
  # ------------------------------------------------------------------------------
  [[ ${COMMIT_SUBJECT} =~ ${JIRA_TICKET_REGEX} ]]
  test $? -eq 0 || add_warning 1 "Start subject line with JIRA ticket followed by ': ', eg. 'MACROUIUX-1239: Add something'  or 'MACROUIUX-1239 Add something'"

  # 10. Put space between JIRA ticket and commit message,
  # ------------------------------------------------------------------------------
  [[ ${COMMIT_SUBJECT} =~ ${JIRA_PREFIX} ]]
  test $? -eq 0 || add_warning 1 "Put space between JIRA ticket and commit message, eg. 'MACROUIUX-1239: Add something' or 'MACROUIUX-1239 Add something'"
}

#
# It's showtime.
#

set_colors

set_editor

if tty >/dev/null 2>&1; then
  TTY=$(tty)
else
  TTY=/dev/tty
fi

while true; do

  read_commit_message

  validate_commit_message

  # if there are no WARNINGS are empty then we're good to break out of here
  test ${#WARNINGS[@]} -eq 0 && exit 0

  display_warnings

  # if non-interactive don't prompt and exit with an error
  if [ ! -t 1 ] && [ -z ${FAKE_TTY+x} ]; then
    exit 1
  fi

  # Ask the question (not using "read -p" as it uses stderr not stdout)
  echo -en "${BLUE}Proceed with commit? [Edit/Yes/No/?]: ${NC}"

  # Read the answer
  read REPLY <"$TTY"

  # Check if the reply is valid
  case "$REPLY" in
  E* | e*)
    $HOOK_EDITOR "$COMMIT_MSG_FILE" <$TTY
    continue
    ;;
  Y*) exit 0 ;;
  N* | n*) exit 1 ;;
  *)
    SKIP_DISPLAY_WARNINGS=1
    prompt_help
    continue
    ;;
  esac

done