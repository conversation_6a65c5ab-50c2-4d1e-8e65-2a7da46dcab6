#!/bin/bash
echo "OCP_ENV = ${OCP_ENV}"

if [ "$CACHED_KEYTAB" == "" ]
then
    m=30
    n=0
    until [ $n -ge 5 ]
    do
        echo "Call /opt/container/ocp-api-client.sh"
        /opt/container/ocp-api-client.sh
        exitCode=$?
        if [ "${exitCode}" -eq "0" ]
        then
            break
        fi
        n=$[$n+1]
        echo "Attempt $n failed with ${exitCode}, sleep $m seconds"
        sleep $m
    done
else
    echo "CACHED_KEYTAB = ${CACHED_KEYTAB}"
    cp "/opt/container/${CACHED_KEYTAB}" /etc/krb5.keytab
    exitCode=0
fi

if [ "${exitCode}" -eq "0" ]
then
    echo ============================================================================
    nginx
else
	echo "ERROR: failed to get keytab file"
	exit 2
fi