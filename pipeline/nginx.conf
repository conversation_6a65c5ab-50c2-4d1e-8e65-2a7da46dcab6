worker_processes  6; # equal to number of CPU cores 8 - 2 = 6

daemon off;

error_log  /var/log/nginx/error.log debug;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log main;

    sendfile        on;

    keepalive_timeout  65;

    include /etc/nginx/conf.d/*.conf;

		# Enable Gzip
		gzip  on;
		gzip_http_version 1.0;
		gzip_comp_level 6;
		gzip_min_length 1100;
		gzip_buffers     4 8k;
		gzip_proxied any;
		gzip_types
				text/html
				text/css
				text/javascript
				text/xml
				text/plain
				text/x-component
				application/javascript
				application/json
				application/xml
				application/rss+xml
				font/truetype
				font/opentype
				application/vnd.ms-fontobject
				image/svg+xml 
				svg 
				svgz
				application/font-woff
				font/woff2;

		gzip_static on;

		gzip_proxied        expired no-cache no-store private auth;
		gzip_vary           on;

    server {
        listen 8080;
		root /data/www;
        
		location = / {
			add_header Cache-Control no-cache;
			try_files /index.html =404;
		}
		
        location / {
			try_files $uri @index;
	  	}

		location @index {
			add_header Cache-Control no-cache;
			try_files /index.html =404;
		}
   }
}
