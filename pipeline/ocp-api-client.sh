#!/bin/bash
# ----------------------------------------------------------------------------------
#  Required env vars: APPNAME, OCP_ENV
#  To do: add TOKEN as a secret for all deployment configs which need to use this
# ----------------------------------------------------------------------------------
set -x
if [ -z ${OCP_ENV} ]
then
        echo "ERROR: The OCP_ENV enviornment variable is not defined. Update deployment config."
        exit 2
else
        OCP_ENV=$(echo ${OCP_ENV} | tr [:upper:] [:lower:])
        if [ "$OCP_ENV" == "qa2" ] || [ "$OCP_ENV" == "qa3" ] || [ "$OCP_ENV" == "qa4" ]; then OCP_ENV="qa"; fi
        if [ "$OCP_ENV" == "uat" ]; then OCP_ENV="uat"; fi
    if [ "$OCP_ENV" == "dev2" ] || [ "$OCP_ENV" == "dev3" ] || [ "$OCP_ENV" == "dev4" ]; then OCP_ENV="dev"; fi
        if [ "$OCP_ENV" == "prd" ] || [ "$OCP_ENV" == "prod2" ]; then OCP_ENV="prd"; fi
        [ "${OCP_ENV}" = "dev" ] && OCP_SUBDOMAIN="apps.nonprod-b.np-ocp.int" && TOKEN='sha256~JOupGARFw1PfMyERwtjkS1szsvvYcnlWs8bkmx5b5jg'
        [ "${OCP_ENV}" = "qa" ]  && OCP_SUBDOMAIN="apps.nonprod-b.np-ocp.int" && TOKEN='sha256~JOupGARFw1PfMyERwtjkS1szsvvYcnlWs8bkmx5b5jg'
        [ "${OCP_ENV}" = "uat" ]  && OCP_SUBDOMAIN="apps.nonprod-b.np-ocp.int" && TOKEN='sha256~JOupGARFw1PfMyERwtjkS1szsvvYcnlWs8bkmx5b5jg'
        [ "${OCP_ENV}" = "prd" ] && OCP_SUBDOMAIN="apps.prod-b.prd-ocp.int"  && TOKEN='sha256~_-Vgv6fJ-tabiHUaSYvVkgx3hkVOoJSfZUoe0fMiGpk'
 
        createKeytabFile=$(curl -s -H "X-AuthOS: ${TOKEN}" http://ocp-api.${OCP_ENV}.${OCP_SUBDOMAIN}/kerberos/check?podname=${HOSTNAME}\&ocp_env=${OCP_ENV})
        echo "${createKeytabFile}"
        if [ "$?" -gt 0 ]
        then
                echo "ERROR creating keytab file. Plesae reach <NAME_EMAIL>"
        else
                getKeytabFile=$(curl -s -H "X-AuthOS: ${TOKEN}"  http://ocp-api.${OCP_ENV}.${OCP_SUBDOMAIN}/kerberos/get?appname=${APPNAME}\&ocp_env=${OCP_ENV} > /etc/krb5.keytab)
                exitCode=$?
                if [ "${exitCode}" -eq "0" ]
                then
                        if [ -f /etc/krb5.keytab ]
                        then
                                currentEpoch=$(date +%s)
                                lastModifiedEpoch=$(stat -c "%Y" /etc/krb5.keytab)
                                if [ $((${currentEpoch}-${lastModifiedEpoch})) -lt 10 ]
                                then
                                        echo "INFO: New keytab file written /etc/krb5.keytab"
                                else
                                        echo "WARN: Existing keytab is older than 10 seconds"
                                fi
                        else
                                echo "ERROR: There is no a /etc/krb5.keytab file"
                        fi
                fi
        fi
fi