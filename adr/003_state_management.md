# Use Redux with Redux Toolkit for state management

## Status

Accepted

## Context

We are building a new web application using JavaScript, npm, TypeScript, Yarn,
and React. We need to decide on a state management library to use for the
project.

## Decision

We have decided to use Redux with Redux Toolkit for state management. Redux is a
popular state management library for JavaScript applications, and Redux Toolkit
is a set of tools and best practices that make it easier to use Redux. We chose
Redux with Redux Toolkit over other state management libraries like the native
Context API and Redux Saga. Redux Toolkit provides a clear and consistent way to
manage our state with built-in support for common Redux use cases like
asynchronous actions and immutable updates.

## Consequences

The benefits of using Redux with Redux Toolkit include:

- Clear and consistent way to manage state
- Good documentation and community support
- Easier to use than plain Redux
- Built-in support for common Redux use cases like asynchronous actions and
  immutable updates

The drawbacks of using Redux with Redux Toolkit include:

- Will require some setup and configuration
- May be overkill for small or simple applications (not our case)

## Alternatives

We considered the following alternatives:

- Native Context API: The native Context API is a built-in feature of React that
  allows you to pass data down the component tree without having to pass props
  manually. However, we found that this is not state management solution. We
  have a complex state to handle, and we want to use a library that provides a
  clear and consistent way to deal with it.
- Redux Saga: Redux Saga is a middleware library for Redux that allows you to
  handle side effects like asynchronous actions and API calls. However, we found
  that it comes with additional complexity, while we can solve some of the
  problems like race conditions with listener middleware from Redux Toolkit.

## Related ADRs

001: [Use React with TypeScript](./001_use_react_with_typescript.md)

## References

- [Redux documentation](https://redux.js.org/)
- [Redux Toolkit documentation](https://redux-toolkit.js.org/)
