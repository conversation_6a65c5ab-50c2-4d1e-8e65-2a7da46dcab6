# Code Structure and Best Practices for State Management

## Status

Approved

## Context

In the software development process, it's important to have a clear
understanding of how to structure the code and manage the state properly. The
team has discussed a few key points regarding these topics.

### Definitions

- Redux - a predictable state container for JavaScript apps
- RTK - Redux Toolkit, a set of tools and best practices for Redux development
- action - a plain JavaScript object that describes a change in state
- side effects: operations that interact with the outside world, such as API
  calls, timers, manipulating the DOM, or accessing local storage. In other
  words modifying things outside the scope of the function is a side effect.
- useEffect - is a hook that allows you to perform side effects in function
  components
- hook - special function that allows you to "hook into" React features from
  within a function component

## Decision

Based on the team's discussion, the following decisions are proposed:

### agGrid Event Handlers

- agGrid (inline) event handler functions will be separated from React hook and
  moved to a separate folder or file (`gridEventHandlers.ts` or more files if
  needed). This will help in organizing the code better and improving
  maintainability.

### State Management

- Thunks should be used for complex asynchronous logic and side effects,
  including API calls, complex synchronous logic, or dispatching multiple
  actions in a row.

- Thunks should not directly manipulate the Redux state but should dispatch
  actions that are processed by reducers to update the state.

- Action Dispatch: Actions should encapsulate the message of what should change,
  rather than simply carrying new state values. This means avoiding simple
  actions that accept parameters and blindly update state to whatever was
  passed. This approach provides better control over state changes and improves
  the understanding of why the state was updated.

- Reactive logic and testability: RTK listeners should be used for reactive
  logic, which can help organize and test more effectively. This approach has
  already been utilized in the liveMode (latestAsOf) and could be extended to
  other areas. Avoiding reactive logic in components and useEffects and relaying
  on Redux as a single source of truth will help to keep the codebase clean and
  maintainable. It should be easier to test and debug the code, since the logic
  is separated from the components.

- Use of Effects and Thunks: The team decided to use react useEffect hooks only
  for updating or re-rendering components and not for manipulating state. For
  state manipulation, actions should be dispatched, if one action is not enough,
  thunks should be used instead. This helps in separating concerns and making
  the code easier to understand and maintain. Application logic should not leak
  to custom hooks. It is better to keep hooks as simple as possible. Hooks can
  select data from the store and dispatch actions, but they should not contain
  complex logic.

### Code Reviews

The team should ensure that PRs are not accepted unless they conform to the best
practice guidelines.

## Consequences

Implementing these decisions will lead to cleaner, more maintainable code with a
clear separation of concerns. It will also lead to more effective state
management, as state updates will be handled by reducers in response to actions
dispatched by thunks. This approach will provide better control over state
changes and improve the understanding of why state was updated.

However, it may require some refactoring of existing code to conform to these
best practices. Additionally, it may require additional effort to ensure that
all PRs conform to the best practice guidelines.

The team will need to have a common understanding of these decisions and be
diligent in implementing them in their work. This will improve communication
within the team and lead to better software development outcomes.

Finally, using RTK listeners for reactive logic could help organize and test
more effectively. However, it may require additional learning and adjustment for
the team.

### Guidelines for developers

- State after Dispatch: Thunks can obtain the latest state after dispatching an
  action since dispatch is synchronous. When an action is dispatched, Redux
  immediately runs the corresponding reducer function and updates the state.
  Therefore, thunks should have minimum logic and should rely on reducers to
  update the state, but they can dispatch multiple actions in a row.
- TypeScript Union Types in Slices: The team agreed to take advantage of
  TypeScript union types in slices. This can help to simplify the code and make
  it more robust by ensuring that only valid types are used.
- Immer for Immutability: Immer is used by default by `createReducer` and
  `createSlice` RTK helpers. Immer simplify the process of updating the state in
  reducers. It allows you to write code that looks like it is mutating the
  state, but it actually creates a new immutable state behind the scenes. This
  can help to reduce boilerplate code and make the code more readable. This
  means that it is safe to write reducer code that "mutates" the state directly,
  as Immer will ensure that only draft value was mutated and new version of the
  state will be created, while the original state will remain unchanged. This
  even applies in the case reducer functions are defined outside of the
  createSlice/createReducer call. This works because RTK wraps "mutating" logic
  in Immer's produce method internally when it executes.

## Related ADRs

003_state_management.md

## References

- https://redux.js.org/usage/side-effects-approaches
- https://redux.js.org/tutorials/essentials/part-5-async-logic
- https://redux-toolkit.js.org/usage/immer-reducers
