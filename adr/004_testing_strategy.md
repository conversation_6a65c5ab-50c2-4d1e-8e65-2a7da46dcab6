# Testing strategy

## Status

Accepted

## Context

### Definitions

In the context of our testing strategy, we classify tests into four categories:
static, unit, integration, and end-to-end tests. These categories are influenced
by the Testing Trophy concept introduced by Kent C<PERSON>.

- Static Tests: These are tests conducted during the static analysis of code.
  They help identify potential issues without executing the code. Tools like
  ESLint and TypeScript fall under this category.
- Unit Tests: These tests focus on individual 'units' of code, which could be a
  function, class, or object that contains logic. Unit tests either have no
  dependencies (collaborators) or have these dependencies mocked for the test.
  In our team's context, when a reviewer refers to 'unit tests', it includes any
  automated tests executed using Jest or Vitest.
- Integration Tests: These tests evaluate the interaction between multiple units
  of code. They help ensure that different parts of the system work together as
  expected.
- End-to-End Tests: These tests validate the entire system, from the user
  interface to the database, in a real-world scenario. They aim to ensure that
  the entire application works as intended without any (or as little as
  possible) mocking in place.

#### Code coverage

Code coverage is a metric that measures the percentage of code that is covered
by tests. It is typically used to determine how well a test suite tests the
system.

### Discussion

Our team has been discussing the merits of unit testing versus integration
testing. The main argument for unit testing is that it allows us to be confident
in refactoring, as it tests individual functions in isolation. However, the
primary argument against unit testing is that it often tests implementation
rather than behavior, requiring constant updates when implementation changes.
This can result in wasted time and effort.

On the other hand, integration testing, which tests the interaction between
different parts of the system, provides a better guarantee of the software's
behavior and requires fewer changes when the implementation is updated, assuming
the behavior remains the same.

We base our reasoning on following considerations:

- Behavioral Contract: Integration tests enshrine the behavioral contract with
  the end user, effectively acting as a check for requirement interpretation.
- Efficiency: Integration tests require fewer updates when implementation
  changes, reducing the time and effort spent on maintaining tests.
- Regression Protection: Integration tests provide better protection against
  regression errors, as they test the overall behavior of the system rather than
  individual units.
- Refactoring Confidence: While unit tests provide confidence during
  refactoring, integration tests can also provide this confidence, if the Code
  coverage metric confirms that they cover a sufficient amount of code.

## Decision

We have decided to prioritize integration testing over unit testing, broadly in
line with Dodds' blog post "Write Tests. Not too many. Mostly Integration." This
means that we will write integration tests for all new features and bug fixes,
and only write unit tests when they are necessary (see principles below). We
will also write integration tests for existing features that are not currently
covered by tests.

### Principles

Our team has agreed on the following principles:

1. Confidence in Code Changes: We will ensure confidence in our code changes by
   using code coverage metrics and integrating with SonarQube. Our goal for
   coverage is 90%, and once reviewed over a period of time, this will be
   enforced by CI.
2. Test Stability: Tests should be written in a way that they do not need to be
   changed if the contract does not change. Write Tests. Not too many. Mostly
   Integration.
3. Core Library Coverage: Library functions (in the core directory) should
   always be covered with unit tests.
4. Reusable Hooks: Reusable hooks should have unit tests if used more than once.
5. Code Review: The reviewer's responsibilities include ensuring that the
   behavioral contract of the user story is sufficiently covered with automated
   integration (or when relevant also unit) tests, and they must challenge the
   developer if there are gaps that can feasibly be closed. Automated test names
   ideally re-state the user story's acceptance criteria or description, and
   extend these where further edge-cases are discovered while coding. If the
   user story's acceptance criteria or description are insufficiently specified,
   the reviewer and developer must discuss this with the reporter of the story
   to confirm the actual requirements.

## Consequences

While this approach may require more effort upfront, it will save time and
effort in the long run by reducing the need for test maintenance.

### Positive

- This approach will lead to tests that are more resilient to changes in
  implementation, reducing the time and effort spent on updating tests.
- It will provide better protection against regression errors, as it tests the
  overall behavior of the system.

### Negative

- This approach may require more time and effort upfront to write comprehensive
  integration tests.
