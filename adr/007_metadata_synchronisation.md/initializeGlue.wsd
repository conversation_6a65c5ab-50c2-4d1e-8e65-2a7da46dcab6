@startuml
title drillthrough context
actor User
participant "PnlLive Report" as PnlLive
participant "@glue42" as Glue42
participant "Trades Report" as Trades
database "Redux (Trades)" as TradesRedux

group Grid
    User -> PnlLive: Open drillthrough app
    PnlLive -> PnlLive: buildDrillThroughContext
    PnlLive -> Glue42: application.start({ drillThroughContext })
    note right: Starts an instance of the app
    Trades -> Glue42: creates a new Glue42 instance
    Glue42 -> Trades: drillThroughContext
end

group InitializeGlue
  group useInitializeGlueEffect

    Glue42 -> PnlLive: creates a new Glue42 instance 
    note right: window.glue
    Glue42 -> Trades: creates a new Glue42 instance 
    note right: window.glue
    group handleDrillThroughContextUpdateEvent
    Trades -> TradesRedux: setDrillThroughViewLiveUpdateAsOfUtcMetadataOrigin
    note right: from drillThroughContext
    Trades -> TradesRedux: start/stop latestAsOfUtcListener
    end

  end
end
@enduml

// TODO: next steps break in debugger and update diagram