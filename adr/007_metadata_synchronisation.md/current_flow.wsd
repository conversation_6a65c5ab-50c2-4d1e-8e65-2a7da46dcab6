@startuml
title Current Synchronization flow
actor User
database "Redux (Trades)" as TradesRedux
database "Redux (PnlLive)" as PNLRedux
participant "PnlLive Report" as PnlLive
participant "@glue42" as Glue42
participant "Trades Report" as Trades
database "Backend API" as API

group Originating Report
  User -> PnlLive: Request Data
  PnlLive -> API: getLatestAsOfUtc
  API --> PnlLive: Return AsOf
  PnlLive -> PNLRedux: currentAsOfUtc

  group buildDrillThroughContext
    PNLRedux -> PnlLive: save currentAsOfUtc as originatingContext.reportAsOfUtc
    PnlLive -> PnlLive: save originatingContext in drillThroughContext
    PnlLive -> Glue42: publishDrillThroughContextEvent (GridRowSelected) + originatingContext
  end
  note right 
  used in publishDrillThroughContextFromRowSelectedEvent
  to get originatingContext
  and openDrillThroughApp 
  (context menu item used in EPIC only)
  end note
  group useGridEventHandlers
    User -> PnlLive: change filter
    PnlLive -> Glue42: publishDrillThroughContextEvent (GridFilterChanged)
    Glue42 -> Trades: call handleDrillThroughContextUpdateEvent (GridFilterChanged)
    Trades -> Trades: Handling GridFilterChanged
    User -> PnlLive: select row
    PnlLive -> Glue42: publishDrillThroughContextEvent (GridRowSelected) + originatingContext
    note left: only GridRowSelected event has originatingContext
    Glue42 -> Trades: call handleDrillThroughContextUpdateEvent (GridRowSelected)
    Trades -> Trades: Handling GridRowSelected
    Trades -> TradesRedux: setOriginatingContextExceptReportAsOf
  end
  note right: called from Grid
end

group Drillthrough
  User -> Trades: click GetLatest
  TradesRedux ---> Trades: preProcessingParams.originatingContext.reportAsOfUtc (originatingReportCurrentAsOfUtc)
  TradesRedux ---> Trades: report.drillThroughViewLiveUpdateAsOfUtcMetadataOrigin
  Trades -> API: fetchLatestAsOfUtc (for originatingReportCurrentAsOfUtc)
  API --> Trades: originatingReportLatestAsOfUtc


  group useGlueChannelListenerEffect
    Trades -> Glue42: subscribe(handleDrillThroughContextUpdateEvent)
  end
  note right: called from Grid

  group useInitializeGlueEffect
    Trades -> Glue42: getGlueContext
    Glue42 --> Trades: drillThroughContext
    Trades -> TradesRedux: setDrillThroughViewLiveUpdateAsOfUtcMetadataOrigin
    Glue42 -> Trades: handleDrillThroughContextUpdateEvent (InitializeApp)
    Glue42 -> Trades: handleDrillThroughContextUpdateEvent (GridRowSelected)
    Glue42 -> Trades: handleDrillThroughContextUpdateEvent (SelectedTradingAreasChanged)
    Glue42 -> Trades: handleDrillThroughContextUpdateEvent (LiveModeChanged)
    Glue42 -> Trades: handleDrillThroughContextUpdateEvent (GridFilterChanged)
  end
  note right
    called from
    InitializeApp -> InitializeGlue
  end note
  
  group useInitializeGlueEffect
    Trades -> Trades: todo
    note left
      originating app should get metadata with its new currentAsOf
      and one of them is called Trades
      publish all metadata timestamps, and trades only reads what it needs
      (to handle channel override by other windows)
      Epic is scope of communication, so other user will not override
      --
      each report when it loads should somehow know what metadata listen for
      --
      
    end note 
  end


end
@enduml