@startuml
    participant User
    participant "Originating Report\n(e.g., PnlLive)" as PnlLive
    database "Redux (PnlLive)" as PnlLiveRedux
    participant Glue42
    database "Backend API" as API
    participant "Subscribed Sync Datac Report\n(e.g., Trades)" as Trades
    database "Redux (Trades)" as TradesRedux

    group Standalone Report (live update) mode
        User ->> PnlLive: Request Data
        PnlLive ->> API: GET /PnlLive/LatestAsOfUtc (long-polling)
        API -->> PnlLive: LatestAsOfUtc (tick)
        PnlLive ->> PnlLiveRedux: set state.currentAsOfUtc to LatestAsOfUtc
        PnlLiveRedux -->> PnlLive: currentAsOfUtc
        PnlLive ->> API: POST currentAsOfUtc to /PnlLive/GetMetadata
        note right: only used to display metadata to user
        API -->> PnlLive: GetMetadataResponse (for currentAsOfUtc)
        PnlLive ->> API: POST currentAsOfUtc to /PnlLive/GetSubset
        API -->> PnlLive: GetSubsetResponse (for currentAsOfUtc)
    end
    note right
    All reports work in this mode by default 
    (if not linked to Glue channel)
    end note

    group Publish Sync Data mode
      PnlLive ->> API: GET /ReportDetails
      API -->> PnlLive: IGetReportDetailsResponse
      note left
          {
            isOriginatingReport: true,
            ...
          }
      end note         
      PnlLive ->> API: GET /PnlLive/LatestAsOfUtc (long-polling)
      API -->> PnlLive: LatestAsOfUtc (tick)
      PnlLive ->> PnlLiveRedux: set state.currentAsOfUtc to LatestAsOfUtc
      PnlLiveRedux -->> PnlLive: currentAsOfUtc
      PnlLive ->> API: POST currentAsOfUtc to /PnlLive/GetMetadata
      API -->> PnlLive: GetMetadataResponse (for currentAsOfUtc)
      note left
      [{
         label: 'Trades'
         time: '2024-09-10T12:00:00.000Z'
         alwaysShow: true
         report: 'trades'
       }]
      end note
      PnlLive -->> Glue42: detect publish collisions\n(optional, to be refined how) 
      PnlLive -->> User: indicate publish collision\n(optional)
      PnlLive ->> Glue42: Publish metadata to active channel
      note right
      [{
         label: 'Trades'
         time: '2024-09-10T12:00:00.000Z'
         alwaysShow: true
         report: 'trades'
       }]
      end note 
      PnlLive ->> API: POST currentAsOfUtc to /PnlLive/GetSubset
      API -->> PnlLive: GetSubsetResponse (for currentAsOfUtc)
    end

    group Subscribe Sync Data mode
        User ->> Glue42: Open Subscribe Sync Data Report (Drillthrough)
        Glue42 ->> Trades: Open in active (green) channel
        Trades ->> API: GET /ReportDetails
        API -->> Trades: IGetReportDetailsResponse
        note left
           {
              isOriginatingReport: false,
              ...
           }
        end note

        group Sync Data Subscription
            Trades ->> Glue42: Subscribe to metadata updates on channel
            Glue42 -->> Trades: Receive Originating Report metadata from active channel
            note right
              [{
                label: 'Trades'
                time: '2024-09-10T12:00:00.000Z'
                alwaysShow: true
                report: 'trades'
              }]
            end note 
            note left: use time from "trades" \nmetadata as currentAsOfUtc
            Trades -> TradesRedux: set state.currentAsOfUtc to OriginatingReport.Metadata[report=='trades'].time
        end
        TradesRedux -->> Trades: currentAsOfUtc
        Trades ->> API: POST currentAsOfUtc to /Trades/GetMetadata
        note left: only used to display metadata to user
        API -->> Trades: GetMetadataResponse (for currentAsOfUtc)
        Trades ->> API: POST currentAsOfUtc to /Trades/GetSubset
        API -->> Trades: GetSubsetResponse (for currentAsOfUtc)
        Trades ->> User: Display data and indicate sync from Glue42 instead of "live update"
    end

    group Channel Change flow
        User ->> PnlLive: Choose red channel
        PnlLive ->> Glue42: Publish metadata to red channel
        User ->> Trades: Choose red channel
        Trades ->> Glue42: Subscribe to metadata on red channel
        PnlLive ->> Glue42: Publish metadata to red channel
        Glue42 ->> Trades: Receive metadata from red channel
    end
@enduml
