# Synchronisation of Metadata for Originating Report to Drillthrough Report

## Status

Accepted

## Context

In the M72UI application, we have two main report types: Originating Reports and
Drillthrough Reports. Originating Reports are the primary reports that users
interact with, while Drillthrough Reports are launched from within an
Originating Report to provide more detailed information about a specific subset
of data.

One of the key pieces of information that needs to be synchronized between
Originating Reports and Drillthrough Reports is the "asOfUtc" metadata. This
metadata represents the point in time for which the report data is being
displayed, and it is crucial for ensuring that the Drillthrough Report displays
data that is consistent with the Originating Report from which it was launched.

## Decision

We have decided to implement the following flow for synchronizing the "asOfUtc"
metadata between Originating Reports and Drillthrough Reports:

1. **Originating Report**:

   - When the user requests data for an Originating Report, the Originating
     Report component (e.g., PnlLive) fetches report details and latest
     "asOfUtc" value from the Backend API (response from LatestAsOf endpoint).
   - Report details allow to discover if the report is an Originating Report.
   - The Originating Report component then requests the metadata from the
     Backend API, which includes timestamps of other reports that are synchronized with the Originating Report. These timestamps can be used as the "asOfUtc" value for backend requests of these other reports.
   - The Originating Report component publishes the metadata to the Glue42
     channels. Metadata includes a key representing the Drillthrough Report
     (e.g., "Trades").

2. **Drillthrough Report**:

   - When the user launches a Drillthrough Report from within an Originating
     Report, it discovers from the report details response that it is not an
     Originating Report (so it's a Drillthrough Report).
   - The Drillthrough Report component (e.g., Trades) subscribes to metadata
     updates on the Glue42 channels.
   - The Drillthrough Report component listens for metadata updates that include
     the key representing the Drillthrough Report (e.g., "Trades").
   - When a metadata update with the relevant key is received, the Drillthrough
     Report component updates its internal state with the new "asOfUtc" value.
   - The Drillthrough Report component then requests a subset of data from the
     Backend API, including the updated "asOfUtc" value.

3. **Channel Management**:

   - When opening a Drillthrough Report, Glue42 will open the application in the
     active channel. If no channel is selected - next free channel should be
     used as active one.
   - Instead of publishing preprocessing parameters in the context, we will
     publish and retrieve them from the channel every time the active channel
     changes.
   - If an application is in subscription mode, live updates should be disabled,
     and the application should rely on the synchronization mechanism. The user
     should be informed that live updates are coming from Glue42.

4. **Use Cases**:
   - Default: Standalone application
   - Windows are opened from Epic, and the user links them with a Glue42 channel
   - User opens a Drillthrough Report from the context menu, resulting in
     automatic linking to a suitable channel.

This approach ensures that the Drillthrough Report always displays data that is
consistent with the "asOfUtc" value from the Originating Report from which it
was launched.

## Consequences

### Advantages

- Ensures data consistency between Originating Reports and Drillthrough Reports.
- Utilizes the existing Glue42 channels for efficient communication between
  components.
- Separates concerns by having the Originating Report component handle metadata
  publication and the Drillthrough Report component handle metadata subscription
  and data retrieval.
- It allows for real-time updates to the metadata, which can improve the
  responsiveness of the system.
- Provides flexibility for different use cases (standalone, linked windows,
  automatic linking).
- Improves user experience by automatically managing channels and clearly
  indicating the source of updates.

### Disadvantages

- It relies on Glue42 to reliably broadcast metadata updates. If Glue42 fails or
  becomes slow, it could impact the synchronization process.
- Potential performance implications if the metadata updates are frequent or if
  there are many subscribers to the Glue42 channels.
- May require additional user training to understand the implications of
  different modes (standalone vs. linked) and channel management.

### Diagram

Refer to `proposed_flow.wsd` ![Proposed Flow](proposed_flow.png)
