# ADR-005: Replacing RTK Queries with Axios for All API Calls

## Context

In our current system, we are using Redux Toolkit (RTK) queries for handling API
calls. However, we've experienced issues related to state changes and component
rerendering, specifically in the `src/core/services/epic/presetsStorage.ts`
file. These issues have led to constant reinitialization of functionality
related to presets, which has negatively impacted the application's performance
and maintainability.

Furthermore, we've identified several concerns regarding the use of RTK queries:

1. Our use case involves delegating calls to external libraries like Presets and
   agGrid. This is not what RTK queries were designed for (it is better suited
   for use with RTK auto-generated hooks in React components).
2. RTK queries cache data in Redux, causing the Redux store to grow
   significantly. This has resulted in performance issues with Redux dev tools,
   making debugging more difficult.
3. There is a confirmed issue of double caching, as agGrid may also cache
   responses from `getSubset`, leading to increased memory usage.

Due to these issues, we are considering replacing RTK queries with Axios for all
API calls.

## Decision

We have decided to replace RTK queries with Axios for all API calls. This will
provide us with more control over our API requests and simplify the system.

## Consequences

The main advantage of this approach is that it addresses the issues we've been
facing with RTK queries, such as unnecessary state changes, component
rerendering, and excessive caching. It also simplifies the system, providing us
with more control over our API requests.

However, there are a few trade-offs to consider:

1. We will lose the automatic caching and normalization features provided by RTK
   queries. However, given our specific use case and the issues we've faced with
   caching, this is an acceptable trade-off.
2. Implementing Axios calls requires more manual setup compared to using RTK
   queries. This could potentially increase the complexity of our codebase, but
   we believe the benefits of improved control and performance outweigh this
   cost.
3. This change will require refactoring of the existing codebase where RTK
   queries are currently being used. This will require additional development
   time and resources.

Overall, we believe that this change will improve the performance,
maintainability, and scalability of our application.
