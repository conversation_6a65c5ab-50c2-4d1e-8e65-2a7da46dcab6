# Use react-testing-library with Vitest

## Status

Accepted

## Context

We are building a new web application using JavaScript, npm, TypeScript, Yarn,
and React. We need to decide on a testing library and test runner to use for the
project.

## Decision

We have decided to use `react-testing-library` as our testing library and
`<PERSON><PERSON><PERSON>` as our test runner. `react-testing-library` is a lightweight and simple
testing library that encourages testing React components in a way that resembles
how users interact with the application. We chose `react-testing-library` over
other testing libraries like `Enzyme` because it provides a more user-centric
approach to testing, and it is easier to use with TypeScript.

We chose `Vitest` as our test runner because it supports TypeScript out of the
box and is over 10x faster than Jest, while it has the same syntax. This will
allow us to write and run tests more quickly and efficiently.

## Consequences

The benefits of using `react-testing-library` with `Vitest` include:

- Lightweight and simple testing library
- Encourages testing React components in a way that resembles how users interact
  with the application
- Easier to use with TypeScript than other testing libraries like `Enzyme`
- Good documentation and community support
- Fast test runner with TypeScript support

## Alternatives

We considered the following alternatives:

- `Enzyme`: `Enzyme` is a popular testing library for React components. However,
  we found that `react-testing-library` provides a more user-centric approach to
  testing, and it is easier to use with TypeScript.
- `Jest`: `Jest` is a popular test runner for JavaScript and TypeScript
  applications. However, we chose `Vitest` because it is over 10x faster than
  Jest, while it has the same syntax.

We chose `react-testing-library` with `Vitest` over these alternatives because
it provides a lightweight and simple testing library with a fast test runner
that supports TypeScript out of the box.

## Related ADRs

None.

## References

- [react-testing-library documentation](https://testing-library.com/docs/react-testing-library/intro/)
- [Vitest documentation](https://vitest.netlify.app/)
