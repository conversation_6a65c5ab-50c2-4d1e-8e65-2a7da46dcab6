# Use plain React with Vite

## Status

Accepted

## Context

We are building a new web application using JavaScript, npm, TypeScript, Yarn,
and React. We need to decide on a build tool and framework to use for the
project.

## Decision

We have decided to use plain React with Vite as our build tool. Vite is a fast
and lightweight build tool that is optimized for modern web development. It
provides a fast development server with hot module replacement, and it supports
TypeScript out of the box. We chose plain React over other frameworks like
Next.js because we do not need most of their features, and we want to keep our
application as simple as possible. We also have limited experience with Angular,
and we found that some Epic core SDK components are easier to use with React
than with Angular.

## Consequences

The benefits of using plain React with Vite include:

- Fast development server with hot module replacement
- Optimized build times
- TypeScript support out of the box
- Simple and lightweight project setup
- Familiarity with <PERSON><PERSON> for the development team

The drawbacks of using plain React with Vite include:

- Lack of server-side rendering (SSR) support
- Limited features compared to other frameworks like Next.js and Angular

## Alternatives

We considered the following alternatives:

- Angular: We are migrating from POC that was written in Angular. Angular is a
  popular framework for building web applications, but we have limited
  experience with it, and we found that some SDK components are easier to use
  with <PERSON>act than with Angular.
- Next.js: Next.js is a popular framework for building server-side rendered
  React applications. However, we do not need most of its features, and we want
  to keep our application as simple as possible.
- Create React App: Create React App is a popular tool for setting up a new
  React project. However, we chose Vite because it provides faster build times
  and better TypeScript support out of the box.

We chose plain React with Vite over these alternatives because it provides a
simple and lightweight project setup with fast build times and good TypeScript
support.

## Related ADRs

None.

## References

- [Vite documentation](https://vitejs.dev/)
- [React documentation](https://reactjs.org/docs/getting-started.html)
