module.exports = {
  env: { browser: true, es2020: true },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react-hooks/recommended'
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: { ecmaVersion: 'latest', sourceType: 'module' },
  plugins: ['react-refresh'],
  rules: {
    'react-refresh/only-export-components': 'warn',
    '@typescript-eslint/ban-ts-comment': 'warn',
    // We have a lot of cases where we manually handle dependencies
    // Please, don't enable it again
    'react-hooks/exhaustive-deps': 'off',
    '@typescript-eslint/no-explicit-any': 'off', // Disable explicit-any error
    '@typescript-eslint/explicit-module-boundary-types': 'off' // Disable implicit any error for function return types
  }
}
